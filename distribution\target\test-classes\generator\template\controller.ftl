package ${basePackage}.web;
import ${basePackage}.core.Result;
import ${basePackage}.core.ResultGenerator;
import ${basePackage}.model.${modelNameUpperCamel};
import ${basePackage}.service.${modelNameUpperCamel}Service;

import ${basePackage}.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.util.List;

/**
* Created by ${author} on ${date}.
*/
@Api(tags = "${modelNameLowerCamel}管理")
@RestController
@RequestMapping("${baseRequestMapping}")
public class ${modelNameUpperCamel}Controller {

	private static Logger log = LoggerFactory.getLogger(${modelNameUpperCamel}Controller.class);

    @Resource
    private ${modelNameUpperCamel}Service ${modelNameLowerCamel}Service;

    @PostMapping("/add")
	@ApiOperation(value = "${modelNameLowerCamel}新增",httpMethod = "POST")
    public Result add(@RequestBody ${modelNameUpperCamel} ${modelNameLowerCamel}) {
    	if(${modelNameLowerCamel} == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	try {
    //		${modelNameLowerCamel}.setCreateTime(new Date());
    //		${modelNameLowerCamel}.setCreateUserId(userId);
    		${modelNameLowerCamel}Service.save(${modelNameLowerCamel});
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success();
    }

    @RequestMapping("/delete")
	@ApiOperation(value = "${modelNameLowerCamel}删除",httpMethod = "GET")
    public Result delete(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	try {
    		${modelNameLowerCamel}Service.deleteById(id);
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @PostMapping("/update")
	@ApiOperation(value = "${modelNameLowerCamel}更新",httpMethod = "POST")
    public Result update(@RequestBody ${modelNameUpperCamel} ${modelNameLowerCamel}) {
    	if(${modelNameLowerCamel} == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	if(${modelNameLowerCamel}.getId() == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	try {
    //		${modelNameLowerCamel}.setUpdateTime(new Date());
    //		${modelNameLowerCamel}.setUpdateUserId(userId);
    		${modelNameLowerCamel}Service.update(${modelNameLowerCamel});
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @RequestMapping("/detail")
	@ApiOperation(value = "${modelNameLowerCamel}获取详情",httpMethod = "GET")
    public Result<${modelNameUpperCamel}> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	${modelNameUpperCamel} ${modelNameLowerCamel} = null;
    	try {
    		${modelNameLowerCamel} = ${modelNameLowerCamel}Service.findById(id);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success(${modelNameLowerCamel});
    }

    @RequestMapping("/list")
	@ApiOperation(value = "${modelNameLowerCamel}获取列表",httpMethod = "POST")
    public Result<List<${modelNameUpperCamel}>> list(@RequestBody ${modelNameUpperCamel} ${modelNameLowerCamel}) {

 //       PageHelper.startPage(page, size);
        
        Condition condition = new Condition(${modelNameLowerCamel}.getClass());
        Criteria criteria = condition.createCriteria();
//        criteria.andEqualTo("name", city.getName());
		PageInfo pageInfo = null;
		try {
    		 List<${modelNameUpperCamel}> list = ${modelNameLowerCamel}Service.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
