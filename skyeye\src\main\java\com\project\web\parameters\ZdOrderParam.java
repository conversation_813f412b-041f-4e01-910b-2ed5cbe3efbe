package com.project.web.parameters;

import com.alibaba.fastjson.JSONObject;
import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 
 */
@Data
@ApiModel("转单订单参数")
public class ZdOrderParam extends BaseBeen {

    @ApiModelProperty(name="carTypes",value = "车类型")
    private List<Integer> carTypes;

    @ApiModelProperty(name="startTime",value = "开始时间")
    private String startTime;

    @ApiModelProperty(name="orderNo",value = "订单号")
    private String orderNo;

    @ApiModelProperty(name="endTime",value = "最后时间")
    private String endTime;

    @ApiModelProperty(name="loadCity",value = "装货城市")
    private List<String> loadCity;

    @ApiModelProperty(name="loadProvince",value = "装货省")
    private List<String> loadProvince;

    @ApiModelProperty(name="unloadProvince",value = "卸货省")
    private List<String> unloadProvince;

    @ApiModelProperty(name="unloadCity",value = "卸货城市")
    private String unloadCity;


    @ApiModelProperty(name="carLens",value = "车长")
    private List<Double> carLens;


    @ApiModelProperty(name="weightTypes",value = "重量类型")
    private List<Integer> weightTypes;

    @ApiModelProperty(name="freightTypes",value = "货物类型")
    private List<Integer> freightTypes;

    @ApiModelProperty(name="sortType",value = "排序类型 1智能排序 2时间顺序 3时间倒序 4金额顺序 5金额倒序 ")
    private Integer sortType;
    @ApiModelProperty(name="status",value = "状态")
    private Integer status;

    @ApiModelProperty(name="userId",value = "接单用户id")
    private Integer userId;

    @ApiModelProperty(name="createUserId",value = "创建用户id")
    private Integer createUserId;

    @ApiModelProperty(name="useType",value = "用车类型 1整车 2零担")
    private Integer useType;

    @ApiModelProperty(name="feeType",value = "费用类型 1一口价 2电议")
    private Integer feeType;


    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
