package com.project.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.project.core.ResultCode;
import com.project.dao.OrderMapper;
import com.project.exception.ServiceException;
import com.project.model.*;
import com.project.service.*;
import com.project.core.AbstractService;
import com.project.web.parameters.OrderParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;


/**
 * Created by CodeGenerator on 2025/04/25.
 */
@Slf4j
@Service
@Transactional
public class OrderServiceImpl extends AbstractService<Order> implements OrderService {
    @Resource
    private OrderMapper cOrderMapper;

    @Resource
    private ProjectOrderService projectOrderService;

    @Resource
    private OrderService orderService;



    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectTeamService projectTeamService;


    @Resource
    private UserService userService;

    @Resource
    private GoodsService goodsService;

    @Resource
    private ProjectAmountService projectAmountService;

    @Override
    public void paySuccess(Integer id) {
        Order o = findById(id);
        User u = userService.findById(o.getUserId());
        Goods goods = goodsService.findById(o.getGoodsId());
        if (StrUtil.isNotBlank(goods.getProjectIds())){
            List<Project> projects = projectService.findByIds(goods.getProjectIds());
            if (CollUtil.isNotEmpty(projects)){
                for (Project project :projects){
                    if (project.getType() == 2){
                        Condition c = new Condition(ProjectAmount.class);
                        c.createCriteria().andEqualTo("userId",u .getId())
                                .andEqualTo("projectId",project.getId());
                        List<ProjectAmount> pas = projectAmountService.findByCondition(c);
                        if (CollUtil.isNotEmpty(pas)){
                            ProjectAmount pa =  pas.get(0);
                            if (pa.getActivityAmount().compareTo(pa.getActivityLimit())<0){
                                c = new Condition(Order.class);
                                c.createCriteria().andEqualTo("userId",o.getUserId())
                                        .andEqualTo("type",1)
                                        .andEqualTo("goodsId",o.getGoodsId())
                                        .andEqualTo("status",2);
                                int i = countByCondition(c);
                                if (i>0){
                                    throw new ServiceException(ResultCode.ORDER_IS_HAVE);
                                }
                            } else {
                                projectAmountService.update(ProjectAmount.builder().id(pa.getId())
                                        .activityOutput(BigDecimal.ZERO).build());
                            }
                        }
                    }
                }
            }
        }

        update(Order.builder().id(id).status(2).updateTime(DateUtil.date()).build());

        if (StrUtil.isNotBlank(goods.getProjectIds())){
            List<Project> projects = projectService.findByIds(goods.getProjectIds());
            if (CollUtil.isNotEmpty(projects)){
                for (Project p : projects){
                    createProject(p,o);
                }
            }
        }
    }

    @Override
    public Order createOrder(OrderParam param, User tokenUser, Goods goods, String orderNo, BigDecimal amount) {

        Order order = new Order();
        order.setGoodsId(param.getGoodsId());
        order.setUserId(tokenUser.getId());
        order.setName(goods.getName());
        order.setAmount(goods.getAmount());
        order.setCreateTime(DateUtil.date());
        order.setType(goods.getType());
        order.setIcon(goods.getPic());
        order.setPid(tokenUser.getPid());
        order.setSettleStatus(1);
        order.setStatus(1);
        order.setOrderNo(orderNo);
        order.setScore(goods.getScore());
        order.setScoreAmount(goods.getScoreAmount());
        order.setScoreType(param.getScoreType());
        order.setAddressId(param.getAddressId());
        order.setDeliverStatus(1);
        orderService.save(order);
        if (StrUtil.isNotEmpty(goods.getProjectIds())){
            List<Project> projects = projectService.findByIds(goods.getProjectIds());
            if (CollUtil.isNotEmpty(projects)){
                for (Project p:projects){
                    if (p.getType() == 1){
                        ProjectOrder po = new ProjectOrder();
                        po.setAmount(order.getAmount());
                        po.setName(order.getName());
                        po.setUserId(order.getUserId());
                        po.setCommission(order.getAmount().multiply(p.getAmount()));
                        po.setType(1);
                        po.setStatus(1);
                        po.setCreateUser(tokenUser.getName());
                        po.setProjectId(p.getId());
                        po.setCreateTime(DateUtil.date());
                        po.setPid(tokenUser.getPid());
                        po.setCompanyId(order.getCompanyId());
                        po.setOrderId(order.getId());
                        po.setOrderNo(order.getOrderNo());
                        projectOrderService.save(po);
                    }
                }
            }
        }
        if (param.getScoreType() == 2){
            if (amount.compareTo(BigDecimal.ZERO) <= 0){
                //订单抵扣后金额小于0
                orderService.paySuccess(order.getId());
                order.setStatus(2);
                return order;
            }
        }
        return order;
    }

    @Override
    public Integer pay(Integer id) {
        Order order = findById(id);
        User user = userService.findById(order.getUserId());
        Goods goods = goodsService.findById(order.getGoodsId());
        if (order.getScoreType() == 2){
            Boolean deal = userService.deal(user.getId(), 4, (byte) 1, goods.getScore().negate(), Long.valueOf(order.getId()));
            //抵扣成功
            if (deal){
                if (goods.getAmount().subtract(goods.getScoreAmount()).compareTo(BigDecimal.ZERO) <= 0){
                    //订单抵扣后金额小于0
                    orderService.paySuccess(order.getId());
                    return 2;
                }
            }
        }
        return  1;
    }

    private void createProject(Project project, Order o) {
        //佣金订单状态
        Condition c = new Condition(ProjectOrder.class);
        c.createCriteria() .andEqualTo("orderId",o.getId());
        List<ProjectOrder> pos = projectOrderService.findByCondition(c);
        ProjectOrder order = null;

        // 直推奖励
        if (CollUtil.isNotEmpty(pos)){
            order = pos.get(0);
            if ( order.getStatus()==1){
                projectOrderService.update(ProjectOrder.builder().id(order.getId()).status(2).build());
            }
        }
        if (project.getType() == 1){
            if (project != null && project.getStatus() == 1){
                c = new Condition(Order.class);
                c.createCriteria().andEqualTo("goodsId",o.getGoodsId())
                        .andEqualTo("status",2)
                        .andEqualTo("userId",o.getPid());
                List<Order> os = orderService.findByCondition(c);
                if (CollUtil.isNotEmpty(os) && order != null){
                    if (!userService.deal(o.getPid(),1,(byte)1,order.getCommission(),Long.valueOf(order.getUserId()))){
                        log.error("paySuccess 添加佣金失败");
                    }
                }
            }
        } else  if (project.getType() == 2){
            c = new Condition(ProjectAmount.class);
            c.createCriteria().andEqualTo("userId",o.getUserId())
                            .andEqualTo("projectId",project.getId());
            List<ProjectAmount> pas = projectAmountService.findByCondition(c);
            if (CollUtil.isNotEmpty(pas)) {
                //新增上限
                projectAmountService.deal(o.getUserId(),project.getId(),5,(byte)3,o.getAmount().multiply(BigDecimal.valueOf(2)),Long.valueOf(o.getId()));
                //新增用户每日产出
                projectAmountService.deal(o.getUserId(),project.getId(),10,(byte)5,project.getAmount3(),Long.valueOf(o.getId()));
            } else {
                //初始化 上限 、 产出
                projectAmountService.save(ProjectAmount.builder()
                        .projectId(project.getId()).activityAmount(BigDecimal.ZERO)
                        .activityLimit(o.getAmount().multiply(BigDecimal.valueOf(2)))
                        .activityNum(0)
                        .activityOutput(project.getAmount3())
                        .userId(o.getUserId())
                        .createTime(DateUtil.date())
                        .companyId(o.getCompanyId())
                        .build());
            }

            // 成团添加席位
            if (project != null && project.getStatus() == 1){
                //检查自己的席位
                buildSelfTeam(o.getUserId(),project);
                //检查父亲的席位
                if (o.getPid() != null){
                    buildTeam(o.getUserId(),o,project);
                }
            }
        }
    }

    private void buildSelfTeam(Integer userId, Project project) {
        User u = userService.findById(userId);
        Condition c = new Condition(ProjectTeam.class);
        c.createCriteria().andEqualTo("userId",userId)
                .andEqualTo("projectId",project.getId());
        List<ProjectTeam> teams = projectTeamService.findByCondition(c);
        if (u != null){
            //不允许复购 所以不做已有团队的操作
            if (CollUtil.isEmpty(teams)){
                projectTeamService.save(ProjectTeam.builder()
                        .companyId(u.getCompanyId())
                        .createTime(DateUtil.date())
                        .userId(u.getId())
                        .icon(u.getIcon())
                        .memberUserId(u.getId())
                        .name(u.getName())
                        .projectId(project.getId())
                        .location(1)
                        .build());
            }
        }
    }

    /**
     * 构建父亲席位
     * @param userId
     * @param order
     * @param project
     */
    private void buildTeam(Integer userId, Order order,Project project) {
        //订单用户
        User u = userService.findById(userId);
        if (u == null){
            log.error("用户未找到");
            return;
        }

        if (u.getPid() == null){
            log.error("用户没有pid2 name:"+ u.getName()+" userId"+u.getId());
            return;
        }
        Condition c = new Condition(ProjectTeam.class);
        c.createCriteria().andEqualTo("userId",u.getPid())
                .andEqualTo("projectId",project.getId());
        List<ProjectTeam> teams = projectTeamService.findByCondition(c);
        HashMap<String,ProjectTeam> map = new HashMap<>();
        if (CollUtil.isEmpty(teams)){
            log.error("pid 没有成团记录 pid:"+u.getPid()+" project.getId()"+project.getId());
            return;
        }
        for (ProjectTeam t:teams) {
            map.put(""+t.getLocation(),t);
        }
        int location = selectLocation(map);
        if (location > 1){
            projectTeamService.save(ProjectTeam.builder()
                    .companyId(u.getCompanyId())
                    .createTime(DateUtil.date())
                    .userId(u.getPid())
                    .icon(u.getIcon())
                    .memberUserId(u.getId())
                    .name(u.getName())
                    .projectId(project.getId())
                    .location(location)
                    .build());
            if (location <= 3){
                    //父亲在爷爷下的席位
                    buildYyTeam(order,project,u,location);
                
                    //父亲在兄弟下的席位
                    buildXdTeam2(order,project,u,location);
                   
            } else {
                // 将送给父亲的席位 加到兄弟 下
                ProjectTeam temp = map.get("2");
                if (location >5){
                    temp = map.get("3");
                }
                buildXdTeam(temp.getMemberUserId(),location,u,project);
            }

        }
        if (teams.size()>=6){
            successTram(u.getPid(),order,project);
        }
    }

    /**
     * 构建 父亲在兄弟下的席位
     * @param order
     * @param project
     * @param u
     * @param location
     */
    private void buildXdTeam2(Order order, Project project, User u, int location) {
        User p = userService.findById(u.getPid());
        if (p == null){
            log.error("推荐用户未找到");
            return;
        }

        Condition c = new Condition(ProjectTeam.class);
        c.createCriteria().andEqualTo("memberUserId",p.getId())
                .andNotIn("userId",CollUtil.newArrayList(p.getId(),p.getPid()))

                .andEqualTo("projectId",project.getId());
        List<ProjectTeam> teams2 = projectTeamService.findByCondition(c);
        if (CollUtil.isNotEmpty(teams2)){
            int location2 = 0;
            ProjectTeam xp = teams2.get(teams2.size()-1);
            try{
                if (xp.getLocation() == 2){
                    if (location == 2){
                        location2 = 4;
                    } else  if (location == 3){
                        location2 = 5;
                    }
                } else if (xp.getLocation() == 3){
                    if (location == 2){
                        location2 = 6;
                    } else  if (location == 3){
                        location2 = 7;
                    }
                }
                if (location2 != 0 ){
                    projectTeamService.save(ProjectTeam.builder()
                            .companyId(u.getCompanyId())
                            .createTime(DateUtil.date())
                            .userId(xp.getUserId())
                            .icon(u.getIcon())
                            .memberUserId(u.getId())
                            .name(u.getName())
                            .projectId(project.getId())
                            .location(location2)
                            .build());
                }
            }catch (Exception e){
                log.error("新增兄弟下父亲席位异常",e);
            } finally {
                //检查兄弟是否满席位
                Condition c2 = new Condition(ProjectTeam.class);
                c2.createCriteria().andEqualTo("userId",xp.getUserId())

                        .andEqualTo("projectId",project.getId());
                List<ProjectTeam> teams = projectTeamService.findByCondition(c2);
                if (CollUtil.isNotEmpty(teams) && teams.size() >= 7){
                    successTram(xp.getUserId(),order,project);
                }
            }

        }
        
    }

    /**
     * 构建爷爷席位
     * @param order
     * @param project
     * @param u
     * @param location
     */
    private void buildYyTeam(Order order,Project project,User u,Integer location) {
        User p = userService.findById(u.getPid());
        if (p == null){
            log.error("推荐用户未找到");
            return;
        }
        if (p.getPid() == null){
            log.error("推荐用户没有pid name:"+ p.getName()+" userId"+p.getId());
            return;
        }
        try{
            Condition c2 = new Condition(ProjectTeam.class);
            c2.createCriteria().andEqualTo("userId",p.getPid())
                    .andEqualTo("memberUserId",p.getId())
                    .andEqualTo("projectId",project.getId());
            List<ProjectTeam> teams2 = projectTeamService.findByCondition(c2);
            if (CollUtil.isNotEmpty(teams2)){
                int location2 = 0;
                if (teams2.get(teams2.size()-1).getLocation() == 2){
                    if (location == 2){
                        location2 = 4;
                    } else  if (location == 3){
                        location2 = 5;
                    }
                } else if (teams2.get(teams2.size()-1).getLocation() == 3){
                    if (location == 2){
                        location2 = 6;
                    } else  if (location == 3){
                        location2 = 7;
                    }
                }
                if (location2 != 0 ){
                    projectTeamService.save(ProjectTeam.builder()
                            .companyId(u.getCompanyId())
                            .createTime(DateUtil.date())
                            .userId(p.getPid())
                            .icon(u.getIcon())
                            .memberUserId(u.getId())
                            .name(u.getName())
                            .projectId(project.getId())
                            .location(location2)
                            .build());
                }
            }
        }catch (Exception e){
            log.error("新增爷爷席位异常",e);
        } finally {
            //检查爷爷是否满席位
            Condition c2 = new Condition(ProjectTeam.class);
            c2.createCriteria().andEqualTo("userId",p.getPid())

                    .andEqualTo("projectId",project.getId());
            List<ProjectTeam> teams2 = projectTeamService.findByCondition(c2);
            if (CollUtil.isNotEmpty(teams2) && teams2.size() >= 7){
                successTram(p.getPid(),order,project);
            }
        }
    }

    /**
     * 构建兄弟席位
     * @param userId
     * @param location
     */
    private void buildXdTeam(Integer userId,Integer location,User u, Project project) {
        User xd = userService.findById(userId);
        if (xd == null){
            log.error("用户未找到");
            return;
        }
        try{
            Condition c2 = new Condition(ProjectTeam.class);
            c2.createCriteria().andEqualTo("userId",xd.getId())

                    .andEqualTo("projectId",project.getId());
            List<ProjectTeam> teams2 = projectTeamService.findByCondition(c2);
            if (CollUtil.isNotEmpty(teams2)){
                int location2 = 0;
                if (teams2.size() == 1 && location%2 == 0){
                    location2 = 2;
                } else if (teams2.size() == 2 && location%2 == 1){
                    location2 = 3;
                }
                if (location2 != 0 ){
                    projectTeamService.save(ProjectTeam.builder()
                            .companyId(xd.getCompanyId())
                            .createTime(DateUtil.date())
                            .userId(xd.getId())
                            .icon(u.getIcon())
                            .memberUserId(u.getId())
                            .name(u.getName())
                            .projectId(project.getId())
                            .location(location2)
                            .build());
                }
            }
        }catch (Exception e){
            log.error("新增兄弟席位异常",e);
        }

    }

    /**
     * 成团
     * @param userId
     * @param order
     * @param project
     */
    private void successTram(Integer userId,Order order,Project project) {
        //团长
        User user = userService.findById(userId);

        //成团 增加自己的积分
        addAmount(userId,order,project,1);
        if (user.getPid() != null){
            addAmount(user.getPid(),order,project,2);
        }
        //清空团队
        Condition c = new Condition(ProjectTeam.class);
        c.createCriteria().andEqualTo("userId",userId)

                .andEqualTo("projectId",project.getId());
        projectTeamService.deleteByCondition(c);
        //重新成为主席
        buildSelfTeam(userId, project);
        //检查父亲的席位
        if (user.getPid() != null){
            buildTeam(userId,order,project);
        }
        //增加成团数
        projectAmountService.addActivityNum(userId, project.getId());
        //不增加 每日产出
        projectAmountService.deal(userId, project.getId(), 11,(byte)5,project.getAmount3(),Long.valueOf(order.getId()));
    }

    /**
     *
     * @param userId
     * @param order
     * @param project
     * @param type
     */
    private void addAmount(Integer userId, Order order, Project project,Integer type) {
        User user = userService.findById(userId);
        List<ProjectOrder> pos = new ArrayList<>();
        ProjectAmount pa = projectAmountService.findPa(userId, project.getId());
        BigDecimal residue =  pa.getActivityLimit().subtract(pa.getActivityAmount());
        BigDecimal add = null;
        if (type == 1){
            add = project.getAmount();
            if (residue.compareTo(BigDecimal.ZERO)<=0 ){
                add = BigDecimal.ZERO;
            }
            if ( residue.compareTo(project.getAmount()) <0 ){
                add = residue;
                //重置每日产出
                projectAmountService.deal(userId,project.getId(),12,(byte)5,pa.getActivityOutput().negate(),Long.valueOf(order.getId()));
            }
        } else {
            add = project.getAmount2();
            if (residue.compareTo(BigDecimal.ZERO)<=0 ){
                add = BigDecimal.ZERO;
            }
            if (residue.compareTo(project.getAmount2()) <0 ){
                add = residue;
                //重置每日产出
                projectAmountService.deal(userId,project.getId(),12,(byte)5,pa.getActivityOutput().negate(),Long.valueOf(order.getId()));
            }
        }
        if (userService.deal(userId,type == 1? 2 :3,(byte)1,add,Long.valueOf(order.getId()))){
            //新增分佣订单
            ProjectOrder po =  ProjectOrder.builder().status(2).orderNo(order.getOrderNo())
                    .orderId(order.getId()).companyId(order.getCompanyId()).pid(user.getPid())
                    .createTime(DateUtil.date()).projectId(project.getId())
                    .type(type == 1? 2 :3).userId(userId).name(order.getName())
                    .amount(order.getAmount()).commission(add).build();
            pos.add(po);
            //增加累积活动金额
            projectAmountService.deal(userId,project.getId(),type == 1? 2 :3,(byte)2,add,Long.valueOf(order.getId()));
        } else {
            log.error("paySuccess 添加成团奖励失败userId"+userId + " add：" + add+ " orderId"+order.getId());
        }
        if (CollUtil.isNotEmpty(pos)){
            projectOrderService.save(pos);
        }
    }

    private int selectLocation(HashMap<String, ProjectTeam> map) {
        ProjectTeam projectTeam = map.get("2");
        if (projectTeam == null){
            return  2;
        }
        projectTeam = map.get("3");
        if (projectTeam == null){
            return  3;
        }
        projectTeam = map.get("4");
        if (projectTeam == null){
            return  4;
        }
        projectTeam = map.get("5");
        if (projectTeam == null){
            return  5;
        }
        projectTeam = map.get("6");
        if (projectTeam == null){
            return  6;
        }
        projectTeam = map.get("7");
        if (projectTeam == null){
            return  7;
        }
        return 1;
    }
}
