package com.project.util;



import java.security.SecureRandom;
import java.util.HashMap;
import java.util.Map;

public final class NanoIdUtil {
    public static final SecureRandom DEFAULT_NUMBER_GENERATOR = new SecureRandom();
    public static final char[] DEFAULT_ALPHABET = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ".toCharArray();
    public static final int DEFAULT_SIZE = 21;

    private NanoIdUtil() {
    }

    public static String randomNanoId() {
        return randomNanoId( DEFAULT_SIZE);
    }

    public static String randomNanoId( int size) {
        SecureRandom random = DEFAULT_NUMBER_GENERATOR;
        char[] alphabet = DEFAULT_ALPHABET;
        if (random == null) {
            throw new IllegalArgumentException("random cannot be null.");
        } else if (alphabet == null) {
            throw new IllegalArgumentException("alphabet cannot be null.");
        } else if (alphabet.length != 0 && alphabet.length < 256) {
            if (size <= 0) {
                throw new IllegalArgumentException("size must be greater than zero.");
            } else {
                int mask = (2 << (int)Math.floor(Math.log((double)(alphabet.length - 1)) / Math.log(2.0))) - 1;
                int step = (int)Math.ceil(1.6 * (double)mask * (double)size / (double)alphabet.length);
                StringBuilder idBuilder = new StringBuilder();

                while(true) {
                    byte[] bytes = new byte[step];
                    random.nextBytes(bytes);

                    for(int i = 0; i < step; ++i) {
                        int alphabetIndex = bytes[i] & mask;
                        if (alphabetIndex < alphabet.length) {
                            idBuilder.append(alphabet[alphabetIndex]);
                            if (idBuilder.length() == size) {
                                return idBuilder.toString();
                            }
                        }
                    }
                }
            }
        } else {
            throw new IllegalArgumentException("alphabet must contain between 1 and 255 symbols.");
        }
    }


    public static void main(String[] args) {
        Map<String,Integer> m = new HashMap<>();
        for(int i=0;i<10000000;i++) {

            String str =  NanoIdUtil.randomNanoId(10);
            if(!m.containsKey(str)){
                m.put(str,1);
            }else{
                m.put(str,m.get(str).intValue()+1);
            }
        }
        int ii=0;
        int iii=0;
        for(String k:m.keySet()){
//
            if(m.get(k).intValue()==1){
                ii++;
                System.out.println(k.length() + "|" + k+"-"+m.get(k).intValue());
            }

            if(m.get(k).intValue()>1){
                iii++;
                System.out.println(k.length() + "-" + k+"-"+m.get(k).intValue());
            }
        }

        System.out.println(m.size()+"-"+ii+"-"+iii);


    }
}
