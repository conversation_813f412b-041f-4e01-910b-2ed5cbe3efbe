package com.project.web.parameters;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Data
@ApiModel(value="成团成员")
public class ProjectTeamBO implements Serializable {
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 项目id
     */
    @ApiModelProperty(value="projectId项目id")
    private Integer projectId;

    /**
     * 用户id
     */
    @ApiModelProperty(value="userId用户id")
    private Integer userId;

    /**
     * 公司id
     */
    @ApiModelProperty(value="companyId公司id")
    private Integer companyId;

    /**
     * 会员用户id
     */
    @ApiModelProperty(value="memberUserId会员用户id")
    private Integer memberUserId;

    /**
     * 姓名
     */
    @ApiModelProperty(value="name姓名")
    private String name;

    /**
     * 头像
     */
    @ApiModelProperty(value="icon头像")
    private String icon;

    /**
     * 位置
     */
    @ApiModelProperty(value="location位置")
    private Integer location;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    private static final long serialVersionUID = 1L;


}