package com.project.web;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.project.core.*;
import com.project.model.*;
import com.project.service.*;
import com.project.web.parameters.LoginAccountParameters;
import com.project.web.parameters.PermissionBo;
import com.project.web.parameters.RegisterAccountParameters;
import com.project.web.parameters.StatisticsBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

/**
 * Created by CodeGenerator on 2019/11/01.
 */
@Api(tags = "统计管理")
@RestController
@RequestMapping("/statistics")
public class StatisticsController {

    private static Logger log = LoggerFactory.getLogger(StatisticsController.class);

    @Resource
    private UserService userService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private UserUtil userUtil;

//    @Resource
//    private WorkService workService;

    @Resource
    private ComplainService complainService;

//    @Resource
//    private ExamineService examineService;

    @PostMapping("/statistics")
    @ApiOperation(value = "统计 ",httpMethod = "POST")
    public Result<StatisticsBo> statistics() {
        try {
//            Condition c = new Condition(Work.class);
//            c.createCriteria().andEqualTo("status",4);
//            int finishWork = workService.countByCondition(c);
//            List<Work> workAll = workService.findAll();
//            List<Complain> complains = complainService.findAll();
//            int countComplain = complains.size();
//            c = new Condition(Complain.class);
//            c.createCriteria().andIn("status",CollUtil.newArrayList(2,3));
//            List<Complain> finshComplains = complainService.findAll();
//
//            c = new Condition(Examine.class);
//            c.createCriteria().andEqualTo("status",3);
//            List<Examine> examines = examineService.findByCondition(c);
//            int sumScore = examines.stream().mapToInt(Examine::getScore).sum();
//            return ResultGenerator.genSuccessResult(StatisticsBo.builder().countComplain(countComplain)
//                    .finishWork(finishWork).finshComplain(finshComplains.size()).sumScore(sumScore)
//                    .workNum(workAll.size()).build());
            return ResultGenerator.genSuccessResult();
        } catch (Exception e) {
            log.error("删除对象操作异常e:{}",e);
            return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
        }
    }

    @NoToken
    @NoLogin
    @GetMapping("/export/work")
    @ApiOperation(value = "导出已完成任务",httpMethod = "GET")
    public void exportWork( HttpServletResponse response) {
        List<String> rowHead = CollUtil.newArrayList("编号", "任务名称", "任务内容", "任务地址","清洁人员", "时间");
        ExcelWriter writer = ExcelUtil.getWriter();
        try{
            writer.writeHeadRow(rowHead);
//            List<List<Object>> rows = new LinkedList<>();
//            Condition c = new Condition(Work.class);
//            c.createCriteria().andEqualTo("status",4);
//            List<Work> list = workService.findByCondition(c);
//            if (CollUtil.isEmpty(list)){
//                return;
//            }
//            for (Work work:list) {
//                User user = userService.findById(work.getCleanUserId());
//                List<Object> rowA = CollUtil.newArrayList(work.getId()
//                        , work.getName()
//                        , work.getContent()
//                        , work.getAddress()
//                        , user.getName()
//                        , DateUtil.format(work.getCreateTime(),"yyyy/MM/dd HH:mm:ss")
//                );
//                rows.add(rowA);
//            }
//            writer.write(rows);
            //设置宽度自适应
            writer.setColumnWidth(-1, 22);
            //response为HttpServletResponse对象
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(("已完成任务列表").getBytes("UTF-8"), "ISO-8859-1") + ".xls");
            ServletOutputStream out = response.getOutputStream();
            //out为OutputStream，需要写出到的目标流
            writer.flush(out);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭writer，释放内存
            writer.close();
        }
    }

    @NoLogin
    @NoToken
    @GetMapping("/export/complain")
    @ApiOperation(value = "导出投诉建议",httpMethod = "GET")
    public void complain( HttpServletResponse response) {
        List<String> rowHead = CollUtil.newArrayList("编号", "投诉建议人", "投诉建议内容", "投诉建议地址","状态", "时间");
        ExcelWriter writer = ExcelUtil.getWriter();
        try{
            List<List<Object>> rows = new LinkedList<>();
            writer.writeHeadRow(rowHead);
            List<Complain> list = complainService.findAll();
            if (CollUtil.isEmpty(list)){
                return;
            }

            for (Complain complain:list) {
                User user = userService.findById(complain.getUserId());
                List<Object> rowA = CollUtil.newArrayList(complain.getId()
                        , user.getName()
                        , complain.getContent()
                        , complain.getAddress()
                        //状态 1 待确认 2 已生成任务 3已确认  4取消
                        , complain.getStatus() == 1? "未处理":complain.getStatus() == 2
                                ? "已生成清洁任务":complain.getStatus() == 3? "已处理": "已取消"
                        , DateUtil.format(complain.getCreateTime(),"yyyy/MM/dd HH:mm:ss")
                );
                rows.add(rowA);
            }
            writer.write(rows);
            //设置宽度自适应
            writer.setColumnWidth(-1, 22);
            //response为HttpServletResponse对象
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(("投诉建议列表").getBytes("UTF-8"), "ISO-8859-1") + ".xls");
            ServletOutputStream out = response.getOutputStream();
            //out为OutputStream，需要写出到的目标流
            writer.flush(out);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭writer，释放内存
            writer.close();
        }
    }

    @NoLogin
    @NoToken
    @GetMapping("/export/examines")
    @ApiOperation(value = "导出检查",httpMethod = "GET")
    public void examines( HttpServletResponse response) {
        List<String> rowHead = CollUtil.newArrayList("编号", "检查人", "待确认内容", "待确认地址","评分", "时间");
        ExcelWriter writer = ExcelUtil.getWriter();
        try{
            List<List<Object>> rows = new LinkedList<>();
            writer.writeHeadRow(rowHead);
//            Condition c = new Condition(Examine.class);
//            //状态 1 待分配 2待检查 3 待确认 4已确认
//            c.createCriteria().andEqualTo("status",4);
//            List<Examine> list = examineService.findByCondition(c);
//            if (CollUtil.isEmpty(list)){
//                return;
//            }
//
//            for (Examine examine:list) {
//                User user = userService.findById(examine.getUserId());
//                List<Object> rowA = CollUtil.newArrayList(examine.getId()
//                        , user.getName()
//                        , examine.getContent()
//                        , examine.getAddress()
//                        //状态 1 待检查 2 待确认 3已确认
//                        , examine.getScore()
//                        , DateUtil.format(examine.getCreateTime(),"yyyy/MM/dd HH:mm:ss")
//                );
//                rows.add(rowA);
//            }
//            writer.write(rows);
            //设置宽度自适应
            writer.setColumnWidth(-1, 22);
            //response为HttpServletResponse对象
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            //test.xls是弹出下载对话框的文件名，不能为中文，中文请自行编码
            response.setHeader("Content-Disposition", "attachment;filename=" + new String(("已评分检查列表").getBytes("UTF-8"), "ISO-8859-1") + ".xls");
            ServletOutputStream out = response.getOutputStream();
            //out为OutputStream，需要写出到的目标流
            writer.flush(out);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭writer，释放内存
            writer.close();
        }
    }

}
