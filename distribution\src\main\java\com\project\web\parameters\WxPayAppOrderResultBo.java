package com.project.web.parameters;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * @Author: lxk
 * @Date: 2019/12/10 14:50
 * @Version 1.0
 */
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("微信预支付返回类")
public class WxPayAppOrderResultBo implements Serializable {

    private static final long serialVersionUID = 3102531737084976255L;
    /**
     * 订单id
     */
    @ApiModelProperty(value="订单id")
    private Integer orderId;
    @ApiModelProperty(value="订单号")
    private String orderSn;

    private String sign;
    private String prepayId;
    private String partnerId;
    private String appId;
    /**
     * 由于package为java保留关键字，因此改为packageValue. 前端使用时记得要更改为package
     */
    private String packageValue;
    private String timeStamp;
    private String nonceStr;

    @ApiModelProperty(value="跳转url地址")
    private String url;

    @ApiModelProperty(value="payCode")
    private String payCode;
}
