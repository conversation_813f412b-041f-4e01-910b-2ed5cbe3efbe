package com.project.core;

public enum CacheType {

	// ----------------------- token 密钥相关 ------------------------

	/**
	 * app端密钥
	 */
	AppSecretKey,

	// ------------------------- 用户相关 -------------------------
	/**
	 * 用户登录
	 */
	UserLogin,


	ShortUrl,

	sjtcRefundCallback, received, holdSpeed,LotteryKey, customs, authCode, chess, playerTower, Ranking, RankStore, enterExplore, friendOpt,
	/**
	 * 关卡加速
	 */
	customsSpeed, PlayUrl;

	private static String keyHead = ProjectConstant.application;

	@Override
	public String toString() {
		return keyHead + "_" + name();
	};
}
