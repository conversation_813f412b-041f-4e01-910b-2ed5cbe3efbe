package com.project.configurer;

import com.github.binarywang.wxpay.config.WxPayConfig;
import com.github.binarywang.wxpay.service.WxPayService;
import com.github.binarywang.wxpay.service.impl.WxPayServiceImpl;
import com.project.core.Parameters;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019/05/05
 * 微信支付信息注入bean 中
 */
@Component
public class WeChatPayConfig {
    @Autowired
    private Parameters parameters;

    @Bean
    @ConditionalOnMissingBean
    public WxPayConfig payConfig() {
        WxPayConfig payConfig = new WxPayConfig();
        payConfig.setAppId(this.parameters.getAppId());
        payConfig.setMchId(this.parameters.getMchId());
        payConfig.setMchKey(this.parameters.getMchKey());
        payConfig.setKeyPath(this.parameters.getKeyPath());
        payConfig.setTradeType(this.parameters.getTradeType());
        payConfig.setNotifyUrl(this.parameters.getNotifyUrl());
        return payConfig;
    }
    @Bean
    public WxPayService wxPayService(WxPayConfig payConfig) {
        WxPayService wxPayService = new WxPayServiceImpl();
        wxPayService.setConfig(payConfig);
        return wxPayService;
    }
}