package com.project.web;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.model.Address;
import com.project.model.Order;
import com.project.model.ProjectOrder;
import com.project.model.User;
import com.project.service.AddressService;
import com.project.service.AreaService;
import com.project.service.OrderService;
import com.project.service.ProjectOrderService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
* Created by CodeGenerator on 2025/04/25.
*/
@Api(tags = "佣金管理")
@RestController
@RequestMapping("/project/order")
public class ProjectOrderController {

	private static Logger log = LoggerFactory.getLogger(ProjectOrderController.class);

    @Resource
    private ProjectOrderService projectOrderService;

	@Resource
	private OrderService orderService;

	@Resource
	private UserUtil userUtil;

	@Resource
	private AddressService addressService;

	@Resource
	private AreaService areaService;

    @PostMapping("/add")
	@ApiOperation(value = "projectOrder新增",httpMethod = "POST")
    public Result add(@RequestBody ProjectOrder projectOrder) {
    	if(projectOrder == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	try {
    //		projectOrder.setCreateTime(new Date());
    //		projectOrder.setCreateUserId(userId);
    		projectOrderService.save(projectOrder);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success();
    }

    @RequestMapping("/delete")
	@ApiOperation(value = "projectOrder删除",httpMethod = "GET")
    public Result delete(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	try {
    		projectOrderService.deleteById(id);
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @PostMapping("/update")
	@ApiOperation(value = "projectOrder更新",httpMethod = "POST")
    public Result update(@RequestBody ProjectOrder projectOrder) {
    	if(projectOrder == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	if(projectOrder.getId() == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	try {
    //		projectOrder.setUpdateTime(new Date());
    //		projectOrder.setUpdateUserId(userId);
    		projectOrderService.update(projectOrder);
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @RequestMapping("/detail")
	@ApiOperation(value = "projectOrder获取详情",httpMethod = "GET")
    public Result<ProjectOrder> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	ProjectOrder projectOrder = null;
    	try {
    		projectOrder = projectOrderService.findById(id);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success(projectOrder);
    }

    @RequestMapping("/list")
	@ApiOperation(value = "projectOrder获取列表",httpMethod = "POST")
    public Result<List<ProjectOrder>> list(@RequestBody ProjectOrder projectOrder, HttpServletRequest request) {
		//校验权限
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}

        PageHelper.startPage(projectOrder.getPage(), projectOrder.getSize());
        
        Condition condition = new Condition(projectOrder.getClass());
        Criteria criteria = condition.createCriteria();
		criteria.andEqualTo("status", 2 );
		criteria.andEqualTo("pid", tokenUser.getId());
		criteria.andIsNotNull("pid");
		PageInfo pageInfo = null;
		try {
    		 List<ProjectOrder> list = projectOrderService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
			pageInfo.setList(list.stream().map( e -> {
				Order order = orderService.findById(e.getOrderId());
				if (order.getAddressId() != null){
					Address address = addressService.findById(order.getAddressId());
					if (address!=null){
						e.setAreaName(areaService.getAreaName(address.getAreaCode()));
					}
				}
				e.setPic(order.getIcon());
				return e;
			}).collect(Collectors.toList()));
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }

	@RequestMapping("/list2")
	@ApiOperation(value = "projectOrder获取列表",httpMethod = "POST")
	public Result<List<ProjectOrder>> list2(@RequestBody ProjectOrder projectOrder, HttpServletRequest request) {
		//校验权限
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}

		PageHelper.startPage(projectOrder.getPage(), projectOrder.getSize());

		Condition condition = new Condition(projectOrder.getClass());
		Criteria criteria = condition.createCriteria();
		if (projectOrder.getStatus() != null){
			criteria.andEqualTo("status", projectOrder.getStatus() );
		}
		if (projectOrder.getUserId() != null){
			criteria.andEqualTo("userId", projectOrder.getUserId());
		}
		PageInfo pageInfo = null;
		try {
			List<ProjectOrder> list = projectOrderService.findByCondition(condition);
			pageInfo = new PageInfo(list);
			pageInfo.setList(list.stream().map( e -> {
				Order order = orderService.findById(e.getOrderId());
				if (order != null){
					if (order.getAddressId() != null){
						Address address = addressService.findById(order.getAddressId());
						if (address!=null){
							e.setAreaName(areaService.getAreaName(address.getAreaCode()));
						}
					}
					e.setPic(order.getIcon());
				}
				return e;
			}).collect(Collectors.toList()));
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success(pageInfo);
	}
}
