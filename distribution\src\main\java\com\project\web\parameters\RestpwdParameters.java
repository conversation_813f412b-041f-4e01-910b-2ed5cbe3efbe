package com.project.web.parameters;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 账号登录参数
 */
@ApiModel("重置交易密码参数")
public class RestpwdParameters {

    @ApiModelProperty(name="userId",value = "用户id",required=true)
    private Integer userId;

    @ApiModelProperty(name="pwd",value = "登录密码",required=true)
    private String pwd;

    @ApiModelProperty(value="地址")
    private String address;

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getUserId() {
        return userId;
    }

    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
