package com.project.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Data
@ApiModel(value="com.project.model.ProjectItemNode")
@Table(name = "b_project_item_node")
public class ProjectItemNode implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 项目id
     */
    @Column(name = "project_id")
    @ApiModelProperty(value="projectId项目id")
    private Integer projectId;

    /**
     * 项目明细id
     */
    @Column(name = "project_item_id")
    @ApiModelProperty(value="projectItemId项目明细id")
    private Integer projectItemId;

    /**
     * 节点名称
     */
    @ApiModelProperty(value="name节点名称")
    private String name;

    /**
     * 进度值
     */
    @ApiModelProperty(value="progress进度值")
    private Integer progress;

    /**
     * 耗时
     */
    @Column(name = "elapsed_time")
    @ApiModelProperty(value="elapsedTime耗时")
    private Integer elapsedTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取项目id
     *
     * @return project_id - 项目id
     */
    public Integer getProjectId() {
        return projectId;
    }

    /**
     * 设置项目id
     *
     * @param projectId 项目id
     */
    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    /**
     * 获取项目明细id
     *
     * @return project_item_id - 项目明细id
     */
    public Integer getProjectItemId() {
        return projectItemId;
    }

    /**
     * 设置项目明细id
     *
     * @param projectItemId 项目明细id
     */
    public void setProjectItemId(Integer projectItemId) {
        this.projectItemId = projectItemId;
    }



    /**
     * 获取进度值
     *
     * @return progress - 进度值
     */
    public Integer getProgress() {
        return progress;
    }

    /**
     * 设置进度值
     *
     * @param progress 进度值
     */
    public void setProgress(Integer progress) {
        this.progress = progress;
    }

    /**
     * 获取耗时
     *
     * @return elapsed_time - 耗时
     */
    public Integer getElapsedTime() {
        return elapsedTime;
    }

    /**
     * 设置耗时
     *
     * @param elapsedTime 耗时
     */
    public void setElapsedTime(Integer elapsedTime) {
        this.elapsedTime = elapsedTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}