package com.project.web.parameters;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Data
@ApiModel(value="会员")
public class ProjectItemMemberBO implements Serializable {
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 项目id
     */
    @ApiModelProperty(value="projectId项目id")
    private Integer projectId;

    /**
     * 项目id
     */
    @ApiModelProperty(value="projectItemId项目id")
    private Integer projectItemId;

    /**
     * 用户id
     */
    @ApiModelProperty(value="userId用户id")
    private Integer userId;

    @ApiModelProperty(value="用户名称")
    private String name;

    @ApiModelProperty(value="用户头像")
    private String icon;


}