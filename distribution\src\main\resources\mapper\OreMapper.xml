<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.OreMapper">
  <resultMap id="BaseResultMap" type="com.project.model.Ore">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="output" jdbcType="DECIMAL" property="output" />
    <result column="accumulate_output" jdbcType="DECIMAL" property="accumulateOutput" />
    <result column="total" jdbcType="DECIMAL" property="total" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
  </resultMap>
</mapper>