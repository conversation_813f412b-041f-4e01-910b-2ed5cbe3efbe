package com.project.web.open.para;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 */
@Data
@ApiModel("账号开放参数")
public class OpenBaseParam {

    @ApiModelProperty(name="companyId",value = "公司id",required=true)
    private Integer companyId;

    @ApiModelProperty(value="外部父用户id")
    private String outPid;
    @ApiModelProperty(name="outUserId",value = "外部用户id",required=true)
    private String outUserId;

    @ApiModelProperty(name="sign",value = "签名",required=true)
    private String sign;

    @ApiModelProperty(value="时间戳",required=true)
    private Long timestamp;

    @ApiModelProperty(value="电话号码")
    private String phoneNo;


    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
