package com.project.service.impl;

import com.project.dao.ProjectItemRecordMapper;
import com.project.model.ProjectItemRecord;
import com.project.service.ProjectItemRecordService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/01/26.
 */
@Service
@Transactional
public class ProjectItemRecordServiceImpl extends AbstractService<ProjectItemRecord> implements ProjectItemRecordService {
    @Resource
    private ProjectItemRecordMapper bProjectItemRecordMapper;

}
