package com.project.service.impl;

import com.project.dao.NoticeMapper;
import com.project.model.Notice;
import com.project.service.NoticeService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2024/04/08.
 */
@Service
@Transactional
public class NoticeServiceImpl extends AbstractService<Notice> implements NoticeService {
    @Resource
    private NoticeMapper bNoticeMapper;

}
