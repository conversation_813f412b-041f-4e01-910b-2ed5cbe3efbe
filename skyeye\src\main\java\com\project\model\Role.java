package com.project.model;

import com.project.core.BaseBeen;
import com.project.web.parameters.PermissionBo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.*;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="com.qimingxing.journey.model.Role")
@Table(name = "b_role")
public class Role extends BaseBeen implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    /**
     * 保留
     */
    @ApiModelProperty(value="type保留")
    private String type;

    /**
     * 状态 0启用 1禁用
     */
    @ApiModelProperty(value="status状态 0启用 1禁用")
    private String status;

    /**
     * 创建人
     */
    @Column(name = "create_user_id")
    @ApiModelProperty(value="createUserId创建人")
    private String createUserId;

    /**
     * 更新人
     */
    @Column(name = "update_user_id")
    @ApiModelProperty(value="updateUserId更新人")
    private String updateUserId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;


    @Transient
    private String permissionIds;

    @Transient
    private List<PermissionBo> permissions;

    public String getPermissionIds() {
        return permissionIds;
    }

    public void setPermissionIds(String permissionIds) {
        this.permissionIds = permissionIds;
    }

    public List<PermissionBo> getPermissions() {
        return permissions;
    }

    public void setPermissions(List<PermissionBo> permissions) {
        this.permissions = permissions;
    }


    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取名称
     *
     * @return name - 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取保留
     *
     * @return type - 保留
     */
    public String getType() {
        return type;
    }

    /**
     * 设置保留
     *
     * @param type 保留
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 获取状态 0启用 1禁用
     *
     * @return status - 状态 0启用 1禁用
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置状态 0启用 1禁用
     *
     * @param status 状态 0启用 1禁用
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 获取创建人
     *
     * @return create_user_id - 创建人
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * 设置创建人
     *
     * @param createUserId 创建人
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 获取更新人
     *
     * @return update_user_id - 更新人
     */
    public String getUpdateUserId() {
        return updateUserId;
    }

    /**
     * 设置更新人
     *
     * @param updateUserId 更新人
     */
    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}