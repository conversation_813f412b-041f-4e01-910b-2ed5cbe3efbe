package com.project.web;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.model.ProjectTeam;
import com.project.service.ProjectTeamService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.util.List;

/**
* Created by CodeGenerator on 2025/04/25.
*/
@Api(tags = "成团推广")
@RestController
@RequestMapping("/project/team")
public class ProjectTeamController {

	private static Logger log = LoggerFactory.getLogger(ProjectTeamController.class);

    @Resource
    private ProjectTeamService projectTeamService;

    @PostMapping("/add")
	@ApiOperation(value = "projectTeam新增",httpMethod = "POST")
    public Result add(@RequestBody ProjectTeam projectTeam) {
    	if(projectTeam == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	try {
    //		projectTeam.setCreateTime(new Date());
    //		projectTeam.setCreateUserId(userId);
    		projectTeamService.save(projectTeam);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success();
    }

    @RequestMapping("/delete")
	@ApiOperation(value = "projectTeam删除",httpMethod = "GET")
    public Result delete(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	try {
    		projectTeamService.deleteById(id);
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @PostMapping("/update")
	@ApiOperation(value = "projectTeam更新",httpMethod = "POST")
    public Result update(@RequestBody ProjectTeam projectTeam) {
    	if(projectTeam == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	if(projectTeam.getId() == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	try {
    //		projectTeam.setUpdateTime(new Date());
    //		projectTeam.setUpdateUserId(userId);
    		projectTeamService.update(projectTeam);
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @RequestMapping("/detail")
	@ApiOperation(value = "projectTeam获取详情",httpMethod = "GET")
    public Result<ProjectTeam> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	ProjectTeam projectTeam = null;
    	try {
    		projectTeam = projectTeamService.findById(id);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success(projectTeam);
    }

    @RequestMapping("/list")
	@ApiOperation(value = "projectTeam获取列表",httpMethod = "POST")
    public Result<List<ProjectTeam>> list(@RequestBody ProjectTeam projectTeam) {

 //       PageHelper.startPage(page, size);
        
        Condition condition = new Condition(projectTeam.getClass());
        Criteria criteria = condition.createCriteria();
//        criteria.andEqualTo("name", city.getName());
		PageInfo pageInfo = null;
		try {
    		 List<ProjectTeam> list = projectTeamService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
