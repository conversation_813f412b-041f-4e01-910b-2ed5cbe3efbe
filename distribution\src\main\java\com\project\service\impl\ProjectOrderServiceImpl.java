package com.project.service.impl;

import com.project.dao.ProjectOrderMapper;
import com.project.model.ProjectOrder;
import com.project.service.ProjectOrderService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/04/25.
 */
@Service
@Transactional
public class ProjectOrderServiceImpl extends AbstractService<ProjectOrder> implements ProjectOrderService {
    @Resource
    private ProjectOrderMapper cProjectOrderMapper;

}
