package com.project.web;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.model.*;
import com.project.service.*;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.project.web.parameters.ProjectBO;
import com.project.web.parameters.ProjectTeamBO;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
* Created by CodeGenerator on 2025/04/25.
*/
@Api(tags = "推广项目管理")
@RestController
@RequestMapping("/project")
public class ProjectController {

	private static Logger log = LoggerFactory.getLogger(ProjectController.class);

    @Resource
    private ProjectService projectService;

	@Resource
	private FlowService flowService;


	@Resource
	private ProjectTeamService projectTeamService;
	@Resource
	private ProjectAmountService projectAmountService;

	@Resource
	private UserUtil userUtil;

	@Resource
	private UserService userService;


    @RequestMapping("/detail")
	@ApiOperation(value = "project获取详情",httpMethod = "GET")
    public Result<ProjectBO> detail(@RequestParam Integer id, HttpServletRequest request) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
    	Project project = null;
    	try {
    		project = projectService.findById(id);
			ProjectBO bo = BeanUtil.toBean(project,ProjectBO.class);
			Condition c = new Condition(Flow.class);
			if (project.getType() == 1){
				c.createCriteria().andEqualTo("userId",tokenUser.getId())
						.andEqualTo("bizType",1);
				List<Flow> flows = flowService.findByCondition(c);
				bo.setFlows(flows);
			}else if (project.getType() == 2){
				c.createCriteria().andEqualTo("userId",tokenUser.getId())
						.andIn("bizType",CollUtil.newArrayList(2,3));
				List<Flow> flows = flowService.findByCondition(c);
				bo.setFlows(flows);
				c = new Condition(ProjectTeam.class);
				c.createCriteria().andEqualTo("projectId",id).andEqualTo("userId",tokenUser.getId());
				List<ProjectTeam> teams = projectTeamService.findByCondition(c);
				if (CollUtil.isNotEmpty(teams)){
					List<ProjectTeamBO> bos = new ArrayList<>();
					for (int i = 1; i < 8; i++) {
						ProjectTeamBO t = new ProjectTeamBO();
						t.setProjectId(project.getId());
						t.setCompanyId(project.getCompanyId());
						t.setLocation(i);
						for (ProjectTeam t2 :teams){
							if (t2.getLocation().compareTo(i) == 0){
								User member = userService.findById(t2.getMemberUserId());
								if (member != null){
									t.setIcon(member.getIcon());
									t.setName(member.getName());
									t.setMemberUserId(member.getId());
								} else {
									t.setIcon(t2.getIcon());
									t.setName(t2.getName());
									t.setMemberUserId(t2.getMemberUserId());
								}
							}
						}
						bos.add(t);
					}
					bo.setTeams(bos);
				} else {
					List<ProjectTeamBO> bos = new ArrayList<>();
					for (int i = 1; i < 8; i++) {
						ProjectTeamBO t = new ProjectTeamBO();
						t.setProjectId(project.getId());
						t.setCompanyId(project.getCompanyId());
						t.setLocation(i);
						bos.add(t);
					}
					bo.setTeams(bos);
				}
			}
			Condition c2 = new Condition(ProjectAmount.class);
			c2.createCriteria().andEqualTo("projectId",id)
					.andEqualTo("userId",tokenUser.getId());
			List<ProjectAmount> projectAmounts = projectAmountService.findByCondition(c2);
			if (CollUtil.isNotEmpty(projectAmounts)){
				bo.setProjectAmount(projectAmounts.get(0));
			}
			return Result.success(bo);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
    }


}
