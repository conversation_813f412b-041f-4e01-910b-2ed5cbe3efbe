package com.project.model;

import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(value="com.project.model.Flow")
@Table(name = "c_flow")
public class Flow extends BaseBeen implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    @Column(name = "user_id")
    @ApiModelProperty(value="userId")
    private Integer userId;

    @ApiModelProperty(value="pid")
    private Integer pid;

    /**
     * 代币类型 1-积分
     */
    @ApiModelProperty(value="type代币类型 1积分 2活动金额 3活动金额上限 4已提现")
    private Byte type;

    /**
     * 代币数值
     */
    @ApiModelProperty(value="num代币数值")
    private BigDecimal num;

    /**
     * 余额
     */
    @ApiModelProperty(value="amount余额")
    private BigDecimal amount;

    /**
     * 业务类型 1、直推分佣 2成团奖励 3成团分佣
     */
    @Column(name = "biz_type")
    @ApiModelProperty(value="业务类型 1、直推分佣 2成团奖励 3成团分佣 4购物抵扣 5参与活动新增上限 6提现")
    private Integer bizType;

    /**
     * 掉落业务id
     */
    @Column(name = "biz_id")
    @ApiModelProperty(value="bizId掉落业务id")
    private Long bizId;

    @Column(name = "create_time")
    @ApiModelProperty(value="createTime")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * @return user_id
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * @param userId
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取代币类型 代币类型 1积分 2活动金额 3活动金额上限 4已提现
     *
     * @return type - 代币类型 1积分 2活动金额 3活动金额上限 4已提现
     */
    public Byte getType() {
        return type;
    }

    /**
     * 设置代币类型 1积分 2活动金额 3活动金额上限 4已提现
     *
     * @param type 代币类型 1积分 2活动金额 3活动金额上限 4已提现
     */
    public void setType(Byte type) {
        this.type = type;
    }

    /**
     * 获取代币数值
     *
     * @return num - 代币数值
     */
    public BigDecimal getNum() {
        return num;
    }

    /**
     * 设置代币数值
     *
     * @param num 代币数值
     */
    public void setNum(BigDecimal num) {
        this.num = num;
    }

    /**
     * 获取余额
     *
     * @return amount - 余额
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 设置余额
     *
     * @param amount 余额
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 获取业务类型 1、直推分佣 2成团奖励 3成团分佣
     *
     * @return biz_type - 业务类型 1、直推分佣 2成团奖励 3成团分佣
     */
    public Integer getBizType() {
        return bizType;
    }

    /**
     * 设置业务类型 1、直推分佣 2成团奖励 3成团分佣
     *
     * @param bizType 业务类型 1、直推分佣 2成团奖励 3成团分佣
     */
    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    /**
     * 获取掉落业务id
     *
     * @return biz_id - 掉落业务id
     */
    public Long getBizId() {
        return bizId;
    }

    /**
     * 设置掉落业务id
     *
     * @param bizId 掉落业务id
     */
    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}