package com.project.service.impl;

import com.project.dao.OreMapper;
import com.project.model.Ore;
import com.project.service.OreService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/05/13.
 */
@Service
@Transactional
public class OreServiceImpl extends AbstractService<Ore> implements OreService {
    @Resource
    private OreMapper cOreMapper;

}
