package com.project.web.parameters;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Data
@ApiModel(value="提现打款入参")
public class DisposeWithdrawOrderParam implements Serializable {

    /**
     * 名称
     */
    @ApiModelProperty(value="外部流水号")
    private String outFlow;


    /**
     * 类型 1提现
     */
    @ApiModelProperty(value="id")
    private Integer withdrawOrderId;




    private static final long serialVersionUID = 1L;

}