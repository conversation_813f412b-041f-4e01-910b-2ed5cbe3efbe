package com.project.web;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.project.service.PermissionService;
import com.project.service.RolePermissionService;
import com.project.service.RoleService;
import com.project.core.Result;
import com.project.core.ResultCode;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.model.Permission;
import com.project.model.Role;
import com.project.model.RolePermission;
import com.project.model.User;
import com.project.web.parameters.PermissionBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * Created by CodeGenerator on 2019/11/01.
 */
@Api(tags = "角色管理")
@RestController
@RequestMapping("/role")
public class RoleController {

	private static Logger log = LoggerFactory.getLogger(RoleController.class);

	@Resource
	private RoleService roleService;

	@Resource
	private UserUtil userUtil;

	@Resource
	private PermissionService permissionService;

	@Resource
	private RolePermissionService rolePermissionService;

	@PostMapping("/add")
	@ApiOperation(value = "role新增",httpMethod = "POST")
	public Result add(@RequestBody Role role, HttpServletRequest request) {
		if (role == null) {
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if (StrUtil.isBlank(role.getName())) {
			return ResultGenerator.genFailResult(ResultCode.NAME_IS_NULL);
		}
		try {
			User tokenUser = userUtil.getTokenUser(request);
			role.setStatus("0");
			role.setCreateTime(DateUtil.date());
			if(tokenUser == null || StrUtil.isBlank(tokenUser.getName())){
				role.setCreateUserId("sys");
			} else {
				role.setCreateUserId(tokenUser.getName());
			}
			roleService.saveAndPermissionIds(role, role.getPermissionIds());
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}

		return ResultGenerator.genSuccessResult();
	}

	@RequestMapping("/delete")
	@ApiOperation(value = "role删除",httpMethod = "GET")
	public Result delete(@RequestParam Integer id) {
		if(id == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			if (id == 1){
				return ResultGenerator.genFailResult(ResultCode.ADMIN_ROLE_UNDELETE);
			}
			roleService.deleteById(id);
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return ResultGenerator.genSuccessResult();
	}

	@PostMapping("/update")
	@ApiOperation(value = "role更新",httpMethod = "POST")
	public Result update(@RequestBody Role role, HttpServletRequest request) {
		if (role == null) {
			log.error("参数不能为空");
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if (role.getId() == null) {
			log.error("参数不能为空");
			return ResultGenerator.genFailResult(ResultCode.ROLEID_IS_NULL);
		}
//		if (StrUtil.isBlank(role.getPermissionIds())) {
//			log.error("参数不能为空");
//			return ResultGenerator.genFailResult(ResultCode.PERMISSIONID_IS_NULL);
//		}
		try {
			User tokenUser = userUtil.getTokenUser(request);
			if (tokenUser == null){
				return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
			}
			List<RolePermission> temp = new ArrayList<RolePermission>();
			if (StrUtil.isNotBlank(role.getPermissionIds())) {
				String[] ids = role.getPermissionIds().split(",");
				for (String id : ids) {
					RolePermission cd = new RolePermission();
					cd.setRoleId(role.getId()+"");
					cd.setPermissionId(id);
					temp.add(cd);
				}
			}
			Role temp2 = new Role();
			temp2.setId(role.getId());
			temp2.setUpdateTime(new Date());
			temp2.setUpdateUserId(tokenUser.getName());
			temp2.setName(role.getName());
			temp2.setStatus(role.getStatus());
			if (rolePermissionService.bangRolePermission(temp, temp2) > 0) {
				return ResultGenerator.genSuccessResult();
			} else {
				return ResultGenerator.genFailResult(ResultCode.SQL_ERROR);
			}
		} catch (Exception e) {
			log.error("绑定关系异常:e{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping("/detail")
	@ApiOperation(value = "role获取详情",httpMethod = "GET")
	public Result<Role> detail(@RequestParam Integer id) {
		if(id == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}

		Role role = null;
		try {
			role = roleService.findById(id);
			Condition condition = new Condition(RolePermission.class);
			Criteria criteria = condition.createCriteria();
			if (id != null) {
				criteria.andEqualTo("roleId", id+"");
			}
			List<RolePermission> rps = rolePermissionService.findByCondition(condition);
			Condition condition2 = new Condition(Permission.class);
			condition2.orderBy("seq");
			List<Permission> ps = permissionService.findByCondition(condition2);
			if (ps == null || ps.isEmpty()) {
				return ResultGenerator.genFailResult(ResultCode.RESULT_IS_NULL);
			}
			List<PermissionBo> bos = new ArrayList<PermissionBo>();
			for (Permission permission : ps) {
				PermissionBo t = new PermissionBo();
				BeanUtils.copyProperties(permission,t);
				t.setHasRelevance("0");
				for (RolePermission rp : rps) {
					if (StrUtil.equals(rp.getPermissionId(), permission.getId() + "")) {
						t.setHasRelevance("1");
					}
				}
				bos.add(t);
			}
			List<PermissionBo> list = assemblyChildren(bos);
			role.setPermissions(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}

		return ResultGenerator.genSuccessResult(role);
	}

	/**
	 * 组装资源组
	 *
	 * @param rs
	 * @return
	 */
	private static List<PermissionBo> assemblyChildren(List<PermissionBo> rs) {
		if (rs == null) {
			return null;
		}
		//权限的集合对象   对象里面有权限的子权限(孩子节点)
		List<PermissionBo> temp = new LinkedList<PermissionBo>();
		temp.addAll(rs);


		List<PermissionBo> ret = new LinkedList<PermissionBo>();
		// 组装一个map
		//把ID为k  每个权限对象 组装为map
		Map<String, PermissionBo> tempMap = new LinkedHashMap<String, PermissionBo>();
		for (PermissionBo r : temp) {
			tempMap.put(r.getId() + "", r);
		}

		//组装子父权限集合
		for (PermissionBo r : temp) {
			//判断是否有父节点
			if (StrUtil.isNotBlank(r.getParentId())) {
				//取出当前对象的父节点对象
				PermissionBo parent = tempMap.get(r.getParentId());
				//判断不为空
				if (parent != null) {
					//取出对象中子节点集合
					List<PermissionBo> children = parent.getChildren();

					//LIst等于空   就创建一个有序集合
					if (children == null) {
						children = new LinkedList<PermissionBo>();
					}
					//在集合添加此对象ID
					children.add(tempMap.get(r.getId() + ""));
					parent.setChildren(children);
				}
			}
		}
		//组装最大的父权限
		//判断权限集合每个对象是否有父节点
		for (PermissionBo r : temp) {
			if (StrUtil.isBlank(r.getParentId())) {
				ret.add(tempMap.get(r.getId() + ""));
			}
		}
		return ret;
	}

	@PostMapping("/list")
	@ApiOperation(value = "role获取列表",httpMethod = "POST")
	public Result<List<Role>> list(@RequestBody Role role) {
		PageHelper.startPage(role.getPage(), role.getSize());
		Condition condition = new Condition(role.getClass());
		Criteria criteria = condition.createCriteria();

		if (StrUtil.isNotBlank(role.getName())) {
			criteria.andLike("name", "%" + role.getName() + "%");
		}
		if (StrUtil.isNotBlank(role.getStatus())) {
			criteria.andEqualTo("status", role.getStatus());
		}
		if (StrUtil.isNotBlank(role.getStartTime())) {
			criteria.andGreaterThanOrEqualTo("createTime", role.getStartTime());
		}
		if (StrUtil.isNotBlank(role.getEndTime())) {
			criteria.andLessThanOrEqualTo("createTime", role.getEndTime());
		}
		PageInfo pageInfo = null;
		try {
			List<Role> list = roleService.findByCondition(condition);
			pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return ResultGenerator.genSuccessResult(pageInfo);
	}
}
