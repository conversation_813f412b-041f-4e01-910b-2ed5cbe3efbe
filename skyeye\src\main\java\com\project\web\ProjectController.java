package com.project.web;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.model.*;
import com.project.service.*;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.project.web.parameters.*;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
* Created by CodeGenerator on 2025/01/22.
*/
@Api(tags = "[app]项目管理")
@RestController
@RequestMapping("/project")
public class ProjectController {

	private static Logger log = LoggerFactory.getLogger(ProjectController.class);

    @Resource
    private ProjectService projectService;

	@Resource
	private UserService userService;

	@Resource
	private ProjectItemService projectItemService;

	@Resource
	private ProjectItemMemberService projectItemMemberService;


	@Resource
	private ProjectItemNodeService projectItemNodeService;

	@Resource
	private ProjectItemRecordService projectItemRecordService;

	@Resource
	private UserUtil userUtil;

	@PostMapping("/add")
	@ApiOperation(value = "project新增",httpMethod = "POST")
    public Result add(@RequestBody ProjectParam param,HttpServletRequest request) {
    	if(param == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
    	try {
			if (param.getId() == null){
				if (StrUtil.isBlank(param.getName())){
					return ResultGenerator.genFailResult(ResultCode.NAME_IS_NULL);
				}
				Project project = BeanUtil.toBean(param,Project.class);
				if (project.getStatus() == null){
					project.setStatus(0);
				}
				project.setCreateTime(DateUtil.date());
				project.setUserId(tokenUser.getId());
				project.setCreateUserId(tokenUser.getId());
				projectService.saveUseGeneratedKeys(project);
				addItmeAndMember(1,project,param.getItems());
			} else {
				Project project = BeanUtil.toBean(param,Project.class);
				project.setUpdateTime(DateUtil.date());
				project.setStatus(null);
				project.setUserId(null);
				projectService.update(project);
				addItmeAndMember(2,project,param.getItems());
			}
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success();
    }

	private void addItmeAndMember(Integer type ,Project project, List<ProjectItemParam> items) {
		try {
			if (type == 2){
				Condition c =new Condition(ProjectItem.class);
				c.createCriteria().andEqualTo("projectId",project.getId());
				projectItemService.deleteByCondition(c);
			}
			for (ProjectItemParam projectItemParam : items){
				projectItemParam.setStatus(0);
				projectItemParam.setProjectId(project.getId());
				ProjectItem projectItem = BeanUtil.toBean(projectItemParam,ProjectItem.class);
				projectItem.setAuditRet(0);
				projectItem.setId(null);
				projectItem.setCreateTime(DateUtil.date());
				projectItemService.saveUseGeneratedKeys(projectItem);
				try{
					ProjectItemMember member = new ProjectItemMember();
					member.setProjectId(projectItem.getProjectId());
					member.setProjectItemId(projectItem.getId());
					member.setCreateTime(DateUtil.date());
					member.setUserId(projectItem.getPrincipalUserId());
					List<ProjectItemMember> members = projectItemParam.getMembers();
					if (CollUtil.isNotEmpty(members)){
						boolean hasPrincipalUserId = false;
						for (ProjectItemMember m : members){
							m.setId(null);
							m.setProjectId(projectItem.getProjectId());
							m.setProjectItemId(projectItem.getId());
							m.setCreateTime(DateUtil.date());
							if (m.getUserId().compareTo(projectItem.getPrincipalUserId()) == 0){
								hasPrincipalUserId = true;
							}
							projectItemMemberService.save(m);
						}
						if (!hasPrincipalUserId){
							projectItemMemberService.save(member);
						}
					}
				}catch (Exception e){
					log.error("新增成员操作异常e:{}",e);
				}
			}
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
		}
	}


	@GetMapping("/delete")
	@ApiOperation(value = "删除项目",httpMethod = "GET")
	public Result delete(@RequestParam Integer projectId,HttpServletRequest request) {
		Project p = projectService.findById(projectId);
		if (p == null){
			return Result.success();
		}
		if (p.getStatus() == 0){
			projectService.deleteById(projectId);
			Condition c = new Condition(ProjectItem.class);
			c.createCriteria().andEqualTo("projectId",projectId);
			List<ProjectItem> items = projectItemService.findByCondition(c);
			if (CollUtil.isNotEmpty(items)){
				projectItemService.deleteByIds(items.stream()
						.map(item -> String.valueOf(item.getId()))
						.collect(Collectors.joining(",")));
			}
		} else{
			return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
		}
		return Result.success();
	}


	@GetMapping("/startItem")
	@ApiOperation(value = "启动项目明细",httpMethod = "GET")
	public Result startItem(@RequestParam Integer projectItemId,HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		ProjectItem projectItem = projectItemService.findById(projectItemId);
		if (projectItem == null){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if (projectItem .getStatus() != 0){
			return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
		}
		if (projectItem.getPrincipalUserId().compareTo(tokenUser.getId()) != 0 ){
			return ResultGenerator.genFailResult(ResultCode.YOU_NOT_AUDIT_USER);
		}
		ProjectItem u = new ProjectItem();
		u.setId(projectItemId);
		u.setStatus(1);
		projectItemService.update(u);
		return ResultGenerator.genSuccessResult();
	}

	@GetMapping("/start")
	@ApiOperation(value = "启动项目",httpMethod = "GET")
	public Result start(@RequestParam Integer projectId,HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		Project project = projectService.findById(projectId);
		if (project == null){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if (project .getStatus() != 0){
			return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
		}
		if (project.getUserId().compareTo(tokenUser.getId()) != 0
				&& project.getAuditUserId().compareTo(tokenUser.getId()) != 0 ){
			return ResultGenerator.genFailResult(ResultCode.YOU_NOT_AUDIT_USER);
		}
		Project u = new Project();
		u.setId(projectId);
		u.setStatus(1);
		projectService.update(u);
		return ResultGenerator.genSuccessResult();
	}

	@GetMapping("/submitItem")
	@ApiOperation(value = "提交项目明细",httpMethod = "GET")
	public Result submitItem(@RequestParam Integer projectItemId,HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		ProjectItem projectItem = projectItemService.findById(projectItemId);
		if (projectItem == null){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if (projectItem .getStatus() != 1){
			return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
		}
		if (projectItem.getPrincipalUserId().compareTo(tokenUser.getId()) != 0 ){
			return ResultGenerator.genFailResult(ResultCode.YOU_NOT_AUDIT_USER);
		}

		ProjectItem u = new ProjectItem();
		u.setId(projectItemId);
		u.setStatus(2);
		projectItemService.update(u);
		return ResultGenerator.genSuccessResult();
	}

	@GetMapping("/finish")
	@ApiOperation(value = "完成项目",httpMethod = "GET")
	public Result finish(@RequestParam Integer projectId,HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		Project project = projectService.findById(projectId);
		if (project == null){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if (project .getStatus() != 1 && project .getStatus() != 2){
			return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
		}
		if (project.getUserId().compareTo(tokenUser.getId()) != 0
				&& project.getAuditUserId().compareTo(tokenUser.getId()) != 0 ){
			return ResultGenerator.genFailResult(ResultCode.YOU_NOT_AUDIT_USER);
		}
		//判断所有明细 都完成
		Condition c = new Condition(ProjectItem.class);
		c.createCriteria().andEqualTo("projectId",projectId);
		List<ProjectItem> items = projectItemService.findByCondition(c);
		if (CollUtil.isNotEmpty(items)){
			for (ProjectItem item:items ) {
				if (item.getStatus() != 4){
					return ResultGenerator.genFailResult(ResultCode.ITEM_STATUS_IS_ERROR);
				}
			}
		}
		Project u = new Project();
		u.setId(projectId);
		u.setStatus(3);
		projectService.update(u);
		return ResultGenerator.genSuccessResult();
	}

	@RequestMapping("/itemDetail")
	@ApiOperation(value = "明细详情",httpMethod = "GET")
	public Result<ProjectItemBO> itemDetail(@RequestParam Integer itemId) {
		if (itemId == null) {
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		ProjectItem item = projectItemService.findById(itemId);
		if(item == null){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		Project project = projectService.findById(item.getProjectId());
		if(project == null){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		ProjectItemBO bo = BeanUtil.toBean(item,ProjectItemBO.class);
		bo.setProjectName(project.getName());
		Condition c = new Condition(ProjectItemRecord.class);
		c.createCriteria().andEqualTo("projectItemId",item.getId())
				.andGreaterThanOrEqualTo("createTime",DateUtil.beginOfMonth(DateUtil.date()));
		List<ProjectItemRecord> records = projectItemRecordService.findByCondition(c);
		if (CollUtil.isNotEmpty(records)){
			bo.setRecords(records.stream().map(e->BeanUtil.toBean(e,ProjectItemRecordBO.class)).collect(Collectors.toList()));
		}
		//TODO 计算完成度

		if (bo.getCompletion() == null){
			bo.setCompletion(0D);
		}
		c = new Condition(ProjectItemMember.class);
		c.createCriteria().andEqualTo("projectItemId",item.getId()) ;
		List<ProjectItemMember> members = projectItemMemberService.findByCondition(c);
		if (CollUtil.isNotEmpty(members)){
			bo.setMembers(members.stream().map(e->BeanUtil.toBean(e,ProjectItemMemberBO.class)).collect(Collectors.toList()));
		}
		return Result.success(bo);
	}

	@PostMapping("/addItme")
	@ApiOperation(value = "新增项目明细",httpMethod = "POST")
	public Result addItme(@RequestBody ProjectItemParam param) {
		if(param == null){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if(param.getProjectId() == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		if(param.getPrincipalUserId() == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}

		try {
			param.setStatus(0);
			ProjectItem projectItem = BeanUtil.toBean(param,ProjectItem.class);
			projectItem.setAuditRet(0);
			projectItem.setCreateTime(DateUtil.date());
			projectItemService.saveUseGeneratedKeys(projectItem);
			ProjectItemMember member = new ProjectItemMember();
			member.setProjectId(projectItem.getProjectId());
			member.setProjectItemId(projectItem.getId());
			member.setCreateTime(DateUtil.date());
			member.setUserId(projectItem.getPrincipalUserId());
			List<ProjectItemMember> members = param.getMembers();
			if (CollUtil.isNotEmpty(members)){
				boolean hasPrincipalUserId = false;
				for (ProjectItemMember m : members){
					m.setId(null);
					m.setProjectId(projectItem.getProjectId());
					m.setProjectItemId(projectItem.getId());
					m.setCreateTime(DateUtil.date());
					if (m.getUserId().compareTo(projectItem.getPrincipalUserId()) == 0){
						hasPrincipalUserId = true;
					}
				}
				if (!hasPrincipalUserId){
					members.add(member);
				}
			} else {
				members = CollUtil.newArrayList(member);
			}

			projectItemMemberService.save(members);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}

		return Result.success();
	}

	@RequestMapping("/deleteItme")
	@ApiOperation(value = "删除项目明细",httpMethod = "POST")
	public Result deleteItme(@RequestParam Integer itemId) {
		if(itemId == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			projectItemService.deleteById(itemId);
			Condition c = new Condition(ProjectItemMember.class);
			c.createCriteria().andEqualTo("projectItemId",itemId);
			List<ProjectItemMember> members = projectItemMemberService.findByCondition(c);
			if (CollUtil.isNotEmpty(members)){
				for (ProjectItemMember member:members ) {
					projectItemMemberService.deleteById(member.getId());
				}
			}
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success();
	}

	@PostMapping("/updateItme")
	@ApiOperation(value = "更新项目明细",httpMethod = "POST")
	public Result updateItme(@RequestBody ProjectItem projectItem) {
		if(projectItem == null){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if(projectItem.getId() == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}

		try {
			ProjectItem item = projectItemService.findById(projectItem.getId());
			if(item == null){
				return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
			}
			projectItem.setUpdateTime(DateUtil.date());
			projectItemService.update(projectItem);
			if (projectItem.getPrincipalUserId() != null && item.getPrincipalUserId().compareTo(projectItem.getPrincipalUserId()) !=0){
				Condition c = new Condition(ProjectItemMember.class);
				c.createCriteria().andEqualTo("userId",projectItem.getPrincipalUserId())
						.andEqualTo("projectItemId",projectItem.getId());
				List<ProjectItemMember> members = projectItemMemberService.findByCondition(c);
				if (CollUtil.isEmpty(members)){
					ProjectItemMember member = new ProjectItemMember();
					member.setProjectId(item.getProjectId());
					member.setUserId(projectItem.getPrincipalUserId());
					member.setProjectItemId(projectItem.getId());
					member.setCreateTime(DateUtil.date());
					projectItemMemberService.save(member);
				}
			}
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success();
	}

	@PostMapping("/signRecord")
	@ApiOperation(value = "签到或请假",httpMethod = "POST")
	public Result signRecord(@RequestBody ProjectItemRecordParam param) {
		if(param == null){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if(param.getProjectItemId() == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			ProjectItemRecord  projectItemRecord = BeanUtil.toBean(param,ProjectItemRecord.class);
			ProjectItem item = projectItemService.findById(param.getProjectItemId());
			if (item ==null){
				return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
			}
			projectItemRecord.setProjectId(item.getProjectId());
			projectItemRecord.setTeamId(item.getPrincipalUserId());
			if (param.getType() == 3){
				projectItemRecord.setOnPics(param.getOnPics());
//				projectItemRecord.setOnTime(DateUtil.date());
				projectItemRecord.setOnTime(param.getOnTime());
				projectItemRecord.setType(1);
			} else if (param.getType() == 4){
				projectItemRecord.setOffPics(param.getOffPics());
//				projectItemRecord.setOffTime(DateUtil.date());
				projectItemRecord.setOffTime(param.getOffTime());

				projectItemRecord.setType(1);
			} else  if (param.getType() == 2){
				projectItemRecord.setOnTime(param.getOnTime());
				projectItemRecord.setOffTime(param.getOffTime());
			}
			Boolean flag = false;
			if (param.getId() != null){
				ProjectItemRecord old = projectItemRecordService.findById(param.getId());
				if (old != null){
					if (old.getType() == 3){
						projectItemRecord.setOnTime(null);
					}
					projectItemRecord.setId(param.getId());
					projectItemRecord.setUpdateTime(DateUtil.date());
					projectItemRecord.setCreateTime(null);
					projectItemRecordService.update(projectItemRecord);
					flag = true;
				}
			}
			if (!flag){
				projectItemRecord.setSignDay(DateUtil.today());
				projectItemRecord.setCreateTime(DateUtil.date());
				projectItemRecordService.save(projectItemRecord);
			}
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success();
	}

	@RequestMapping("/memberList")
	@ApiOperation(value = "项目明细成员列表",httpMethod = "GET")
	public Result<List<ProjectItemMemberBO>> memberList(@RequestParam Integer projectItemId) {
		Condition c = new Condition(ProjectItemMember.class);
		c.createCriteria().andEqualTo("projectItemId",projectItemId);
		List<ProjectItemMember> members = projectItemMemberService.findByCondition(c);
		if (CollUtil.isNotEmpty(members)){
			return Result.success(members.stream().map( e -> {
				User user = userService.findById(e.getUserId());
				ProjectItemMemberBO bo = BeanUtil.toBean(e,ProjectItemMemberBO.class);
				if (user != null){
					bo.setName(user.getName());
					bo.setIcon(user.getIcon());
				}
				return  bo;
			}).collect(Collectors.toList()));
		}
		return Result.success();
	}

	@PostMapping("/addMember")
	@ApiOperation(value = "项目明细新增成员",httpMethod = "POST")
	public Result addMember(@RequestBody List<ProjectItemMemberBO> members) {
		if (CollUtil.isNotEmpty(members)){
			List<ProjectItemMember> adds = members.stream().map(e-> {
				ProjectItemMember bo = BeanUtil.toBean(e,ProjectItemMember.class);
				bo.setCreateTime(DateUtil.date());
				return  bo;
			}).collect(Collectors.toList());
			try{
				projectItemMemberService.save(adds);
			}catch (DuplicateKeyException e){
				return Result.success();
			}

		}
		return Result.success();
	}

	@RequestMapping("/deleteMember")
	@ApiOperation(value = "项目明细删除成员",httpMethod = "GET")
	public Result deleteMember(@RequestParam String memberIds) {
		projectItemMemberService.deleteByIds(memberIds);
		return Result.success();
	}

	@PostMapping("/addNode")
	@ApiOperation(value = "新增节点",httpMethod = "POST")
	public Result addNode(@RequestBody ProjectItemNode projectItemNode) {
		if(projectItemNode == null){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if(projectItemNode.getProjectId() == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		if(projectItemNode.getProjectItemId() == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			projectItemNode.setCreateTime(DateUtil.date());
			projectItemNodeService.save(projectItemNode);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}

		return Result.success();
	}


	@PostMapping("/audit")
	@ApiOperation(value = "审核",httpMethod = "POST")
	public Result audit(@RequestBody AuditParam param,HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		if(param == null){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if(param.getProjectItemId() == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			ProjectItem item = projectItemService.findById(param.getProjectItemId());
			if(item == null){
				return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
			}
			if (item.getAuditRet() != 0){
				return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
			}
			if (item.getStatus() != 2){
				return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
			}
			Project  project = projectService.findById(item.getProjectId());
			if(project == null){
				return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
			}
			if (project.getUserId().compareTo(tokenUser.getId()) != 0
				&& project.getAuditUserId().compareTo(tokenUser.getId()) != 0
			){
				return ResultGenerator.genFailResult(ResultCode.YOU_NOT_AUDIT_USER);
			}
			ProjectItem update = new ProjectItem();
			update.setAudit(param.getAudit());
			update.setId(param.getProjectItemId());
			update.setAuditRet(param.getAuditRet());
			update.setAuditTime(DateUtil.date());
			update.setAuditType(param.getAuditType());
			update.setScore(param.getScore());
			if (param.getAuditRet() == 1){
				update.setStatus(4);
			}else if (param.getAuditRet() == 2){
				update.setStatus(3);
			}
			projectItemService.update(update);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success();
	}


	@RequestMapping("/record")
	@ApiOperation(value = "project获取记录详情",httpMethod = "GET")
	public Result<ProjectItemRecordBO> record(@RequestParam Integer itemId,@RequestParam String time,HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		Condition c = new Condition(ProjectItemRecord.class);
		c.createCriteria().andEqualTo("projectItemId",itemId)
				.andEqualTo("signDay",time);
		List<ProjectItemRecord> records = projectItemRecordService.findByCondition(c);
		if (CollUtil.isNotEmpty(records)){
			return ResultGenerator.genSuccessResult(records.get(0));
		}
		return ResultGenerator.genSuccessResult();
	}

	@RequestMapping("/detail")
	@ApiOperation(value = "project获取详情",httpMethod = "POST")
    public Result<ProjectVO> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	try {
			Project project = projectService.findById(id);
			if(project == null){
				return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
			}
			ProjectVO vo = BeanUtil.toBean(project,ProjectVO.class);
			Condition c = new Condition(ProjectItem.class);
			c.createCriteria().andEqualTo("projectId",project.getId());
			List<ProjectItem> items = projectItemService.findByCondition(c);
			vo.setCompletion(0D);
			if (CollUtil.isNotEmpty(items)){
				vo.setItems(items.stream().map(e -> {
					ProjectItemBO bo = BeanUtil.toBean(e, ProjectItemBO.class);
					bo.setCompletion(0D);
					Condition c1 = new Condition(ProjectItemRecord.class);
					c1.createCriteria().andEqualTo("projectId", e.getProjectId())
							.andEqualTo("projectItemId", e.getId());
					Integer countDays = projectItemRecordService.countByCondition(c1);
					bo.setSignDays(countDays);
					if (countDays != null){
						bo.setCompletion(Double.valueOf(countDays)/e.getDays());
					}

					Condition c2 = new Condition(ProjectItemMember.class);
					c2.createCriteria().andEqualTo("projectItemId",e.getId()) ;
					List<ProjectItemMember> members = projectItemMemberService.findByCondition(c2);
					if (CollUtil.isNotEmpty(members)){
						bo.setMembers(members.stream().map(e1->BeanUtil.toBean(e1,ProjectItemMemberBO.class)).collect(Collectors.toList()));
					}
					return bo;
				}).collect(Collectors.toList()));
				//设置完成度 completion
				List<ProjectItem> completionList = items.stream().filter(e -> e.getStatus() == 4).collect(Collectors.toList());
				if (CollUtil.isNotEmpty(completionList)){
					Double completion = Double.valueOf(completionList.size()*100) / Double.valueOf(items.size());
					String formatted = String.format("%.1f", completion);
					vo.setCompletion(Double.parseDouble(formatted));
				}
			}
			c = new Condition(ProjectItemNode.class);
			c.createCriteria().andEqualTo("projectId",project.getId());
			List<ProjectItemNode> nodes = projectItemNodeService.findByCondition(c);
			if (CollUtil.isNotEmpty(nodes)){
				vo.setNodes(nodes.stream().map(e -> BeanUtil.toBean(e, ProjectItemNodeBO.class)).collect(Collectors.toList()));
			}

			return Result.success(vo);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        

    }

    @RequestMapping("/list")
	@ApiOperation(value = "project获取列表",httpMethod = "POST")
    public Result<List<ProjectBO>> list(@RequestBody Project project, HttpServletRequest request) {

		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}

        Condition condition = new Condition(project.getClass());
        Criteria criteria = condition.createCriteria();
		if (StrUtil.isNotBlank(project.getName())){
			criteria.andLike("name", "%"+project.getName()+"%");
		}
		if (project.getStatus() != null){
			criteria.andEqualTo("status", project.getStatus());
		}
		if (tokenUser.getType() == 3){
			criteria.andEqualTo("userId", tokenUser.getId());
		} else if (tokenUser.getType() == 1){
			criteria.andEqualTo("ownerPhone", tokenUser.getLoginAccount());
		} else if (tokenUser.getType() == 2){
			Condition c2 = new Condition(ProjectItemMember.class);
			c2.createCriteria().andEqualTo("userId",tokenUser.getId());
			List<ProjectItemMember> members = projectItemMemberService.findByCondition(c2);
			if (CollUtil.isNotEmpty(members)){
				criteria.andIn("id", members.stream() .map(ProjectItemMember::getProjectId)
						.collect(Collectors.toList()));
			} else {
				return Result.success(new PageInfo());
			}
		}
		PageInfo pageInfo = null;
		try {
			PageHelper.startPage(project.getPage(), project.getSize());
			condition.setOrderByClause("id desc");
    	    List<Project> list = projectService.findByCondition(condition);
		    pageInfo = new PageInfo(list);
			List<ProjectBO> list2 = new ArrayList<>();
			for (Project p:list){
				ProjectBO bo = BeanUtil.toBean(p, ProjectBO.class);
				Condition c = new Condition(ProjectItemRecord.class);
				c.createCriteria().andEqualTo("projectId", p.getId());
				Integer countDays = projectItemRecordService.countByCondition(c);
				bo.setSignDays(countDays);
				Condition c1 = new Condition(ProjectItem.class);
				c1.createCriteria().andEqualTo("projectId",p.getId());
				List<ProjectItem> items = projectItemService.findByCondition(c1);
				bo.setCompletion(0D);
				if (CollUtil.isNotEmpty(items)){
					bo.setItems(items.stream().map(e1 -> {
						ProjectItemBO bo1 = BeanUtil.toBean(e1, ProjectItemBO.class);
						bo1.setCompletion(0D);
						Condition c2 = new Condition(ProjectItemRecord.class);
						c2.createCriteria().andEqualTo("projectId", e1.getProjectId())
								.andEqualTo("projectItemId", e1.getId());
						return bo1;
					}).collect(Collectors.toList()));
					//设置完成度 completion
					List<ProjectItem> completionList = items.stream().filter(e1 -> e1.getStatus() == 4).collect(Collectors.toList());
					if (CollUtil.isNotEmpty(completionList)){
						Double completion = Double.valueOf(completionList.size()*100) / Double.valueOf(items.size());
						String formatted = String.format("%.1f", completion);
						bo.setCompletion(Double.parseDouble(formatted));
					}
				}
				list2.add(bo);
			}
			pageInfo.setList(list2);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }


	/**
	 * 通过userId获取菜单，需要TOKEN的.
	 *
	 */
	@ApiOperation(value = "获取统计信息 type 1：月实现盈利 2年实现盈利 ",httpMethod = "GET")
	@RequestMapping(value = "/statistics", method = { RequestMethod.GET, RequestMethod.POST })
	public Result<StatisticsBo> statistics(@RequestParam Integer type, HttpServletRequest request) {
		Condition condition = new Condition(Project.class);
		Criteria criteria = condition.createCriteria();
		if (type == 1){
			criteria.andGreaterThanOrEqualTo("beginTime", DateUtil.beginOfMonth(DateUtil.date()))
					.andLessThanOrEqualTo("finishTime", DateUtil.endOfMonth(DateUtil.date()));
		} else {
			criteria.andGreaterThanOrEqualTo("beginTime", DateUtil.beginOfYear(DateUtil.date()))
					.andLessThanOrEqualTo("finishTime", DateUtil.endOfYear(DateUtil.date()));
		}
		PageInfo pageInfo = null;
		try {
			StatisticsBo bo = new StatisticsBo();
			bo.setFinsh(BigDecimal.ZERO);
			bo.setTotal(BigDecimal.ONE);
			List<Project> list = projectService.findByCondition(condition);
			BigDecimal total = BigDecimal.ZERO ;
			BigDecimal finish = BigDecimal.ZERO;
			if (CollUtil.isNotEmpty(list)){
				for (Project p:list ) {
					total = total .add(p.getAmount());
					if (p.getStatus() == 3){
						finish = finish .add(p.getAmount());
					}
				}
				bo.setFinsh(finish);
				bo.setTotal(total);
			}
			return ResultGenerator.genSuccessResult(bo);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}

	}
}
