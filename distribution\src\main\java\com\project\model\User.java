package com.project.model;

import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
@ApiModel(value="用户")
@Table(name = "b_user")
public class User extends BaseBeen implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;



    /**
     * 姓名
     */
    @ApiModelProperty(value="name姓名")
    private String name;


    @Column(name = "out_user_id")
    @ApiModelProperty(value="外部用户id")
    private String outUserId;

    @ApiModelProperty(value="地址")
    private String address;

    @ApiModelProperty(value="头像")
    private String icon;
    @Transient
    @ApiModelProperty(value="type 登录类型 1业主 2施工方 3项目方")
    private Integer type;

    @Column(name = "vip_level")
    @ApiModelProperty(value="窗口数")
    private Integer vipLevel;



    @ApiModelProperty(value="pid")
    private Integer pid;

    @ApiModelProperty(value="积分")
    private BigDecimal amount;

    @Column(name = "withdraw_amount")
    @ApiModelProperty(value="已提现金额")
    private BigDecimal withdrawAmount;

    @Column(name = "freeze_amount")
    @ApiModelProperty(value="冻结金额")
    private BigDecimal freezeAmount;

    @ApiModelProperty(value="交易密码")
    private String transpwd;


    @Column(name = "invite_code")
    @ApiModelProperty(value="邀请码")
    private String inviteCode;

    /**
     * 登录账号
     */
    @Column(name = "login_account")
    @ApiModelProperty(value="loginAccount登录账号")
    private String loginAccount;

    /**
     * 密码
     */
    @ApiModelProperty(value="password密码")
    private String password;

    /**
     * 密码盐
     */
    @ApiModelProperty(value="salt密码盐")
    private String salt;

    /**
     * 手机号码
     */
    @Column(name = "phone_no")
    @ApiModelProperty(value="phoneNo手机号码")
    private String phoneNo;

    @Column(name = "work_no")
    @ApiModelProperty(value="工号")
    private String workNo;


    /**
     * 邮箱
     */
    @ApiModelProperty(value="email邮箱")
    private String email;

    /**
     * 状态 0 正常 1注销 
     */
    @ApiModelProperty(value="status状态 0 正常 1注销 ")
    private String status;

    /**
     * 公司id
     */
    @Column(name = "company_id")
    @ApiModelProperty(value="companyId公司id")
    private Integer companyId;





    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    @Column(name = "vip_time")
    @ApiModelProperty(value="会员到期时间")
    private Date vipTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    /**
     * 创建人
     */
    @Column(name = "create_user_id")
    @ApiModelProperty(value="createUserId创建人")
    private String createUserId;


    /**
     * 更新人
     */
    @Column(name = "update_user_id")
    @ApiModelProperty(value="updateUserId更新人")
    private String updateUserId;

    private static final long serialVersionUID = 1L;


    @Transient
    private String token;

    @Transient
    private String roleName;

    @Transient
    private String roleIds;



    @Transient
    private List<UserRole> userRoles;


}