package com.project.web;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.model.Address;
import com.project.model.User;
import com.project.service.AddressService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.project.service.AreaService;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.stream.Collectors;

/**
* Created by CodeGenerator on 2025/04/25.
*/
@Api(tags = "地址管理")
@RestController
@RequestMapping("/address")
public class AddressController {

	private static Logger log = LoggerFactory.getLogger(AddressController.class);

    @Resource
    private AddressService addressService;

	@Resource
	private AreaService areaService;

	@Resource
	private UserUtil userUtil;

    @PostMapping("/add")
	@ApiOperation(value = "address新增",httpMethod = "POST")
    public Result add(@RequestBody Address address,HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
    	if(address == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	try {
			if (address.getIsDefault() == 1){
				Condition c =new Condition(Address.class);
				c.createCriteria().andEqualTo("userId",tokenUser.getId())
						.andEqualTo("isDefault",1);
				List<Address> addresses = addressService.findByCondition(c);
				if (CollUtil.isNotEmpty(addresses)){
					for (Address a :addresses){
						Address u = new Address();
						u.setId(a.getId());
						u.setIsDefault(0);
						addressService.update(u);
					}
				}
			}
			address.setUserId(tokenUser.getId());
			address.setCreateTime(DateUtil.date());
			addressService.save(address);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success();
    }

    @RequestMapping("/delete")
	@ApiOperation(value = "address删除",httpMethod = "GET")
    public Result delete(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	try {
    		addressService.deleteById(id);
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @PostMapping("/update")
	@ApiOperation(value = "address更新",httpMethod = "POST")
    public Result update(@RequestBody Address address) {
    	if(address == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	if(address.getId() == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	try {
    		addressService.update(address);
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @RequestMapping("/detail")
	@ApiOperation(value = "address获取详情",httpMethod = "GET")
    public Result<Address> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	Address address = null;
    	try {
    		address = addressService.findById(id);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success(address);
    }

	@RequestMapping("/setDefault")
	@ApiOperation(value = "设置默认地址",httpMethod = "GET")
	public Result<Address> setDefault( @RequestParam Integer id,HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		Address address = addressService.findById(id);
		if (address.getIsDefault() == 1){
			Condition c =new Condition(Address.class);
			c.createCriteria().andEqualTo("userId",tokenUser.getId())
					.andEqualTo("isDefault",1);
			List<Address> addresses = addressService.findByCondition(c);
			if (CollUtil.isNotEmpty(addresses)){
				for (Address a :addresses){
					if (address.getId().compareTo(a.getId()) == 0){
						continue;
					}
					Address u = new Address();
					u.setId(a.getId());
					u.setIsDefault(0);
					addressService.update(u);
				}
			}
			return Result.success();
		}
		if (address.getUserId().compareTo(tokenUser.getId()) !=0){
			return ResultGenerator.genFailResult(ResultCode.PERMISSION_NOT_HAS);
		}
		Condition c =new Condition(Address.class);
		c.createCriteria().andEqualTo("userId",tokenUser.getId())
				.andEqualTo("isDefault",1);
		List<Address> addresses = addressService.findByCondition(c);
		if (CollUtil.isNotEmpty(addresses)){
			for (Address a :addresses){
				Address u = new Address();
				u.setId(a.getId());
				u.setIsDefault(0);
				addressService.update(u);
			}
		}
		Address u = new Address();
		u.setId(id);
		u.setIsDefault(1);
		addressService.update(u);
		return Result.success();
	}

	@RequestMapping("/getDefault")
	@ApiOperation(value = "获取默认地址",httpMethod = "GET")
	public Result<Address> getDefault( HttpServletRequest request) {

		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		try {
			Condition c =new Condition(Address.class);
			c.createCriteria().andEqualTo("userId",tokenUser.getId())
					.andEqualTo("isDefault",1);
			List<Address> addresses = addressService.findByCondition(c);
			if (CollUtil.isNotEmpty(addresses)){
				Address address = addresses.get(0);
				address.setAreaName(areaService.getAreaName(address.getAreaCode()));
				return Result.success(address);
			}
			return Result.success();
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}

	}

    @RequestMapping("/list")
	@ApiOperation(value = "address获取列表",httpMethod = "POST")
    public Result<List<Address>> list(@RequestBody Address address,HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}

        PageHelper.startPage(address.getPage(), address.getSize());
        Condition condition = new Condition(address.getClass());
		PageInfo pageInfo = null;
		try {
			condition.createCriteria().andEqualTo("userId",tokenUser.getId());
			condition.setOrderByClause("is_default desc");
    		 List<Address> list = addressService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
			 pageInfo.setList(list.stream().map(e -> {
				 e.setAreaName(areaService.getAreaName(e.getAreaCode()));
				 return e;
			 }).collect(Collectors.toList()));
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
