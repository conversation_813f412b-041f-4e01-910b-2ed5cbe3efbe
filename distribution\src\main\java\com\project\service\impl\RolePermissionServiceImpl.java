package com.project.service.impl;

import com.project.dao.RoleMapper;
import com.project.dao.RolePermissionMapper;
import com.project.exception.ServiceException;
import com.project.model.Role;
import com.project.model.RolePermission;
import com.project.service.RolePermissionService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.List;


/**
 * Created by CodeGenerator on 2024/04/08.
 */
@Service
@Transactional
public class RolePermissionServiceImpl extends AbstractService<RolePermission> implements RolePermissionService {
    @Resource
    private RolePermissionMapper bRolePermissionMapper;

    @Resource
    private RoleMapper bRoleMapper;

    /**
     * 做删除操作后，进行插入
     */
    @Override
    public int save(List<RolePermission> models) {
        Condition condition = new Condition(RolePermission.class);
        Example.Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("roleId", models.get(0).getRoleId());
        bRolePermissionMapper.deleteByCondition(condition);
        return bRolePermissionMapper.insertList(models);
    }

    @Override
    public int bangRolePermission(List<RolePermission> temp, Role role) {
        if (role==null) {
            throw new ServiceException("角色不能为空");
        }
        if (role.getId() == null) {
            throw new ServiceException("角色id不能为空");
        } else {
            if(bRoleMapper.updateByPrimaryKeySelective(role) > 0){
                return save(temp);
            } else {
                throw new ServiceException("更新关联关系操作失败");
            }
        }

    }
}
