<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.OrderMapper">
  <resultMap id="BaseResultMap" type="com.project.model.Order">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="pid" jdbcType="INTEGER" property="pid" />
    <result column="level" jdbcType="INTEGER" property="level" />
    <result column="month" jdbcType="INTEGER" property="month" />
  </resultMap>
</mapper>