package com.project.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;

import java.net.URLEncoder;

@Slf4j
//@Component
public class GaoApi {

    private static String TRANS_API_HOST = "https://restapi.amap.com/";
    private static String KEY = "8f91609ed5b15a58c3e70d9f3ca46978";


    @SneakyThrows
    public static String getInfo(String query) {
        return HttpGet.get(TRANS_API_HOST+"v3/geocode/geo?output=json&key="+KEY+"&address="+URLEncoder.encode(query,"UTF-8"),null);
    }


    public static void main(String[] args) {
        System.out.println(getInfo("金帝海珀雅苑"));
    }



}
