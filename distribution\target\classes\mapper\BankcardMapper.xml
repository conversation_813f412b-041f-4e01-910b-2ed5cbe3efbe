<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.BankcardMapper">
  <resultMap id="BaseResultMap" type="com.project.model.Bankcard">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="no" jdbcType="VARCHAR" property="no" />
    <result column="bank" jdbcType="VARCHAR" property="bank" />
    <result column="sub_bank" jdbcType="VARCHAR" property="subBank" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="identity_card" jdbcType="VARCHAR" property="identityCard" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>