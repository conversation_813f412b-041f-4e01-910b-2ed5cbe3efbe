<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.FreezeMapper">
  <resultMap id="BaseResultMap" type="com.project.model.Freeze">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="no" jdbcType="INTEGER" property="no" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>