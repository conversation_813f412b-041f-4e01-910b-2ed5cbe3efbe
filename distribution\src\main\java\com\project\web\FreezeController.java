package com.project.web;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.model.Freeze;
import com.project.service.FreezeService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.util.List;

/**
* Created by CodeGenerator on 2025/05/13.
*/
@Api(tags = "freeze管理")
@RestController
@RequestMapping("/freeze")
public class FreezeController {

	private static Logger log = LoggerFactory.getLogger(FreezeController.class);

    @Resource
    private FreezeService freezeService;

    @PostMapping("/add")
	@ApiOperation(value = "freeze新增",httpMethod = "POST")
    public Result add(@RequestBody Freeze freeze) {
    	if(freeze == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	try {
    //		freeze.setCreateTime(new Date());
    //		freeze.setCreateUserId(userId);
    		freezeService.save(freeze);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success();
    }

    @RequestMapping("/delete")
	@ApiOperation(value = "freeze删除",httpMethod = "GET")
    public Result delete(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	try {
    		freezeService.deleteById(id);
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @PostMapping("/update")
	@ApiOperation(value = "freeze更新",httpMethod = "POST")
    public Result update(@RequestBody Freeze freeze) {
    	if(freeze == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	if(freeze.getId() == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	try {
    //		freeze.setUpdateTime(new Date());
    //		freeze.setUpdateUserId(userId);
    		freezeService.update(freeze);
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @RequestMapping("/detail")
	@ApiOperation(value = "freeze获取详情",httpMethod = "GET")
    public Result<Freeze> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	Freeze freeze = null;
    	try {
    		freeze = freezeService.findById(id);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success(freeze);
    }

    @RequestMapping("/list")
	@ApiOperation(value = "freeze获取列表",httpMethod = "POST")
    public Result<List<Freeze>> list(@RequestBody Freeze freeze) {

 //       PageHelper.startPage(page, size);
        
        Condition condition = new Condition(freeze.getClass());
        Criteria criteria = condition.createCriteria();
//        criteria.andEqualTo("name", city.getName());
		PageInfo pageInfo = null;
		try {
    		 List<Freeze> list = freezeService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
