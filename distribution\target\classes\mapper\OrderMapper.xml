<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.OrderMapper">
  <resultMap id="BaseResultMap" type="com.project.model.Order">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />

    <result column="score_type" jdbcType="INTEGER" property="scoreType" />

    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="score_amount" jdbcType="DECIMAL" property="scoreAmount" />

    <result column="address_id" jdbcType="INTEGER" property="addressId" />

    <result column="deliver_status" jdbcType="INTEGER" property="deliverStatus" />
    <result column="deliver_no" jdbcType="VARCHAR" property="deliverNo" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />


    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="pid" jdbcType="INTEGER" property="pid" />
    <result column="goods_id" jdbcType="INTEGER" property="goodsId" />
  </resultMap>
</mapper>