package com.project.service.impl;

import com.project.dao.CompanyMapper;
import com.project.model.Company;
import com.project.service.CompanyService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/05/07.
 */
@Service
@Transactional
public class CompanyServiceImpl extends AbstractService<Company> implements CompanyService {
    @Resource
    private CompanyMapper bCompanyMapper;

}
