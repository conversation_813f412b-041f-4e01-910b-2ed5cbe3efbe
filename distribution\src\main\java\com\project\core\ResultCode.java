package com.project.core;

/**
 * 响应码枚举，参考HTTP状态码的语义
 */
public enum ResultCode {
    /**
     * 系统错误
     */
    SUCCESS("200","成功"),//成功
    FAIL("400","失败"),//失败
    UNAUTHORIZED("401","未认证（签名错误）"),//未认证（签名错误）
    NOT_FOUND("404","接口不存在"),//接口不存在
    INTERNAL_SERVER_ERROR("500","服务器内部错误"),
    PERMISSION_NOT_HAS("409","没有操作权限"),
    BACKSTAGE_IS_ERROR("501","网关异常"),

    /**
     * 业务错误
     */
    CZPF("502","操作频繁"),
    OBJECT_IS_NULL("6000","对象不能为空"),

    ID_IS_NULL("6001","对象ID不能为空"),
    CODE_IS_NULL("6002","编号不能为空"),
    NAME_IS_NULL("6003","名称不能为空"),
    SEQ_IS_NULL("6004","排序不能为空"),
    LEVEL_IS_NULL("6005","级别不能为空"),
    USERID_IS_NULL("6006","用户id不能为空"),
    RESULT_IS_NULL("6007","查询结果为空"),
    TYPE_IS_NULL("6008","类型为空"),
    SQL_ERROR("6009","数据库异常"),
    IDENTITY_IS_NULL("6010","身份证不能为空"),
    CUSTOMERID_IS_NULL("6011","客户号不能为空"),
    CUSTOMERID_IS_ERROR("6012","未查询到用户信息"),
    SELECT_IS_ERROR("6013","数据异常查询失败，请联系检查用户信息"),
    IDENTITYUPKEY_IS_ERROR("6014","身份证正面照片key不能为空"),
    CODE_IS_INVALID("6015","验证码无效"),
    TOKEN_IS_NULL("6016","TOKEN不能为空"),

    /**
     * 权限
     */
    PERMISSION_ENAME_IS_NULL("6040","英文名不能为空"),
    PERMISSION_ISACTION_IS_NULL("6041","是否有动作不能为空"),
    PERMISSION_COMPOSINGKEY_IS_NULL("6042","排版不能为空"),
    PERMISSIONID_IS_NULL("6043","权限id不能为空"),

    /**
     * 登录
     */
    LOGIN_ACCOUNT_IS_NULL("6030","登录账号不能为空"),
    LOGIN_ACCOUNT_IS_ERROR("6035","登录账号不存在"),
    LOGIN_PASSWORD_IS_NULL("6031","登录密码不能为空"),
    LOGIN_PASSWORD_IS_ERROR("6033","登录密码错误"),
    LOGIN_USER_STATUS_IS_ERROR("6034","用户状态有误"),


    ACCOUNT_IS_NULL("7001","账号不能为空" ),
    USER_IS_NULL("7002","用户未找到" ),
    USER_IS_ERROR("7003", "用户异常"),
    UNLOGIN_PWD_ERROR("7004", "登录密码错误") ,
    ROLEID_IS_NULL("7005", "角色id不能为空" ),
    ADMIN_ROLE_UNDELETE("7006", "超级管理员不能被删除"),
    UNLOGIN_FAILURE ("7007", "登录失败"),

    USER_IS_EXIST("7008", "用户已注册"),
    PHONE_IS_NULL("7009", "手机号码为空"),
    CONTENT_IS_NULL ("7010", "内容为空"),
    ADDRESS_IS_NULL("7011", "地址为空"),
    STATUS_IS_ERROR("7012", "状态异常" ),
    EXAMINE_TIME_IS_NULL("7013", "检查时间不能为空" ),
    ROLEIDS_IS_NULL("7014", "roleIds不能为空" ),
    ROLE_IS_ERROR("7015", "您不是管理员无法操作" ),
    DELETE_ADMIN_USER("7016", "超级管理员无法删除"),
    VIP_TIME_IS_NULL ("7017","您不是vip"),
    VIP_TIME_IS_OVER ("7018","您的VIP已过期"),
    CODE_IS_ERROR ("7019","卡密有误"),
    INVITECODE_ERROR("7020","邀请码未找到，可以不填" ),
    CODE_IS_USED("7021", "卡密已使用"), NOT_CREATE_USER("7022", "您不是订单创建人" ),
    YOU_NOT_AUDIT_USER("7023", "您不是审核人员或者项目负责人" ),
    ITEM_STATUS_IS_ERROR("7024", "项目明细为完成"),
    YEAR_IS_NULL("7025", "请设置年份" ),
    MONTH_IS_NULL("7025", "请设置月份"),

    ORDER_IS_HAVE("7026", "您已购买产品"),


    AMOUNT_IS_NOT_INSUFFICIENT("7027", "您的积分余额不足" ),
    PARAMETER_IS_ILLEGALITY("7028", "参数不合法" ),
    ORDER_IS_NOT_HAVE("7029", "您还没购买产品" ), SIGNED("7030", "您已签到" ),
    FREEZE_IS_NULL("7031", "冻结信息未找到"),
    USER_IS_NOT_ADMIN("7032", "用户不是管理员"),

    BANKCARD_IS_HAVE("7033", "银行卡信息已存在，请联系管理员更改" ),
    GOODS_IS_NULL("7034", "商品没找到" ), USER_IS_NOT_ACTIVE("7035", "用户已禁用，请退出后重新登录" );//服务器内部错误

    private final String code;   //状态码
    private final String message;

    ResultCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    public String code() {
        return code;
    }

    public String message() {
        return message;
    }

    public String getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    /**
     * 通过状态码获取ENUM的名字
     * @param code
     * @return
     */
    public static ResultCode getEnumByStatusCode(String code) {
        for (ResultCode p : ResultCode.values()) {
            if (p.code().equalsIgnoreCase(code)) {
                return p;
            }
        }

        return null;
    }
}
