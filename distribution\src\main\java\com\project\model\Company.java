package com.project.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Data
@ApiModel(value="com.project.model.Company")
@Table(name = "b_company")
public class Company implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 类型 1渠道商 2公司
     */
    @ApiModelProperty(value="type类型 1渠道商 2公司")
    private Integer type;

    /**
     * 负责人
     */
    @ApiModelProperty(value="principal负责人")
    private String principal;

    @ApiModelProperty(value="图片")
    private String pic;

    /**
     * 负责人电话
     */
    @Column(name = "principal_phone")
    @ApiModelProperty(value="principalPhone负责人电话")
    private String principalPhone;

    @Column(name = "principal_wechat")
    @ApiModelProperty(value="负责人微信")
    private String principalWechat;

    @Column(name = "principal_qrcode")
    @ApiModelProperty(value="负责人微信二维码")
    private String principalQrcode;

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    /**
     * 备注
     */
    @ApiModelProperty(value="content备注")
    private String content;

    @ApiModelProperty(value="支付描述")
    private String description;

    /**
     * vip失效时间
     */
    @Column(name = "vip_time")
    @ApiModelProperty(value="vipTimevip失效时间")
    private Date vipTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取类型 1渠道商 2公司
     *
     * @return type - 类型 1渠道商 2公司
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置类型 1渠道商 2公司
     *
     * @param type 类型 1渠道商 2公司
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取负责人
     *
     * @return principal - 负责人
     */
    public String getPrincipal() {
        return principal;
    }

    /**
     * 设置负责人
     *
     * @param principal 负责人
     */
    public void setPrincipal(String principal) {
        this.principal = principal;
    }

    /**
     * 获取负责人电话
     *
     * @return principal_phone - 负责人电话
     */
    public String getPrincipalPhone() {
        return principalPhone;
    }

    /**
     * 设置负责人电话
     *
     * @param principalPhone 负责人电话
     */
    public void setPrincipalPhone(String principalPhone) {
        this.principalPhone = principalPhone;
    }

    /**
     * 获取名称
     *
     * @return name - 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取备注
     *
     * @return content - 备注
     */
    public String getContent() {
        return content;
    }

    /**
     * 设置备注
     *
     * @param content 备注
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * 获取vip失效时间
     *
     * @return vip_time - vip失效时间
     */
    public Date getVipTime() {
        return vipTime;
    }

    /**
     * 设置vip失效时间
     *
     * @param vipTime vip失效时间
     */
    public void setVipTime(Date vipTime) {
        this.vipTime = vipTime;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}