package com.project.service.impl;

import com.project.dao.OrderMapper;
import com.project.model.Order;
import com.project.service.OrderService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2024/04/25.
 */
@Service
@Transactional
public class OrderServiceImpl extends AbstractService<Order> implements OrderService {
    @Resource
    private OrderMapper bOrderMapper;

}
