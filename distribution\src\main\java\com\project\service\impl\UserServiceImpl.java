package com.project.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.project.dao.UserMapper;
import com.project.model.Flow;
import com.project.model.User;
import com.project.model.UserRole;
import com.project.service.FlowService;
import com.project.service.UserRoleService;
import com.project.service.UserService;
import com.project.core.AbstractService;
import com.project.web.parameters.ValBo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Created by CodeGenerator on 2024/04/08.
 */
@Service
@Transactional
public class UserServiceImpl extends AbstractService<User> implements UserService {
    @Resource
    private UserMapper bUserMapper;

    @Resource
    private UserRoleService userRoleService;

    @Resource
    private FlowService flowService;

    @Override
    public void saveAndBandRole(User user, String roleIds, String code) {
        if (StrUtil.isNotBlank(code)){
            User p = findBy("inviteCode", code);
            if (p != null){
                user.setPid(p.getId());
                user.setCompanyId(p.getCompanyId());
            }
        }
        if (user.getCompanyId() == null){
            user.setCompanyId(1);
        }
        if (saveUseGeneratedKeys(user) > 0 ) {
            if (StrUtil.isNotBlank(roleIds)){
                String[] ids = roleIds.split(",");
                List<UserRole> temp = new ArrayList<UserRole>();
                for (String rId : ids) {
                    UserRole cd = new UserRole();
                    cd.setRoleId(Integer.parseInt(rId));
                    cd.setUserId(user.getId());
                    temp.add(cd);
                }
                if (temp != null) {
                    Condition condition = new Condition(UserRole.class);
                    Example.Criteria criteria = condition.createCriteria();
                    criteria.andEqualTo("userId", user.getId());
                    userRoleService.deleteByCondition(condition);
                    userRoleService.save(temp);
                }
            }
        }
    }

    @Override
    public void saveAndUpdateOpenUser(User user, String outUserId, String outPid) {
        User u = findBy("outUserId", outUserId);
        if (u == null) {
            Condition c = new Condition(User.class);
            c.createCriteria().andEqualTo("phoneNo", user.getPhoneNo())
                    .andEqualTo("companyId",user.getCompanyId());
            List<User> users = findByCondition(c);
            if (CollUtil.isNotEmpty(users)){
                u = users.get(0);
            }
        }
        if (u == null){
            u = user;
            User p = findBy("outUserId", outPid);
            if (p != null){
                u.setPid(p.getId());
            }
        }
        u.setIcon(user.getIcon());
        u.setName(user.getName());
        u.setOutUserId(outUserId);
        update(u);
    }

    @Override
    public void updateAndBandRole(User user, String roleIds) {
        update(user);
        if (StringUtils.isNotBlank(roleIds)) {
            String[] ids = roleIds.split(",");
            List<UserRole> temp = new ArrayList<UserRole>();
            for (String rId : ids) {
                UserRole cd = new UserRole();
                cd.setRoleId(Integer.parseInt(rId));
                cd.setUserId(user.getId());
                temp.add(cd);
            }
            if (temp != null) {
                Condition condition = new Condition(UserRole.class);
                Example.Criteria criteria = condition.createCriteria();
                criteria.andEqualTo("userId", user.getId());
                userRoleService.deleteByCondition(condition);
                userRoleService.save(temp);
            }
        }
    }

    @Override
    public boolean check(User user) {
        if (user.getVipTime() == null){
            return false;
        }
        if (DateUtil.compare(DateUtil.date(),user.getVipTime())>=0){
            return false;
        }
        return true;
    }

    /**
     * 货币交易
     * @param userId
     * @param bizType 业务类型 DealBizTypeEnum
     * @param type 1积分 2活动金额 3活动上限 4提现
     * @param num
     * @return
     */
    @Override
    public Boolean deal(Integer userId, Integer bizType, Byte type, BigDecimal num, Long bizId) {
        User user = findById(userId);
        if (user == null){
            return false;
        }
        BigDecimal a = BigDecimal.ZERO;
        if (type.compareTo((byte)1) == 0){
            a = user.getAmount();
        } else  if (type.compareTo((byte)4) == 0){
            a = user.getFreezeAmount();
        }else  if (type.compareTo((byte)6) == 0){
            a = user.getWithdrawAmount();
        }
        flowService.save(Flow.builder().bizType(bizType).bizId(bizId).amount(a.add(num))
                        .pid(user.getPid())
                .createTime(DateUtil.date()).num(num).userId(userId).type(type).build());
        Map<String,Object> map = new HashMap<>();
        map.put("userId",userId);
        map.put("bizType",bizType);
        map.put("type",type);
        map.put("num",num);
        int i = bUserMapper.deal(map);
        if (i > 0 ){
            return true;
        } else {
            return false;
        }
    }

    @Override
    public List<ValBo> statistic(Integer userId) {
        return bUserMapper.countPopularize(userId);
    }


}
