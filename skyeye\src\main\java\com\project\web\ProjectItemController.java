package com.project.web;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.model.ProjectItem;
import com.project.service.ProjectItemService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.util.List;

/**
* Created by CodeGenerator on 2025/01/22.
*/
@Api(tags = "项目明细管理")
@RestController
@RequestMapping("/project/item")
public class ProjectItemController {

	private static Logger log = LoggerFactory.getLogger(ProjectItemController.class);

    @Resource
    private ProjectItemService projectItemService;

    @PostMapping("/add")
	@ApiOperation(value = "projectItem新增",httpMethod = "POST")
    public Result add(@RequestBody ProjectItem projectItem) {
    	if(projectItem == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	try {
    //		projectItem.setCreateTime(new Date());
    //		projectItem.setCreateUserId(userId);
    		projectItemService.save(projectItem);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success();
    }

    @RequestMapping("/delete")
	@ApiOperation(value = "projectItem删除",httpMethod = "POST")
    public Result delete(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	try {
    		projectItemService.deleteById(id);
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @PostMapping("/update")
	@ApiOperation(value = "projectItem更新",httpMethod = "POST")
    public Result update(@RequestBody ProjectItem projectItem) {
    	if(projectItem == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	if(projectItem.getId() == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	try {
    //		projectItem.setUpdateTime(new Date());
    //		projectItem.setUpdateUserId(userId);
    		projectItemService.update(projectItem);
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @RequestMapping("/detail")
	@ApiOperation(value = "projectItem获取详情",httpMethod = "POST")
    public Result<ProjectItem> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	ProjectItem projectItem = null;
    	try {
    		projectItem = projectItemService.findById(id);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success(projectItem);
    }

    @RequestMapping("/list")
	@ApiOperation(value = "projectItem获取列表",httpMethod = "POST")
    public Result<List<ProjectItem>> list(@RequestBody ProjectItem projectItem) {

        PageHelper.startPage(projectItem.getPage(), projectItem.getSize());
        
        Condition condition = new Condition(projectItem.getClass());
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("projectId", projectItem.getProjectId());
		PageInfo pageInfo = null;
		try {
    		 List<ProjectItem> list = projectItemService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
