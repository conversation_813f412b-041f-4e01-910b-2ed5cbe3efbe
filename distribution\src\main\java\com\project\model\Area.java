package com.project.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;
import javax.persistence.*;

@Data
@ApiModel(value="com.project.model.Area")
@Table(name = "b_area")
public class Area implements Serializable {
    /**
     * id
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="idid")
    private Integer id;

    /**
     * 地区码
     */
    @Column(name = "area_code")
    @ApiModelProperty(value="areaCode地区码")
    private String areaCode;

    /**
     * 区域名称
     */
    @Column(name = "area_name")
    @ApiModelProperty(value="areaName区域名称")
    private String areaName;

    /**
     * 上级区域编码
     */
    @Column(name = "area_parent_code")
    @ApiModelProperty(value="areaParentCode上级区域编码")
    private String areaParentCode;

    /**
     * 区域类型 1国家2省3市4区5街道
     */
    @Column(name = "area_type")
    @ApiModelProperty(value="areaType区域类型 1国家2省3市4区5街道")
    private String areaType;

    private static final long serialVersionUID = 1L;

    /**
     * 获取id
     *
     * @return id - id
     */
    public Integer getId() {
        return id;
    }

    /**
     * 设置id
     *
     * @param id id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取地区码
     *
     * @return area_code - 地区码
     */
    public String getAreaCode() {
        return areaCode;
    }

    /**
     * 设置地区码
     *
     * @param areaCode 地区码
     */
    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    /**
     * 获取区域名称
     *
     * @return area_name - 区域名称
     */
    public String getAreaName() {
        return areaName;
    }

    /**
     * 设置区域名称
     *
     * @param areaName 区域名称
     */
    public void setAreaName(String areaName) {
        this.areaName = areaName;
    }

    /**
     * 获取上级区域编码
     *
     * @return area_parent_code - 上级区域编码
     */
    public String getAreaParentCode() {
        return areaParentCode;
    }

    /**
     * 设置上级区域编码
     *
     * @param areaParentCode 上级区域编码
     */
    public void setAreaParentCode(String areaParentCode) {
        this.areaParentCode = areaParentCode;
    }

    /**
     * 获取区域类型 1国家2省3市4区5街道
     *
     * @return area_type - 区域类型 1国家2省3市4区5街道
     */
    public String getAreaType() {
        return areaType;
    }

    /**
     * 设置区域类型 1国家2省3市4区5街道
     *
     * @param areaType 区域类型 1国家2省3市4区5街道
     */
    public void setAreaType(String areaType) {
        this.areaType = areaType;
    }

    @Transient
    @ApiModelProperty("子地区列表")
    private List<Area> children;
}