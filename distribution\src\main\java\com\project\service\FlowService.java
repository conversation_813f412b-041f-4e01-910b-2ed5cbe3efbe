package com.project.service;
import com.project.model.Flow;
import com.project.core.Service;
import com.project.web.parameters.ValBo;

import java.util.List;


/**
 * Created by CodeGenerator on 2025/05/08.
 */
public interface FlowService extends Service<Flow> {

    /**
     * type :0直推人数 1直推有效人数 2直推收益 3成团奖励 4成团分佣
     * @param id
     * @param type
     * @return
     */
    List<ValBo> statistic(Integer id, Integer type);
}
