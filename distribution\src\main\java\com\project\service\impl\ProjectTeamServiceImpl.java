package com.project.service.impl;

import com.project.dao.ProjectTeamMapper;
import com.project.model.ProjectTeam;
import com.project.service.ProjectTeamService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/04/25.
 */
@Service
@Transactional
public class ProjectTeamServiceImpl extends AbstractService<ProjectTeam> implements ProjectTeamService {
    @Resource
    private ProjectTeamMapper cProjectTeamMapper;

}
