package com.project.core;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一API响应结果封装
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class Result<T> {
    private String code;
    private String msg;
    private T data;

    public Result setCode(ResultCode resultCode) {
        this.code = resultCode.code();
        return this;
    }

    public String getCode() {
        return code;
    }

    public Result setCode(String code) {
        this.code = code;
        return this;
    }

    public String getMessage() {
        return msg;
    }

    public Result setMessage(String msg) {
        this.msg = msg;
        return this;
    }

    public T getData() {
        return data;
    }

    public Result setData(T data) {
        this.data = data;
        return this;
    }



    @Override
    public String toString() {
        return JSON.toJSONString(this);
    }


    public static Result success() {
        return Result.builder()
                .code("200")
                .msg("success")
                .build();
    }

    public static Result success(String msg) {
        return Result.builder()
                .code("200")
                .msg(msg)
                .data(null)
                .build();
    }

    public static Result success(Object data) {
        return Result.builder()
                .code("200")
                .msg("success")
                .data(data)
                .build();
    }

    public static Result success(Object data, String message) {
        return Result.builder()
                .code("200")
                .msg(message)
                .data(data)
                .build();
    }

    public static Result build(String code, String message) {
        return Result.builder()
                .code(code)
                .msg(message)
                .build();
    }

    public static Result fail(String code, String message, Object data) {
        return Result.builder()
                .code(code)
                .msg(message)
                .data(data)
                .build();
    }

    public static Result fail(String code, String message) {
        return Result.builder()
                .code(code)
                .msg(message)
                .build();
    }

    public static Result fail(ResultCode resultCode) {
        return Result.builder()
                .code(resultCode.getCode())
                .msg(resultCode.getMessage())
                .build();
    }

    public static Result fail(Object data) {
        return Result.builder()
                .code("400")
                .msg("操作失败")
                .data(data)
                .build();
    }

    public static Result fail(String msg) {
        return Result.builder()
                .code("400")
                .msg(msg)
                .build();
    }

    public static Result fail() {
        return Result.builder()
                .code("400")
                .msg("fail")
                .build();
    }
}
