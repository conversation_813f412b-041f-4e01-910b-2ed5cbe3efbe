<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.FlowMapper">
  <resultMap id="BaseResultMap" type="com.project.model.Flow">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="pid" jdbcType="INTEGER" property="pid" />
    <result column="type" jdbcType="TINYINT" property="type" />
    <result column="num" jdbcType="DECIMAL" property="num" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="biz_type" jdbcType="INTEGER" property="bizType" />
    <result column="biz_id" jdbcType="BIGINT" property="bizId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <select id="statisticByType" resultType="com.project.web.parameters.ValBo">
    SELECT
      DATE(create_time) AS name,  -- 将时间戳转换为日期格式
      COUNT(DISTINCT biz_id) AS val  -- 统计每天的不同用户数量
    FROM
      c_flow
    WHERE
      user_id = #{userId}
      and biz_type = #{bizType}
    GROUP BY
      DATE(create_time)  -- 只按日期分组
    ORDER BY
      DATE(create_time) ASC;  -- 按日期升序排列
    </select>

  <select id="statisticAmountByType" resultType="com.project.web.parameters.ValBo">

    SELECT
      DATE(create_time) AS name,  -- 将时间戳转换为日期格式
      sum(num) AS val          -- 统计每天的用户数量
    FROM
      c_flow                           -- 假设用户表名为 users
    WHERE
      user_id = #{userId}
      and biz_type = #{bizType}
      and type = 1
    GROUP BY
      DATE(create_time)                -- 按日期分组
    ORDER BY
      DATE(create_time) ASC;              -- 按日期升序排列
  </select>
</mapper>