<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.ProjectAmountMapper">
  <resultMap id="BaseResultMap" type="com.project.model.ProjectAmount">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="activity_limit" jdbcType="DECIMAL" property="activityLimit" />
    <result column="activity_amount" jdbcType="DECIMAL" property="activityAmount" />
    <result column="activity_num" jdbcType="INTEGER" property="activityNum" />
    <result column="activity_output" jdbcType="DECIMAL" property="activityOutput" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>

  <update id="deal" >
  update c_project_amount set
      <if test="type != null and type == 2">
        activity_amount = activity_amount + #{num}
      </if>
      <if test="type != null and type == 3">
        activity_limit = activity_limit + #{num}
      </if>
      <if test="type != null and type == 5">
          activity_output = activity_output + #{num}
      </if>
  where user_id = #{userId} and project_id =  #{projectId}
      <if test="type != null and type == 2">
        and  activity_amount + #{num} >= 0
      </if>
      <if test="type != null and type == 3">
        and  activity_limit + #{num} >= 0
      </if>
      <if test="type != null and type == 5">
          and  activity_output + #{num} >= 0
      </if>
  </update>
</mapper>