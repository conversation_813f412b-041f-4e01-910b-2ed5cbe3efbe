package com.project.web.parameters;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@ApiModel("统计对象")
public class StatisticsBo {

    @ApiModelProperty("总预算")
    private BigDecimal total;

    @ApiModelProperty("完成业绩")
    private  BigDecimal finsh;





}