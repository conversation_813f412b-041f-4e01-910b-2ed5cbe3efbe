package com.project.web.parameters;

import com.project.core.BaseBeen;
import com.project.model.NoticeUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="公告")
public class NoticeBO extends BaseBeen implements Serializable {
    @ApiModelProperty(value="id")
    private Integer id;


    /**
     * 内容
     */
    @ApiModelProperty(value="content内容")
    private String content;

    @ApiModelProperty(value="content内容")
    private String title;


    @ApiModelProperty(value="img")
    private String img;

    @ApiModelProperty(value="创建人")
    private String createUser;


    /**
     * 失效时间
     */
    @ApiModelProperty(value="loseTime失效时间")
    private Date loseTime;

    /**
     * 状态 1正常 2禁用
     */
    @ApiModelProperty(value="status状态 1正常 2禁用")
    private Integer status;

    @ApiModelProperty(value="类型 1通知 2公告")
    private Integer type;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    @ApiModelProperty(value="参与人")
    private List<NoticeUser> users;

    private static final long serialVersionUID = 1L;


}