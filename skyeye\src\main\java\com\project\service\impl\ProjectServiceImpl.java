package com.project.service.impl;

import com.project.dao.ProjectMapper;
import com.project.model.Project;
import com.project.service.ProjectService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/01/22.
 */
@Service
@Transactional
public class ProjectServiceImpl extends AbstractService<Project> implements ProjectService {
    @Resource
    private ProjectMapper bProjectMapper;

}
