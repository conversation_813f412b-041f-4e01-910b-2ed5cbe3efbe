package com.project.web.parameters;

import com.project.core.BaseBeen;
import com.project.model.Company;
import com.project.model.UserRole;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
@ApiModel(value="用户")
public class UserBO extends BaseBeen implements Serializable {
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 姓名
     */
    @ApiModelProperty(value="name姓名")
    private String name;

    @ApiModelProperty(value="地址")
    private String address;

    @ApiModelProperty(value="头像")
    private String icon;
    @ApiModelProperty(value="type 登录类型 1用户 2渠道方")
    private Integer type;

    @ApiModelProperty(value="窗口数")
    private Integer vipLevel;



    @ApiModelProperty(value="pid")
    private Integer pid;

    @ApiModelProperty(value="余额")
    private BigDecimal amount;

    @ApiModelProperty(value="已提现金额")
    private BigDecimal withdrawAmount;

    @ApiModelProperty(value="冻结金额")
    private BigDecimal freezeAmount;

    @ApiModelProperty(value="交易密码")
    private String transpwd;


    @ApiModelProperty(value="邀请码")
    private String inviteCode;

    /**
     * 登录账号
     */
    @ApiModelProperty(value="loginAccount登录账号")
    private String loginAccount;

    /**
     * 密码
     */
    @ApiModelProperty(value="password密码")
    private String password;

    /**
     * 密码盐
     */
    @ApiModelProperty(value="salt密码盐")
    private String salt;

    /**
     * 手机号码
     */
    @ApiModelProperty(value="phoneNo手机号码")
    private String phoneNo;

    @ApiModelProperty(value="工号")
    private String workNo;

    @ApiModelProperty(value="推荐人")
    private String referrer;


    /**
     * 邮箱
     */
    @ApiModelProperty(value="email邮箱")
    private String email;

    /**
     * 状态 0 正常 1注销 
     */
    @ApiModelProperty(value="status状态 0 正常 1注销 ")
    private String status;

    /**
     * 公司id
     */
    @ApiModelProperty(value="companyId公司id")
    private Integer companyId;

    @ApiModelProperty(value="公司")
    private Company company;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    @ApiModelProperty(value="会员到期时间")
    private Date vipTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    /**
     * 创建人
     */
    @ApiModelProperty(value="createUserId创建人")
    private String createUserId;

    /**
     * 更新人
     */
    @ApiModelProperty(value="updateUserId更新人")
    private String updateUserId;

    private static final long serialVersionUID = 1L;

    private String token;

    private String roleName;

    @ApiModelProperty(value="活动每日产出")
    private BigDecimal activityOutput;

    private String roleIds;

    @ApiModelProperty(value="签到状态 1 今天未签到 2 今天已签到 ")
    private Integer signStatus;

    private List<UserRole> userRoles;

    @ApiModelProperty(value="推荐人 ")
    private UserBO parent;

}