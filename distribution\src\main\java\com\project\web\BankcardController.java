package com.project.web;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.model.Bankcard;
import com.project.model.User;
import com.project.service.BankcardService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.project.service.UserService;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
* Created by CodeGenerator on 2025/05/10.
*/
@Api(tags = "银行卡管理")
@RestController
@RequestMapping("/bankcard")
public class BankcardController {

	private static Logger log = LoggerFactory.getLogger(BankcardController.class);

	@Resource
	private UserUtil userUtil;
    @Resource
    private BankcardService bankcardService;

	@Resource
	private UserService userService;

    @PostMapping("/add")
	@ApiOperation(value = "bankcard新增",httpMethod = "POST")
    public Result add(@RequestBody Bankcard bankcard, HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
    	if(bankcard == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	try {
			Condition c = new Condition(Bankcard.class);
			Criteria criteria = c.createCriteria();
			criteria.andEqualTo("userId",tokenUser.getId());
			List<Bankcard> bankcards = bankcardService.findByCondition(c);
			if (CollUtil.isNotEmpty(bankcards)){
				return ResultGenerator.genFailResult(ResultCode.BANKCARD_IS_HAVE);
			}
			bankcard.setUserId(tokenUser.getId());
    		bankcard.setCreateTime(DateUtil.date());
    		bankcardService.save(bankcard);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success();
    }

    @RequestMapping("/delete")
	@ApiOperation(value = "bankcard删除",httpMethod = "GET")
    public Result delete(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	try {
    		bankcardService.deleteById(id);
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @PostMapping("/update")
	@ApiOperation(value = "bankcard更新",httpMethod = "POST")
    public Result update(@RequestBody Bankcard bankcard) {
    	if(bankcard == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	if(bankcard.getId() == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	try {
    //		bankcard.setUpdateTime(new Date());
    //		bankcard.setUpdateUserId(userId);
    		bankcardService.update(bankcard);
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @RequestMapping("/detail")
	@ApiOperation(value = "获取银行卡详情",httpMethod = "GET")
    public Result<Bankcard> detail(HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		Condition condition = new Condition(Bankcard.class);
		Criteria criteria = condition.createCriteria();
		criteria.andEqualTo("userId",tokenUser.getId());
		try {
			List<Bankcard> list = bankcardService.findByCondition(condition);
			if (CollUtil.isEmpty(list)){
				return Result.success();
			}
			return Result.success(list.get(0));
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}

    }

	@RequestMapping("/detailByUserId")
	@ApiOperation(value = "获取银行卡详情通过用户id",httpMethod = "GET")
	public Result<Bankcard> detailByUserId(@RequestParam Integer userId) {
		User u = userService.findById(userId);
		if (u == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		Condition condition = new Condition(Bankcard.class);
		Criteria criteria = condition.createCriteria();
		criteria.andEqualTo("userId",u.getId());
		try {
			List<Bankcard> list = bankcardService.findByCondition(condition);
			if (CollUtil.isEmpty(list)){
				return Result.success();
			}
			return Result.success(list.get(0));
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}

	}

    @RequestMapping("/list")
	@ApiOperation(value = "bankcard获取列表",httpMethod = "POST")
    public Result<List<Bankcard>> list(HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
        Condition condition = new Condition(Bankcard.class);
        Criteria criteria = condition.createCriteria();
		criteria.andEqualTo("userId",tokenUser.getId());
		try {
    		 List<Bankcard> list = bankcardService.findByCondition(condition);
			return Result.success(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
    }
}
