package com.project.web.socket;

public class 直播 {

    // 编写一个Java程序，实现直播功能。
    public static void main(String[] args) {
        /**
         *  最近一年期间，完成2套链游，完成3套游戏应用，
         *  完成1套学生实习代签到系统，
         *  完成1套运满m转单系统，完成1套抖音小玩法系统，
         *  完成1套学生毕设
         */
        String s = "本人十三年java软件开发，七年技术总监，" +
                "待过大厂，也呆过初创公司， 擅长Java后端开发，" +
                "微服务架构，独立负责过3000-4000万用户系统，" +
                "日活300万";
        /**
         *
         *  上麦连线    聊聊你的第n桶金
         *
         *
         *
         *  我可以投钱，投技术，你出点子或者流量，
         *  只要你能说服我，我免费给你开发这款app
         */
        String s1 =   "你有任何小程序/app/网站定制开发、" +
                "兼职问题都可以上麦连线";

        StringBuilder sb = new StringBuilder();
        sb.append(s);
        sb.append(s1);
        System.out.println(sb.toString());
    }



}
