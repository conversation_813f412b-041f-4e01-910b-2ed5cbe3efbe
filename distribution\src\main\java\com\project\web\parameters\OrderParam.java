package com.project.web.parameters;

import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Data
@ApiModel(value="创建订单入参")
public class OrderParam extends BaseBeen implements Serializable {




    @ApiModelProperty(value="货币类型 1微信 2支付宝 3usdt 4线下")
    private Integer coinType;

    @ApiModelProperty(value="wxAppId")
    private String wxAppId;

    /**
     * 支付类型 (1.APP支付2.扫码支付3.H5网页支付,4-JSAPI支付)
     */
    @ApiModelProperty(value="支付类型 (1.APP支付2.扫码支付3.H5网页支付,4-JSAPI支付) ")
    private Integer payModel;

    /**
     * 用户id
     */
    @ApiModelProperty(value="积分抵扣类型 1不抵扣 2抵扣")
    private Integer scoreType;

    /**
     * 商品id
     */
    @ApiModelProperty(value="goodsId商品id")
    private Integer goodsId;

    @ApiModelProperty(value="地址id")
    private Integer addressId;

    private static final long serialVersionUID = 1L;


}