package com.project.web;
import cn.hutool.core.util.StrUtil;
import com.project.core.NoToken;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.enums.CarTypeEnum;
import com.project.enums.FreightTypeEnum;
import com.project.model.Area;
import com.project.service.AreaService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.project.web.parameters.AreaParam;
import com.project.web.parameters.EnumBo;
import io.swagger.annotations.*;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
* Created by CodeGenerator on 2024/11/28.
*/
@Api(tags = "area管理")
@RestController
@RequestMapping("/area")
public class AreaController {

	private static Logger log = LoggerFactory.getLogger(AreaController.class);

    @Resource
    private AreaService areaService;


	@GetMapping("/getEnum")
	@ApiOperation(value = "获取枚举(1车类型 2货物类型)",httpMethod = "GET")
	public Result<List<EnumBo>> getEnum(@RequestParam Integer id) {
		if(id == null){
			id = 1;
		}
		switch (id){
			case 1:
				return ResultGenerator.genSuccessResult(Arrays.stream(CarTypeEnum.values())
						.map(temp -> EnumBo.builder().code(temp.getKey()).name(temp.getCode()).build()).collect(Collectors.toList()));
			case 2:
				return ResultGenerator.genSuccessResult(Arrays.stream(FreightTypeEnum.values())
						.map(temp -> EnumBo.builder().code(temp.getKey()).name(temp.getCode()).build()).collect(Collectors.toList()));

		}
		return ResultGenerator.genSuccessResult();
	}


	@NoToken
	@ApiOperation(value = "list区域列表，不分页")
	@ApiResponses(@ApiResponse(code = 200, message = "success"))
	@PostMapping("/list")
	public Result<List<Area>> list(@RequestBody AreaParam param) {
		Condition condition = new Condition(Area.class);
		Criteria criteria = condition.createCriteria();
		if (StrUtil.isNotBlank(param.getAreaParentCode())) {
			criteria.andEqualTo("areaParentCode", param.getAreaParentCode());
		} else {
			// 没有传条件,查出所有省
			criteria.andEqualTo("areaParentCode", "1");
		}
		List<Area> list = areaService.findByCondition(condition);
		return Result.success(list);
	}

	@NoToken
	@ApiOperation(value = "China获取中国区域所有的层级信息")
	@ApiResponses(@ApiResponse(code = 200, message = "success"))
	@GetMapping("China")
	public Result<Area> China() {
		// TODO 做缓存
		List<Area> list = areaService.findAll();
		Area areaVo = assemblyArea(list, "6").get("1");
		return Result.success(areaVo);
	}

	/**
	 * @param lessThanType 类型小于XX的
	 * @return
	 */
	@NoToken
	@ApiOperation(value = "China/init获取中国区域某个类型的层级信息")
	@ApiImplicitParam(name = "lessThanType", value = "小于哪个类型 1国家2省3市4区5街道", required = true, paramType = "query", dataType = "string")
	@ApiResponses(@ApiResponse(code = 200, message = "success"))
	@GetMapping("China/init")
	public Result<Area> ChinaInit(@RequestParam String lessThanType) {
		Condition condition = new Condition(Area.class);
		Criteria criteria = condition.createCriteria();
		criteria.andLessThan("areaType", lessThanType);
		List<Area> list = areaService.findByCondition(condition);
		Area areaVo = assemblyArea(list, lessThanType).get("1");
		return Result.success(areaVo);
	}

	@NoToken
	@ApiOperation(value = "通过code获取地址")
	@GetMapping("getByCode")
	public Result<String> getByCode(@RequestParam String code) {
		return Result.success(areaService.getAreaName(code));
	}



	/**
	 * 组装上下层级
	 *
	 * @param list
	 * @return
	 */
	private Map<String, Area> assemblyArea(List<Area> list, String lessThanType) {
		Map<String, Area> temp = new HashMap(list.size());
		int parentMapInitialCapacity = 2;
		switch (lessThanType) {
			case "4":
				parentMapInitialCapacity = 35;
				break;
			case "5":
				parentMapInitialCapacity = 512;
				break;
			case "6":
				parentMapInitialCapacity = 3500;
				break;
		}
		Map<String, List<Area>> parentTemps = new HashMap(parentMapInitialCapacity);
		for (Area area : list) {
			temp.put(area.getAreaCode(), area);
			// 优化性能  4s+ ~ 3s+ 数据量大,需要缓存
			List<Area> areaVos = parentTemps.get(area.getAreaParentCode());
			if (null == areaVos) {
				areaVos = new ArrayList<>();
			}
			areaVos.add(area);
			parentTemps.put(area.getAreaParentCode(), areaVos);
		}
		for (String parentCode : parentTemps.keySet()) {
			List<Area> childrenAreaVos = parentTemps.get(parentCode);
			if (null != childrenAreaVos) {
				Area areaVo = temp.get(parentCode);
				if (null != areaVo) {
					areaVo.setChildren(childrenAreaVos);
				}
			}
		}

		// for (AreaVo area : list) {
		//     AreaVo p = temp.get(area.getAreaParentCode());
		//     if (p == null) {
		//         continue;
		//     }
		//     if (CollUtil.isEmpty(p.getChildren())) {
		//         p.setChildren(new ArrayList());
		//     }
		//     p.getChildren().add(area);
		// }
		return temp;
	}
}
