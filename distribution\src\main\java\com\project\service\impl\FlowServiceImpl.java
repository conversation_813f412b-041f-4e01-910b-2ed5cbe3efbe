package com.project.service.impl;

import com.project.dao.FlowMapper;
import com.project.model.Flow;
import com.project.service.FlowService;
import com.project.core.AbstractService;
import com.project.web.parameters.ValBo;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * Created by CodeGenerator on 2025/05/08.
 */
@Service
@Transactional
public class FlowServiceImpl extends AbstractService<Flow> implements FlowService {
    @Resource
    private FlowMapper cFlowMapper;

    /**
     * type :0直推人数 1直推有效人数 2直推收益 3成团奖励 4成团分佣
     * @param userId
     * @param type
     * @return
     */
    @Override
    public List<ValBo> statistic(Integer userId, Integer type) {
        //业务类型 1、直推分佣 2成团奖励 3成团分佣 4购物抵扣 5参与活动新增上限 6提现
        List<ValBo> ret = new ArrayList<>();
        if (type == 1){
            ret = cFlowMapper.statisticByType(userId,1);
        } else if (type == 2){
            ret = cFlowMapper.statisticAmountByType(userId,1);
        } else if (type == 3){
            ret = cFlowMapper.statisticAmountByType(userId,2);
        }else if (type == 4){
            ret = cFlowMapper.statisticAmountByType(userId,3);
        }
        return ret;
    }
}
