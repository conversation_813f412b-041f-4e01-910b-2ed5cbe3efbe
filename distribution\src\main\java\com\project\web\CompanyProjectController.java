package com.project.web;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.model.CompanyProject;
import com.project.service.CompanyProjectService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.util.List;

/**
* Created by CodeGenerator on 2025/05/07.
*/
@Api(tags = "companyProject管理")
@RestController
@RequestMapping("/company/project")
public class CompanyProjectController {

	private static Logger log = LoggerFactory.getLogger(CompanyProjectController.class);

    @Resource
    private CompanyProjectService companyProjectService;

    @PostMapping("/add")
	@ApiOperation(value = "companyProject新增",httpMethod = "POST")
    public Result add(@RequestBody CompanyProject companyProject) {
    	if(companyProject == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	try {
    //		companyProject.setCreateTime(new Date());
    //		companyProject.setCreateUserId(userId);
    		companyProjectService.save(companyProject);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success();
    }

    @RequestMapping("/delete")
	@ApiOperation(value = "companyProject删除",httpMethod = "GET")
    public Result delete(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	try {
    		companyProjectService.deleteById(id);
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @PostMapping("/update")
	@ApiOperation(value = "companyProject更新",httpMethod = "POST")
    public Result update(@RequestBody CompanyProject companyProject) {
    	if(companyProject == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	if(companyProject.getId() == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	try {
    //		companyProject.setUpdateTime(new Date());
    //		companyProject.setUpdateUserId(userId);
    		companyProjectService.update(companyProject);
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @RequestMapping("/detail")
	@ApiOperation(value = "companyProject获取详情",httpMethod = "GET")
    public Result<CompanyProject> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	CompanyProject companyProject = null;
    	try {
    		companyProject = companyProjectService.findById(id);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success(companyProject);
    }

    @RequestMapping("/list")
	@ApiOperation(value = "companyProject获取列表",httpMethod = "POST")
    public Result<List<CompanyProject>> list(@RequestBody CompanyProject companyProject) {

 //       PageHelper.startPage(page, size);
        
        Condition condition = new Condition(companyProject.getClass());
        Criteria criteria = condition.createCriteria();
//        criteria.andEqualTo("name", city.getName());
		PageInfo pageInfo = null;
		try {
    		 List<CompanyProject> list = companyProjectService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
