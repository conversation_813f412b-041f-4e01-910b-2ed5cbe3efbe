package com.project.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.project.core.*;
import com.project.model.*;
import com.project.service.*;
import com.project.web.parameters.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

/**
 * Created by CodeGenerator on 2019/11/01.
 */
@Api(tags = "[app]登录管理")
@RestController
@RequestMapping("/login")
public class LoginController {

    private static Logger log = LoggerFactory.getLogger(LoginController.class);

    @Resource
    private UserService userService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private UserUtil userUtil;

    @Resource
    private PermissionService permissionService;

    @Resource
    private CompanyService companyService;

    @Resource
    private RoleService roleService;

    @Resource
    private UserRoleService userRoleService;

    @Resource
    private FlowService flowService;

    @Resource
    private CodeLibraryService codeLibraryService;

    @NoToken
    @GetMapping("/codeLibrary")
    @ApiOperation(value = "获取配置",httpMethod = "GET")
    public Result<CodeLibrary> codeLibrary(@RequestParam String code) {
        return ResultGenerator.genSuccessResult(codeLibraryService.findBy("code",code));
    }
    @NoToken
    @PostMapping("/login")
    @ApiOperation(value = "app登录",httpMethod = "POST")
    public Result<UserBO> add(@RequestBody LoginAccountParameters loginParameters) {
        if(loginParameters == null){
            return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
        }
        if(StrUtil.isBlank(loginParameters.getAccount())){
            return ResultGenerator.genFailResult(ResultCode.ACCOUNT_IS_NULL);
        }
        Condition condition = new Condition(User.class);
        condition.createCriteria().andEqualTo("loginAccount",loginParameters.getAccount());
        List<User> users = userService.findByCondition(condition);
        if(CollUtil.isEmpty(users)){
            return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
        }
        if(users.size() != 1){
            return ResultGenerator.genFailResult(ResultCode.USER_IS_ERROR);
        }
        User user = users.get(0);
        if(StrUtil.equals("1",user.getStatus())){
            return ResultGenerator.genFailResult(ResultCode.LOGIN_USER_STATUS_IS_ERROR);
        }
        String pwd2 = SecureUtil.md5(loginParameters.getPwd() + user.getSalt()).toUpperCase();
        log.error("[密码]"+loginParameters.getAccount() + " :"+pwd2);
        //		String pwd2 = MD5Util.getMD5((loginParameters.getPwd() + user.getSalt()).toUpperCase());
        if (StrUtil.equals(user.getPassword().toUpperCase(), pwd2)) {
            String token = StrUtil.uuid();
            CacheKey key = CacheKey.generateKey(CacheType.UserLogin, token);
            user.setToken(token);
            user.setType(loginParameters.getType());
            //设置redis缓存1小时
            redisUtil.set(key.toString(), JSONUtil.toJsonStr(user), 7, TimeUnit.DAYS);
            user.setPassword("***");
            user.setSalt("***");
            user.setTranspwd("***");
            UserBO bo = BeanUtil.toBean(user,UserBO.class);
            if (user.getPid() != null){
                User p = userService.findById(user.getPid());
                if (p!=null){
                    bo.setReferrer(p.getName());
                }
            }
            if (user.getCompanyId() != null){
                Company company = companyService.findById(user.getCompanyId());
                bo.setCompany(company);
            }
            Condition c = new Condition(Flow.class);
            c.createCriteria().andEqualTo("userId",user.getId()).andEqualTo("bizType",9)
                    .andGreaterThanOrEqualTo("createTime", DateUtil.today());
            List<Flow> flows = flowService.findByCondition(c);
            if (CollUtil.isNotEmpty(flows)){
                bo.setSignStatus(2);
            } else {
                bo.setSignStatus(1);
            }
            return ResultGenerator.genSuccessResult(bo);
        } else {
            return ResultGenerator.genFailResult(ResultCode.UNLOGIN_PWD_ERROR);
        }
    }


    @NoToken
    @PostMapping("/login2")
    @ApiOperation(value = "后台登录",httpMethod = "POST")
    public Result<UserBO> login2(@RequestBody LoginAccountParameters loginParameters) {
        if(loginParameters == null){
            return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
        }
        if(StrUtil.isBlank(loginParameters.getAccount())){
            return ResultGenerator.genFailResult(ResultCode.ACCOUNT_IS_NULL);
        }
        Condition condition = new Condition(User.class);
        condition.createCriteria().andEqualTo("loginAccount",loginParameters.getAccount());
        List<User> users = userService.findByCondition(condition);
        if(CollUtil.isEmpty(users)){
            return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
        }
        if(users.size() != 1){
            return ResultGenerator.genFailResult(ResultCode.USER_IS_ERROR);
        }
        User user = users.get(0);
        Condition c2 = new Condition(UserRole.class);
        c2.createCriteria().andEqualTo("roleId",1);
        List<UserRole> roles = userRoleService.findByCondition(c2);
        if (CollUtil.isEmpty(roles)){
            return ResultGenerator.genFailResult(ResultCode.USER_IS_NOT_ADMIN);
        }
        if(StrUtil.equals("1",user.getStatus())){
            return ResultGenerator.genFailResult(ResultCode.LOGIN_USER_STATUS_IS_ERROR);
        }
        String pwd2 = SecureUtil.md5(loginParameters.getPwd() + user.getSalt()).toUpperCase();
        log.error("[密码]"+loginParameters.getAccount() + " :"+pwd2);
        //		String pwd2 = MD5Util.getMD5((loginParameters.getPwd() + user.getSalt()).toUpperCase());
        if (StrUtil.equals(user.getPassword().toUpperCase(), pwd2)) {
            String token = StrUtil.uuid();
            CacheKey key = CacheKey.generateKey(CacheType.UserLogin, token);
            user.setToken(token);
            user.setType(loginParameters.getType());
            //设置redis缓存1小时
            redisUtil.set(key.toString(), JSONUtil.toJsonStr(user), 7, TimeUnit.DAYS);
            user.setPassword("***");
            user.setSalt("***");
            user.setTranspwd("***");
            UserBO bo = BeanUtil.toBean(user,UserBO.class);
            if (user.getPid() != null){
                User p = userService.findById(user.getPid());
                if (p!=null){
                    bo.setReferrer(p.getName());
                }
            }
            if (user.getCompanyId() != null){
                Company company = companyService.findById(user.getCompanyId());
                bo.setCompany(company);
            }
            Condition c = new Condition(Flow.class);
            c.createCriteria().andEqualTo("userId",user.getId()).andEqualTo("bizType",9)
                    .andGreaterThanOrEqualTo("createTime", DateUtil.today());
            List<Flow> flows = flowService.findByCondition(c);
            if (CollUtil.isNotEmpty(flows)){
                bo.setSignStatus(2);
            } else {
                bo.setSignStatus(1);
            }
            return ResultGenerator.genSuccessResult(bo);
        } else {
            return ResultGenerator.genFailResult(ResultCode.UNLOGIN_PWD_ERROR);
        }
    }


    @NoToken
    @PostMapping("/register")
    @ApiOperation(value = "注册",httpMethod = "POST")
    public Result register(@RequestBody RegisterAccountParameters params) {
        if(params == null){
            return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
        }
        if(StrUtil.isBlank(params.getAccount())){
            return ResultGenerator.genFailResult(ResultCode.ACCOUNT_IS_NULL);
        }
        Condition condition = new Condition(User.class);
        condition.createCriteria().andEqualTo("loginAccount",params.getAccount());
        List<User> users = userService.findByCondition(condition);
        if(CollUtil.isNotEmpty(users)){
            return ResultGenerator.genFailResult(ResultCode.USER_IS_EXIST);
        }
        String salt = RandomUtil.randomString(4);
        String pwd2 = SecureUtil.md5(params.getPwd() + salt).toUpperCase();
        log.error("[密码]"+params.getAccount() + " :"+pwd2);

        String code = getInviteCode(0);
        if (code == null){
            return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
        }
        User user = User.builder().createUserId("sys").createTime(DateUtil.date()).inviteCode(code)
                .name(params.getAccount()).companyId(params.getCompanyId())
                .transpwd(params.getTranspwd()).amount(BigDecimal.ZERO)
                .withdrawAmount(BigDecimal.ZERO).freezeAmount(BigDecimal.ZERO)
                .icon("https://pasture-**********.cos.ap-shanghai.myqcloud.com/yc/head.jpg")
                .status("0") .loginAccount(params.getAccount()).password(pwd2).salt(salt).build();


        if (StrUtil.isNotBlank(params.getPhone())){
            user.setPhoneNo(params.getPhone());
        }
        if (StrUtil.isNotBlank(params.getName())){
            user.setName(params.getName());
        }
        if (StrUtil.isNotBlank(params.getAddress())){
            user.setAddress(params.getAddress());
        }
        if (StrUtil.isNotBlank(params.getWorkNo())){
            user.setWorkNo(params.getWorkNo());
        }
        String roleIds = "";
        if (params.getType() == 1){
            roleIds = "2";
        } else if (params.getType() == 2){
            roleIds = "3";
        }
        user.setVipLevel(1);
        userService.saveAndBandRole(user,roleIds,params.getCode());
        return ResultGenerator.genSuccessResult(user);
    }

    private String getInviteCode(int index) {
        if (index > 10000){
            return null;
        }
        String s = RandomUtil.randomString(8);
        User inviteCode = userService.findBy("inviteCode", s);
        if (inviteCode == null){
            return s;
        }
        return getInviteCode(index + 1);
    }


    /**
     * 通过userId获取菜单，需要TOKEN的.
     *
     */
    @ApiOperation(value = "获取用户菜单",httpMethod = "GET")
    @RequestMapping(value = "/queryMenu", method = { RequestMethod.GET, RequestMethod.POST })
    public Result<List<PermissionBo>> queryMenu(HttpServletRequest request) {
        //校验权限
        try {
            User tokenUser = userUtil.getTokenUser(request);
            if (tokenUser == null){
                return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
            }
            List<Permission> ps = permissionService.selectEenu(tokenUser.getId() + "");

            List<PermissionBo> bos = new LinkedList<PermissionBo>();
            for (Permission permission : ps) {
                PermissionBo t = new PermissionBo();
                BeanUtils.copyProperties( permission,t);
                bos.add(t);
            }
            bos = assemblyChildren(bos);
            return ResultGenerator.genSuccessResult(bos);
        } catch (Exception e) {
            log.error("获取菜单异常e:{}", e);
            return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
        }

    }

    /**
     * 组装资源组
     *
     * @param rs
     * @return
     */
    private static List<PermissionBo> assemblyChildren(List<PermissionBo> rs) {
        if (rs == null) {
            return null;
        }
        List<PermissionBo> temp = new LinkedList<>();
        temp.addAll(rs);
        List<PermissionBo> ret = new LinkedList<>();
        // 组装一个map
        Map<String, PermissionBo> tempMap = new TreeMap<>();
        for (PermissionBo r : temp) {
            tempMap.put(r.getId() + "", r);
        }
        for (PermissionBo r : temp) {
            if (StrUtil.isNotBlank(r.getParentId())) {
                PermissionBo parent = tempMap.get(r.getParentId());
                if (parent != null) {
                    List<PermissionBo> children = parent.getChildren();
                    if (children == null) {
                        children = new LinkedList<>();
                    }
                    children.add(tempMap.get(r.getId() + ""));
                    parent.setChildren(children);
                }
            }
        }
        for (PermissionBo r : temp) {
            if (StrUtil.isBlank(r.getParentId())) {
                ret.add(tempMap.get(r.getId() + ""));
            }
        }
        return ret;
    }




}
