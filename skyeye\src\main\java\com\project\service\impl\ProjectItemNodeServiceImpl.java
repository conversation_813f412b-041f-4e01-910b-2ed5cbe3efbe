package com.project.service.impl;

import com.project.dao.ProjectItemNodeMapper;
import com.project.model.ProjectItemNode;
import com.project.service.ProjectItemNodeService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/01/25.
 */
@Service
@Transactional
public class ProjectItemNodeServiceImpl extends AbstractService<ProjectItemNode> implements ProjectItemNodeService {
    @Resource
    private ProjectItemNodeMapper bProjectItemNodeMapper;

}
