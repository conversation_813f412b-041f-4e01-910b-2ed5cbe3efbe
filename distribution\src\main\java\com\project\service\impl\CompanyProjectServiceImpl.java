package com.project.service.impl;

import com.project.dao.CompanyProjectMapper;
import com.project.model.CompanyProject;
import com.project.service.CompanyProjectService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/05/07.
 */
@Service
@Transactional
public class CompanyProjectServiceImpl extends AbstractService<CompanyProject> implements CompanyProjectService {
    @Resource
    private CompanyProjectMapper bCompanyProjectMapper;

}
