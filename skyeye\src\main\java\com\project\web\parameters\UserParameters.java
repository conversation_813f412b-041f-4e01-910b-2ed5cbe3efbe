package com.project.web.parameters;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 */
@Data
@ApiModel("账号参数")
public class UserParameters {

    @ApiModelProperty(name="account",value = "登录账号",required=true)
    private String account;

    @ApiModelProperty(name="pwd",value = "登录密码",required=true)
    private String pwd;

    @ApiModelProperty(name="roleIds",value = "角色ids",required=true)
    private String roleIds;


    @ApiModelProperty(value="name姓名")
    private String name;

    @ApiModelProperty(value="地址")
    private String address;

    @ApiModelProperty(value="phoneNo手机号码")
    private String phoneNo;

    @ApiModelProperty(value="工号")
    private String workNo;

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
