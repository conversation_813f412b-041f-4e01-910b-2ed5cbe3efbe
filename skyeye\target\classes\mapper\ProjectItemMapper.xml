<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.ProjectItemMapper">
  <resultMap id="BaseResultMap" type="com.project.model.ProjectItem">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user_id" jdbcType="INTEGER" property="createUserId" />
    <result column="principal_user_id" jdbcType="INTEGER" property="principalUserId" />
    <result column="update_user_id" jdbcType="INTEGER" property="updateUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="days" jdbcType="INTEGER" property="days" />
    <result column="score" jdbcType="INTEGER" property="score" />
    <result column="audit" jdbcType="VARCHAR" property="audit" />
    <result column="audit_time" jdbcType="TIMESTAMP" property="auditTime" />
    <result column="audit_type" jdbcType="INTEGER" property="auditType" />
    <result column="audit_ret" jdbcType="INTEGER" property="auditRet" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
  </resultMap>
</mapper>