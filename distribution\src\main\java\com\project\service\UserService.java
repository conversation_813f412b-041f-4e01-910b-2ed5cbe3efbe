package com.project.service;
import com.project.model.User;
import com.project.core.Service;
import com.project.web.parameters.ValBo;

import java.math.BigDecimal;
import java.util.List;


/**
 * Created by CodeGenerator on 2024/04/08.
 */
public interface UserService extends Service<User> {

    void saveAndBandRole(User user, String roleIds, String code);

    void saveAndUpdateOpenUser(User user, String outUserId, String outPid);

    void updateAndBandRole(User user, String roleIds);

    boolean check(User user);

    /**
     * 货币交易
     * @param userId
     * @param bizType 业务类型  1、直推分佣 2成团奖励 3成团分佣 4购物抵扣 5参与活动新增上限 6提现 7冻结 8解冻 9签到
     * @param type 1积分 2活动金额 3活动金额上限 4已提现 5每日产出 6提现金额
     * @param num
     * @return
     */
    Boolean deal(Integer userId, Integer bizType, Byte type, BigDecimal num, Long bizId);

    /**
     * 统计 推广人数
     * @param id
     * @return
     */
    List<ValBo> statistic(Integer id);
}
