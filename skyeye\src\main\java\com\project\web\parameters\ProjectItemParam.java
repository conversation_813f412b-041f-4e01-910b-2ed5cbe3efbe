package com.project.web.parameters;

import com.project.core.BaseBeen;
import com.project.model.ProjectItemMember;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

@Data
@ApiModel(value="项目明细入参")
public class ProjectItemParam extends BaseBeen implements Serializable {

    /**
     * 项目id
     */
    @ApiModelProperty(value="projectId项目id")
    private Integer projectId;

    @ApiModelProperty(value="amount预计金额")
    private BigDecimal amount;


    @ApiModelProperty(value="明细负责人")
    private Integer principalUserId;


    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    /**
     * 类型 1泥工 2木工 3瓷砖 4水电
     */
    @ApiModelProperty(value="type类型 1泥工 2木工 3瓷砖 4水电")
    private Integer type;

    /**
     * 状态 0未开始  1进行中 2待审核 3整改中 4已完成
     */
    @ApiModelProperty(value="status状态 0未开始  1进行中 2待审核 3整改中 4已完成")
    private Integer status;


    /**
     * 开始时间
     */
    @ApiModelProperty(value="beginTime开始时间")
    private Date beginTime;

    /**
     * 预计完成时间
     */
    @ApiModelProperty(value="finishTime预计完成时间")
    private Date finishTime;

    /**
     * 预计天数
     */
    @ApiModelProperty(value="days预计天数")
    private Integer days;


    @ApiModelProperty(value="项目明细成员")
    private List<ProjectItemMember> members;


    private static final long serialVersionUID = 1L;


}