<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.UserMapper">
  <resultMap id="BaseResultMap" type="com.project.model.User">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="login_account" jdbcType="VARCHAR" property="loginAccount" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="invite_code" jdbcType="VARCHAR" property="inviteCode" />
    <result column="pid" jdbcType="INTEGER" property="pid" />
    <result column="vip_level" jdbcType="INTEGER" property="vipLevel" />

    <result column="work_no" jdbcType="VARCHAR" property="workNo" />
    <result column="salt" jdbcType="VARCHAR" property="salt" />
    <result column="phone_no" jdbcType="VARCHAR" property="phoneNo" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />




  </resultMap>

</mapper>