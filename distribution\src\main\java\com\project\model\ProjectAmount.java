package com.project.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(value="com.project.model.ProjectAmount")
@Table(name = "c_project_amount")
public class ProjectAmount implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 项目id
     */
    @Column(name = "project_id")
    @ApiModelProperty(value="projectId项目id")
    private Integer projectId;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty(value="userId用户id")
    private Integer userId;

    /**
     * 公司id
     */
    @Column(name = "company_id")
    @ApiModelProperty(value="companyId公司id")
    private Integer companyId;

    /**
     * 限额
     */
    @Column(name = "activity_limit")
    @ApiModelProperty(value="activityLimit限额")
    private BigDecimal activityLimit;

    /**
     * 已获得金额
     */
    @Column(name = "activity_amount")
    @ApiModelProperty(value="activityAmount已获得金额")
    private BigDecimal activityAmount;

    /**
     * 成团次数
     */
    @Column(name = "activity_num")
    @ApiModelProperty(value="activityNum成团次数")
    private Integer activityNum;

    /**
     * 每日产出
     */
    @Column(name = "activity_output")
    @ApiModelProperty(value="activityOutput每日产出")
    private BigDecimal activityOutput;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取项目id
     *
     * @return project_id - 项目id
     */
    public Integer getProjectId() {
        return projectId;
    }

    /**
     * 设置项目id
     *
     * @param projectId 项目id
     */
    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    /**
     * 获取用户id
     *
     * @return user_id - 用户id
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * 设置用户id
     *
     * @param userId 用户id
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取公司id
     *
     * @return company_id - 公司id
     */
    public Integer getCompanyId() {
        return companyId;
    }

    /**
     * 设置公司id
     *
     * @param companyId 公司id
     */
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    /**
     * 获取限额
     *
     * @return activity_limit - 限额
     */
    public BigDecimal getActivityLimit() {
        return activityLimit;
    }

    /**
     * 设置限额
     *
     * @param activityLimit 限额
     */
    public void setActivityLimit(BigDecimal activityLimit) {
        this.activityLimit = activityLimit;
    }

    /**
     * 获取已获得金额
     *
     * @return activity_amount - 已获得金额
     */
    public BigDecimal getActivityAmount() {
        return activityAmount;
    }

    /**
     * 设置已获得金额
     *
     * @param activityAmount 已获得金额
     */
    public void setActivityAmount(BigDecimal activityAmount) {
        this.activityAmount = activityAmount;
    }



    /**
     * 获取每日产出
     *
     * @return activity_output - 每日产出
     */
    public BigDecimal getActivityOutput() {
        return activityOutput;
    }

    /**
     * 设置每日产出
     *
     * @param activityOutput 每日产出
     */
    public void setActivityOutput(BigDecimal activityOutput) {
        this.activityOutput = activityOutput;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}