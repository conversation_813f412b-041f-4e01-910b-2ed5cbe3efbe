package com.project.service.impl;

import com.project.dao.ProjectItemMemberMapper;
import com.project.model.ProjectItemMember;
import com.project.service.ProjectItemMemberService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/01/22.
 */
@Service
@Transactional
public class ProjectItemMemberServiceImpl extends AbstractService<ProjectItemMember> implements ProjectItemMemberService {
    @Resource
    private ProjectItemMemberMapper bProjectItemMemberMapper;

}
