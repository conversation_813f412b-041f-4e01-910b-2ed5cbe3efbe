package com.project.web.parameters;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.project.model.Flow;
import com.project.model.ProjectAmount;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

@Data
@ApiModel(value="项目")
public class ProjectBO implements Serializable {
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 公司id
     */
    @ApiModelProperty(value="companyId公司id")
    private Integer companyId;

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    /**
     * 类型 1直推 2成团
     */
    @ApiModelProperty(value="type类型 1直推 2成团 ")
    private Integer type;

    @ApiModelProperty(value="图片")
    private String pic;

    /**
     * 状态 0未开始  1进行中 2待审核 3已完成
     */
    @ApiModelProperty(value="status状态 0未开始  1进行中 2待审核 3已完成")
    private Integer status;

    /**
     * 用户id
     */
    @ApiModelProperty(value="userId用户id")
    private Integer userId;

    /**
     * 创建人
     */
    @ApiModelProperty(value="createUserId创建人")
    private Integer createUserId;

    /**
     * 更新人
     */
    @ApiModelProperty(value="updateUserId更新人")
    private Integer updateUserId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value="beginTime开始时间")
    private Date beginTime;

    /**
     * 预计完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value="finishTime预计完成时间")
    private Date finishTime;

    /**
     * 返佣
     */
    @ApiModelProperty(value="返佣")
    private BigDecimal amount;

    @ApiModelProperty(value="description描述")
    private String description;

    @ApiModelProperty(value="席位")
    private List<ProjectTeamBO> teams;

    @ApiModelProperty(value="流水")
    private List<Flow> flows;

    @ApiModelProperty(value="项目金额")
    private ProjectAmount projectAmount;


    private static final long serialVersionUID = 1L;


}