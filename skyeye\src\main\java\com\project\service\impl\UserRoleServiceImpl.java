package com.project.service.impl;

import com.project.dao.UserRoleMapper;
import com.project.model.UserRole;
import com.project.service.UserRoleService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2024/04/08.
 */
@Service
@Transactional
public class UserRoleServiceImpl extends AbstractService<UserRole> implements UserRoleService {
    @Resource
    private UserRoleMapper bUserRoleMapper;

    @Override
    public void deleteByCondition(Condition condition) {
        bUserRoleMapper.deleteByCondition(condition);
    }
}
