package com.project.web;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.model.Goods;
import com.project.service.GoodsService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.util.List;

/**
* Created by CodeGenerator on 2025/04/25.
*/
@Api(tags = "商品管理")
@RestController
@RequestMapping("/goods")
public class GoodsController {

	private static Logger log = LoggerFactory.getLogger(GoodsController.class);

    @Resource
    private GoodsService goodsService;

    @PostMapping("/add")
	@ApiOperation(value = "goods新增",httpMethod = "POST")
    public Result add(@RequestBody Goods goods) {
    	if(goods == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	try {
    //		goods.setCreateTime(new Date());
    //		goods.setCreateUserId(userId);
    		goodsService.save(goods);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success();
    }

    @RequestMapping("/delete")
	@ApiOperation(value = "goods删除",httpMethod = "GET")
    public Result delete(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	try {
    		goodsService.deleteById(id);
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @PostMapping("/update")
	@ApiOperation(value = "goods更新",httpMethod = "POST")
    public Result update(@RequestBody Goods goods) {
    	if(goods == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	if(goods.getId() == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	try {
    //		goods.setUpdateTime(new Date());
    //		goods.setUpdateUserId(userId);
    		goodsService.update(goods);
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @RequestMapping("/detail")
	@ApiOperation(value = "goods获取详情",httpMethod = "GET")
    public Result<Goods> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	Goods goods = null;
    	try {
    		goods = goodsService.findById(id);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success(goods);
    }

    @RequestMapping("/list")
	@ApiOperation(value = "goods获取列表",httpMethod = "POST")
    public Result<List<Goods>> list(@RequestBody Goods goods) {

        PageHelper.startPage(goods.getPage(), goods.getSize());
        
        Condition condition = new Condition(goods.getClass());
        Criteria criteria = condition.createCriteria();
		criteria.andEqualTo("companyId", goods.getCompanyId());
		criteria.andEqualTo("status", 1);
		if (goods.getType() != null){
			criteria.andEqualTo("type", goods.getType());
		}
		PageInfo pageInfo = null;
		try {
    		 List<Goods> list = goodsService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
