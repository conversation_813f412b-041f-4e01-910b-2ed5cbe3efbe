//package com.project.web.socket;
//
//import com.corundumstudio.socketio.SocketIOClient;
////import io.swagger.annotations.ApiModel;
////import io.swagger.annotations.ApiModelProperty;
////import com.youyou.circle.data.CircleUserInfoData;
//import lombok.*;
//
//
///**
// * 用户对象
// * <AUTHOR>
// * @date ：Created in 2020/7/9 13:40
// */
//@Builder
//@AllArgsConstructor
//@NoArgsConstructor
//@Getter
//@Setter
///**
// * socket用户对象
// */
//public class SocketUserBo {
//
//    /**
//     * 状态 1未举手 2已举手
//     */
////    @ApiModelProperty("状态 1未举手 2已举手")
//    private Integer status;
//
//    /**
//     *
//     */
////    @ApiModelProperty("状态 1一对一聊天 2群聊 ")
//    private Integer type;
//
//    //    @ApiModelProperty("状态 1管理员 2成员 ")
//    private Integer memberType;
//
////    private CircleUserInfoData userInfo;
//
////    private YyMobileUser user;
//
////    @ApiModelProperty("用户id")
//    private String userId;
//
////    @ApiModelProperty("用户客户端id")
//    private String clientId;
//
////    @ApiModelProperty("client")
//    private SocketIOClient client;
//
////    @ApiModelProperty("名字")
//    private String name;
//
//    /**
//     * 头像
//     */
////    @ApiModelProperty("头像")
//    private String headPic;
//
////    @ApiModelProperty("状态 1是 2否 ")
//    private Integer isHospital;
//
//
//}