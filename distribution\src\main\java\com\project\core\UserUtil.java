package com.project.core;

import cn.hutool.json.JSONUtil;
import com.project.model.User;
import com.project.service.UserService;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * @Date: 2019/10/10 10:12
 * @Version 1.0
 */
@Component
public class UserUtil {

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private UserService userService;


    public User getTokenUser(HttpServletRequest request) {
        if (request == null){
            ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (null != sra) {
                request = sra.getRequest();
            } else {
                return null;
            }
        }
        String token = request.getHeader("accessToken");
        CacheKey key = CacheKey.generateKey(CacheType.UserLogin, token);
        Object user = redisUtil.get(key.toString());
        if (user != null) {
            return JSONUtil.toBean(user.toString(), User.class);
        }
        return null;
    }

    public void updateTokenUser(User u,HttpServletRequest request) {
        if (request == null){
            ServletRequestAttributes sra = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
            if (null != sra) {
                request = sra.getRequest();
            } else {
                return ;
            }
        }
        String token = request.getHeader("accessToken");
        CacheKey key = CacheKey.generateKey(CacheType.UserLogin, token);
        redisUtil.set(key.toString(),JSONUtil.toJsonStr(u));
    }
}
