<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.GoodsMapper">
  <resultMap id="BaseResultMap" type="com.project.model.Goods">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="project_ids" jdbcType="VARCHAR" property="projectIds" />
    <result column="big_pic" jdbcType="VARCHAR" property="bigPic" />
    <result column="link" jdbcType="VARCHAR" property="link" />
    <result column="effective_value" jdbcType="INTEGER" property="effectiveValue" />
    <result column="memo" jdbcType="VARCHAR" property="memo" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />

    <result column="score" jdbcType="DECIMAL" property="score" />
    <result column="score_amount" jdbcType="DECIMAL" property="scoreAmount" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="discount_amount" jdbcType="DECIMAL" property="discountAmount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="pic" jdbcType="VARCHAR" property="pic" />
    <result column="member_type" jdbcType="VARCHAR" property="memberType" />
    <result column="sort_num" jdbcType="INTEGER" property="sortNum" />
  </resultMap>
</mapper>