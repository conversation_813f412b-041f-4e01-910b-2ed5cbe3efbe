package com.project.util;

import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.StrUtil;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.imageio.ImageIO;
import java.awt.*;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * file工具
 */
public class FileUtils {

	public static InputStream buildInputStream(String path) {
		if (StrUtil.isEmpty(path)) {
			throw new RuntimeException("请确保证书路径已设置");
		}

		InputStream inputStream;

		final String prefix = "classpath:";
		if (path.startsWith(prefix)) {
			path = StringUtils.removeFirst(path, prefix);
			if (!path.startsWith("/")) {
				path = "/" + path;
			}
			inputStream = FileUtils.class.getResourceAsStream(path);
			if (inputStream == null) {
				throw new RuntimeException("支付宝证书加载出错");
			}
		} else if (path.startsWith("http://") || path.startsWith("https://")) {
			try {
				inputStream = new URL(path).openStream();
				if (inputStream == null) {
					throw new RuntimeException("支付宝证书加载出错");
				}
			} catch (IOException e) {
				throw new RuntimeException("支付宝证书加载出错", e);
			}
		} else {
			try {
				File file = new File(path);
				if (!file.exists()) {
					throw new RuntimeException("支付宝证书加载出错");
				}
				inputStream = new FileInputStream(file);
			} catch (IOException e) {
				throw new RuntimeException("支付宝证书加载出错", e);
			}
		}
		return inputStream;
	}

	/**
	 * 将MultipartFile转为File
	 * @param mulFile
	 * @return
	 */
	public static File multipartFileToFile(MultipartFile mulFile) throws IOException {
		InputStream ins = mulFile.getInputStream();
		String fileName = mulFile.getOriginalFilename();
		String prefix = getFileNameNoEx(fileName)+ UUID.fastUUID();
		String suffix = "."+getExtensionName(fileName);
		File toFile = File.createTempFile(prefix,suffix);
		OutputStream os = new FileOutputStream(toFile);
		int bytesRead = 0;
		byte[] buffer = new byte[8192];
		while ((bytesRead = ins.read(buffer, 0, 8192)) != -1) {
			os.write(buffer, 0, bytesRead);
		}
		os.close();
		ins.close();
		return toFile;
	}

	/**
	 * 获取文件扩展名
	 *
	 */
	public static String getExtensionName(String filename) {
		if ((filename != null) && (filename.length() > 0)) {
			int dot = filename.lastIndexOf('.');
			if ((dot >-1) && (dot < (filename.length() - 1))) {
				return filename.substring(dot + 1);
			}
		}
		return filename;
	}

	/**
	 * 获取不带扩展名的文件名
	 *
	 */
	public static String getFileNameNoEx(String filename) {
		if ((filename != null) && (filename.length() > 0)) {
			int dot = filename.lastIndexOf('.');
			if ((dot >-1) && (dot < (filename.length()))) {
				return filename.substring(0, dot);
			}
		}
		return filename;
	}

	/**
	 * 缩小图片
	 *
	 */
	public static Image scale(String urlString) {
		try {
			return ImgUtil.scale(ImageIO.read(new URL(urlString)), 300, 300);
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	public static void main(String[] args) {
		HttpURLConnection httpUrl = null;
		URL url = null;
		try {
			url = new URL("https://qijidev-1300771826.cos.ap-shanghai.myqcloud.com/1617953453057.png");
			Image i = ImgUtil.scale(ImageIO.read(url), 300, 300);
			ImageIO.write(ImgUtil.toBufferedImage(i), "png", new File("D:/work/data/yw/image/apple.png"));
//			byte[] b = Base64.decodeBase64("aaa");
//			Image i2 = ImgUtil.scale(ImgUtil.toImage(b), 300, 300);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
