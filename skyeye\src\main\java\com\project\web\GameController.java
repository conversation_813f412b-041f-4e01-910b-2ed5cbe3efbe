package com.project.web;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.json.JSONUtil;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.model.Order;
import com.project.model.User;
import com.project.service.OrderService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.project.service.UserService;
import com.project.web.parameters.GameDataParam;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Date;
import java.util.List;

/**
* Created by CodeGenerator on 2024/04/25.
*/
@Api(tags = "order管理")
@RestController
@RequestMapping("/game")
public class GameController {

	private static Logger log = LoggerFactory.getLogger(GameController.class);

    @Resource
    private OrderService orderService;

	@Resource
	private UserService userService;

	@Resource
	private UserUtil userUtil;

    @PostMapping("/start")
	@ApiOperation(value = "新增记录",httpMethod = "POST")
    public Result add(@RequestBody GameDataParam param) {
    	log.info("新增记录"+ JSONUtil.toJsonStr(param));
        return Result.success();
    }



    @RequestMapping("/detail")
	@ApiOperation(value = "order获取详情",httpMethod = "POST")
    public Result<Order> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	Order order = null;
    	try {
    		order = orderService.findById(id);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success(order);
    }

    @RequestMapping("/list")
	@ApiOperation(value = "order获取列表",httpMethod = "POST")
    public Result<List<Order>> list(@RequestBody Order order, HttpServletRequest req) {

        PageHelper.startPage(order.getPage(), order.getSize());
		User user = userUtil.getTokenUser(req);
		if (user == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		Condition condition = new Condition(order.getClass());
        Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("pid", user.getId());
		PageInfo pageInfo = null;
		try {
    		 List<Order> list = orderService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
