<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.UserMapper">
  <resultMap id="BaseResultMap" type="com.project.model.User">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="login_account" jdbcType="VARCHAR" property="loginAccount" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="invite_code" jdbcType="VARCHAR" property="inviteCode" />
    <result column="pid" jdbcType="INTEGER" property="pid" />
    <result column="vip_level" jdbcType="INTEGER" property="vipLevel" />

    <result column="work_no" jdbcType="VARCHAR" property="workNo" />
    <result column="salt" jdbcType="VARCHAR" property="salt" />
    <result column="phone_no" jdbcType="VARCHAR" property="phoneNo" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />

    <result column="withdraw_amount" jdbcType="DECIMAL" property="withdrawAmount" />

    <result column="freeze_amount" jdbcType="DECIMAL" property="freezeAmount" />
    <result column="transpwd" jdbcType="VARCHAR" property="transpwd" />
    <result column="out_user_id" jdbcType="VARCHAR" property="outUserId" />


<!--    <result column="activity_limit" jdbcType="DECIMAL" property="activityLimit" />-->
<!--    <result column="activity_amount" jdbcType="DECIMAL" property="activityAmount" />-->

<!--    <result column="activity_num" jdbcType="INTEGER" property="activityNum" />-->
<!--    <result column="activity_output" jdbcType="DECIMAL" property="activityOutput" />-->
<!--    <result column="activity_reward_type" jdbcType="INTEGER" property="activityRewardType" />-->


  </resultMap>
  <update id="deal" >
    update b_user set
    <if test="type != null and type == 1">
      amount = amount + #{num}
    </if>
<!--    <if test="type != null and type == 2">-->
<!--      activity_amount = activity_amount + #{num}-->
<!--    </if>-->
<!--    <if test="type != null and type == 3">-->
<!--      activity_limit = activity_limit + #{num}-->
<!--    </if>-->
    <if test="type != null and type == 4">
      freeze_amount = freeze_amount + #{num}
    </if>
    <if test="type != null and type == 6">
      withdraw_amount = withdraw_amount + #{num}
    </if>

    where id = #{userId}
    <if test="type != null and type == 1">
      and  amount + #{num} >= 0
    </if>
<!--    <if test="type != null and type == 2">-->
<!--      and  activity_amount + #{num} >= 0-->
<!--    </if>-->
<!--    <if test="type != null and type == 3">-->
<!--      and  activity_limit + #{num} >= 0-->
<!--    </if>-->
    <if test="type != null and type == 4">
      and  freeze_amount + #{num} >= 0
    </if>
    <if test="type != null and type == 6">
      and  withdraw_amount + #{num} >= 0
    </if>

  </update>

  <select id="countPopularize" resultType="com.project.web.parameters.ValBo">
    SELECT
      DATE(create_time) AS name,  -- 将时间戳转换为日期格式
      COUNT(id) AS val          -- 统计每天的用户数量
    FROM
      b_user                           -- 假设用户表名为 users
    WHERE
      pid = #{userId}                -- :your_id 是传入的参数，代表你的ID
    GROUP BY
      DATE(create_time)                -- 按日期分组
    ORDER BY
      DATE(create_time) ASC;              -- 按日期升序排列
  </select>
</mapper>