<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.AddressMapper">
  <resultMap id="BaseResultMap" type="com.project.model.Address">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="province_code" jdbcType="VARCHAR" property="provinceCode" />
    <result column="city_code" jdbcType="VARCHAR" property="cityCode" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="mobile" jdbcType="VARCHAR" property="mobile" />
    <result column="is_default" jdbcType="INTEGER" property="isDefault" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>