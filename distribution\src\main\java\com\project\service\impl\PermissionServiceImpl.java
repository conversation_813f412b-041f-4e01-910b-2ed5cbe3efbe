package com.project.service.impl;

import com.project.dao.PermissionMapper;
import com.project.model.Permission;
import com.project.service.PermissionService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;


/**
 * Created by CodeGenerator on 2024/04/08.
 */
@Service
@Transactional
public class PermissionServiceImpl extends AbstractService<Permission> implements PermissionService {
    @Resource
    private PermissionMapper bPermissionMapper;

    @Override
    public List<Permission> selectEenu(String userId) {
        return bPermissionMapper.selectEenu(userId);
    }

    @Override
    public List<Permission> selectPermissionByUserId(String userId) {
        return bPermissionMapper.selectPermissionByUserId(userId);
    }
}
