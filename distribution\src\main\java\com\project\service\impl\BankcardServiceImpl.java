package com.project.service.impl;

import com.project.dao.BankcardMapper;
import com.project.model.Bankcard;
import com.project.service.BankcardService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/05/10.
 */
@Service
@Transactional
public class BankcardServiceImpl extends AbstractService<Bankcard> implements BankcardService {
    @Resource
    private BankcardMapper cBankcardMapper;

}
