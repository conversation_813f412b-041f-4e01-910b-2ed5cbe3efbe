package com.project.service.impl;

import com.project.dao.FileMapper;
import com.project.model.File;
import com.project.service.FileService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2024/04/09.
 */
@Service
@Transactional
public class FileServiceImpl extends AbstractService<File> implements FileService {
    @Resource
    private FileMapper bFileMapper;

}
