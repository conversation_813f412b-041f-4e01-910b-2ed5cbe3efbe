package com.project.web;
import cn.hutool.core.collection.CollUtil;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.model.Company;
import com.project.model.CompanyProject;
import com.project.model.Project;
import com.project.service.CompanyProjectService;
import com.project.service.CompanyService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.project.service.ProjectService;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.util.List;

/**
* Created by CodeGenerator on 2025/05/07.
*/
@Api(tags = "首页")
@RestController
@RequestMapping("/company")
public class CompanyController {

	private static Logger log = LoggerFactory.getLogger(CompanyController.class);

    @Resource
    private CompanyService companyService;

	@Resource
	private ProjectService projectService;

	@Resource
	private CompanyProjectService companyProjectService;



	@RequestMapping("/detail")
	@ApiOperation(value = "公司详情",httpMethod = "GET")
    public Result<Company> detail(@RequestParam Integer companyId) {
    	if(companyId == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	Company company = null;
    	try {
    		company = companyService.findById(companyId);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(company);
    }

	@RequestMapping("/projectDetail")
	@ApiOperation(value = "公司项目详情",httpMethod = "GET")
	public Result<CompanyProject> projectDetail(@RequestParam Integer companyId) {
		if(companyId == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			Condition c =new Condition(CompanyProject.class);
			c.createCriteria().andEqualTo("companyId",companyId);
			List<CompanyProject> p = companyProjectService.findByCondition(c);
			if (CollUtil.isNotEmpty(p)){
				return Result.success(p.get(0));
			}
			return Result.success();
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
	}

	@RequestMapping("/distributionList")
	@ApiOperation(value = "获取推广活动列表",httpMethod = "GET")
	public Result<List<Project>> distributionList(@RequestParam Integer companyId) {
		Condition condition = new Condition(Project.class);
		Criteria criteria = condition.createCriteria();
		criteria.andEqualTo("companyId", companyId);
		try {
			List<Project> list = projectService.findByCondition(condition);
			return Result.success(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
	}


}
