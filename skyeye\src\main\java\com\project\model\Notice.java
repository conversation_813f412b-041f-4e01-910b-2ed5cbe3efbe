package com.project.model;

import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="com.project.model.Notice")
@Table(name = "b_notice")
public class Notice extends BaseBeen implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;


    /**
     * 内容
     */
    @ApiModelProperty(value="content内容")
    private String content;

    @ApiModelProperty(value="content内容")
    private String title;


    @ApiModelProperty(value="img")
    private String img;

    @Column(name = "create_user")
    @ApiModelProperty(value="创建人")
    private String createUser;


    /**
     * 失效时间
     */
    @Column(name = "lose_time")
    @ApiModelProperty(value="loseTime失效时间")
    private Date loseTime;

    /**
     * 状态 1正常 2禁用
     */
    @ApiModelProperty(value="status状态 1正常 2禁用")
    private Integer status;

    @ApiModelProperty(value="类型 1通知 2公告")
    private Integer type;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    private static final long serialVersionUID = 1L;


}