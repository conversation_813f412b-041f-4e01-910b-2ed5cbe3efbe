package com.project.web.parameters;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: lxk
 * @Date: 2019/12/14 13:56
 * @Version 1.0
 */
@Data
@ApiModel("类型码列表参数")
public class CodeLibraryListParam implements Serializable {

    private static final long serialVersionUID = -5318595472853014732L;

    @ApiModelProperty("类型 1项目类型 2明细类型")
    private Integer type;

//    @ApiModelProperty(value="projectId项目id")
//    private Integer projectId;
}
