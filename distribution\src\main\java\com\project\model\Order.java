package com.project.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(value="com.project.model.Order")
@Table(name = "c_order")
public class Order extends BaseBeen implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    @ApiModelProperty(value="图片")
    private String icon;

    @Column(name = "order_no")
    @ApiModelProperty(value="订单号")
    private String orderNo;

    @Column(name = "company_id")
    @ApiModelProperty(value="companyId公司id")
    private Integer companyId;

    /**
     * 支付金额
     */
    @ApiModelProperty(value="amount支付金额")
    private BigDecimal amount;

    /**
     * 类型 1企业商品 2积分商品
     */
    @ApiModelProperty(value="type 类型 1企业商品 2积分商品")
    private Integer type;

    @Column(name = "coin_type")
    @ApiModelProperty(value="货币类型 1微信 2支付宝 3usdt")
    private Integer coinType;

    @Column(name = "address_id")
    @ApiModelProperty(value="地址id")
    private Integer addressId;

    @Column(name = "deliver_status")
    @ApiModelProperty(value="发货状态 1未发货 2已发货")
    private Integer deliverStatus;

    @Column(name = "deliver_no")
    @ApiModelProperty(value="物流编号")
    private String deliverNo;


    /**
     * 状态 1未支付 2已付款  3已退款
     */
    @ApiModelProperty(value="状态 1未支付 2已付款  3已退款 4 已取消")
    private Integer status;

    @Column(name = "settle_status")
    @ApiModelProperty(value="状态 1未结算 2已结算")
    private Integer settleStatus;

    @ApiModelProperty(value="积分兑换所需积分")
    private BigDecimal score;

    @Column(name = "score_amount")
    @ApiModelProperty(value="积分兑换金额")
    private BigDecimal scoreAmount;

    @Column(name = "score_type")
    @ApiModelProperty(value="是否兑换")
    private Integer scoreType;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value="createUser创建人")
    private String createUser;

    @Transient
    @ApiModelProperty(value="memo")
    private String memo;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    @Transient
    @ApiModelProperty(value="省市区地址")
    private String areaName;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty(value="userId用户id")
    private Integer userId;

    /**
     * 推荐人id
     */
    @ApiModelProperty(value="pid推荐人id")
    private Integer pid;

    /**
     * 商品id
     */
    @Column(name = "goods_id")
    @ApiModelProperty(value="goodsId商品id")
    private Integer goodsId;

    private static final long serialVersionUID = 1L;


}