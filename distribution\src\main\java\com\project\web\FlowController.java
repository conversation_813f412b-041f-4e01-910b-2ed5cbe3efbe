package com.project.web;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.model.Flow;
import com.project.model.User;
import com.project.service.FlowService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
* Created by CodeGenerator on 2025/05/08.
*/
@Api(tags = "账号明细管理")
@RestController
@RequestMapping("/flow")
public class FlowController {

	private static Logger log = LoggerFactory.getLogger(FlowController.class);

    @Resource
    private FlowService flowService;

	@Resource
	private UserUtil userUtil;

    @RequestMapping("/list")
	@ApiOperation(value = "flow获取列表",httpMethod = "POST")
    public Result<List<Flow>> list(@RequestBody Flow flow, HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
        PageHelper.startPage(flow.getPage(), flow.getSize());
        Condition condition = new Condition(flow.getClass());
        Criteria criteria = condition.createCriteria();
		criteria.andEqualTo("userId", tokenUser.getId());
		if(flow.getBizType() != null){
			criteria.andEqualTo("bizType", flow.getBizType());
		}
		if(flow.getType() != null){
			criteria.andEqualTo("type", flow.getType());
		}
		PageInfo pageInfo = null;
		try {
			condition.setOrderByClause("id desc");
    		 List<Flow> list = flowService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }


	@RequestMapping("/list2")
	@ApiOperation(value = "flow获取列表2",httpMethod = "POST")
	public Result<List<Flow>> list2(@RequestBody Flow flow) {
		PageHelper.startPage(flow.getPage(), flow.getSize());
		Condition condition = new Condition(flow.getClass());
		Criteria criteria = condition.createCriteria();
		if(flow.getUserId() != null){
			criteria.andEqualTo("userId", flow.getUserId());
		}
		if(flow.getBizType() != null){
			criteria.andEqualTo("bizType", flow.getBizType());
		}
		if(flow.getType() != null){
			criteria.andEqualTo("type", flow.getType());
		}
		PageInfo pageInfo = null;
		try {
			condition.setOrderByClause("id desc");
			List<Flow> list = flowService.findByCondition(condition);
			pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success(pageInfo);
	}
}
