<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.CompanyProjectMapper">
  <resultMap id="BaseResultMap" type="com.project.model.CompanyProject">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="pic" jdbcType="VARCHAR" property="pic" />
    <result column="principal" jdbcType="VARCHAR" property="principal" />
    <result column="principal_phone" jdbcType="VARCHAR" property="principalPhone" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="description" jdbcType="VARCHAR" property="description" />
  </resultMap>
</mapper>