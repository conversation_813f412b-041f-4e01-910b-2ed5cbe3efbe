//package com.project.util;
//
//import cn.hutool.core.img.ImgUtil;
//import cn.hutool.core.lang.UUID;
//import cn.hutool.core.util.StrUtil;
//import com.google.protobuf.InvalidProtocolBufferException;
//import com.microsoft.playwright.*;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.web.multipart.MultipartFile;
//
//import javax.imageio.ImageIO;
//import java.awt.*;
//import java.io.*;
//import java.net.HttpURLConnection;
//import java.net.URL;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.function.Consumer;
//
///**
// * file工具
// */
//public class TestUtils {
//
//	private void trackDouyin() {
//		try (Playwright playwright = Playwright.create()) {
//			Browser browser = playwright.webkit().launch();
//			Page page = browser.newPage();
////                page.emulateMedia(null);
//
//			Map<String, String> map = new HashMap<>();
//			Consumer<Request> listener = request -> {
////                    if (request.url().startsWith("https://live.douyin.com/webcast/im/fetch/?")) {
//			};
//			//监听websocket
//			page.onWebSocket(new Consumer<WebSocket>() {
//				@Override
//				public void accept(WebSocket webSocket) {
//					webSocket.onFrameReceived(new Consumer<WebSocketFrame>() {
//						@Override
//						public void accept(WebSocketFrame webSocketFrame) {
//							final byte[] data = webSocketFrame.binary();
//							System.out.println("：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：::");
//							try {
//								//最外层解析 try catch 不指定ws路径也可以，解析失败不会崩溃
//								//proto解析外层
//								WSS.WssResponse wss = WSS.WssResponse.parseFrom(data);
//								System.out.println(wss);
//								//GZIP解压data数据
//								final byte[] uncompress = Utils.uncompress(wss.getData());
//								//解析data
//								DanmuvoWSS.Response response = DanmuvoWSS.Response.parseFrom(uncompress);
//								final List<DanmuvoWSS.Message> messagesList = response.getMessagesList();
//								System.out.println(messagesList);
//								//根据message区分message类型解析 与之前接口相同
//								decodeMessage(messagesList, manager);
//							} catch (InvalidProtocolBufferException e) {
//								e.printStackTrace();
//							}
//
//							System.out.println("11111111111111111111111111111111111111111111111111111111111111111111111111111111");
//							System.out.println("：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：：::");
//
//						}
//					});
//				}
//			});
//
//			page.onRequestFinished(listener);
//			//访问页面
//			page.navigate(url);
////                browser.startTracing();
//
//			while (true) {
//				try {
//					//循环访问激活页面
//					page.content();
//					if (stop) {
//						if (manager != null) {
//							browser.close();
//							closeManager();
//
//						}
//						break;
//					}
//					Thread.sleep(1000);
//				} catch (InterruptedException e) {
//					e.printStackTrace();
//				}
//			}
//
//		}
//	}
//
//	public static void main(String[] args) {
//
//	}
//
//}
