package com.project.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Data
@ApiModel(value="com.project.model.CodeLibrary")
@Table(name = "b_code_library")
public class CodeLibrary implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 项目id 项目自定义code
     */
    @Column(name = "user_id")
    @ApiModelProperty(value="用户id 项目自定义code")
    private Integer userId;

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    /**
     * 图标
     */
    @ApiModelProperty(value="icon图标")
    private String icon;

    /**
     * key
     */
    @Column(name = "key_code")
    @ApiModelProperty(value="key_code")
    private String keyCode;

    /**
     * 码
     */
    @ApiModelProperty(value="code码")
    private String code;

    /**
     * 值1
     */
    @ApiModelProperty(value="val值1")
    private String val;

    /**
     * 值2
     */
    @ApiModelProperty(value="val2值2")
    private String val2;

    /**
     * 状态 1正常 2禁用
     */
    @ApiModelProperty(value="status状态 1正常 2禁用")
    private Integer status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }


    /**
     * 获取名称
     *
     * @return name - 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取图标
     *
     * @return icon - 图标
     */
    public String getIcon() {
        return icon;
    }

    /**
     * 设置图标
     *
     * @param icon 图标
     */
    public void setIcon(String icon) {
        this.icon = icon;
    }


    /**
     * 获取码
     *
     * @return code - 码
     */
    public String getCode() {
        return code;
    }

    /**
     * 设置码
     *
     * @param code 码
     */
    public void setCode(String code) {
        this.code = code;
    }

    /**
     * 获取值1
     *
     * @return val - 值1
     */
    public String getVal() {
        return val;
    }

    /**
     * 设置值1
     *
     * @param val 值1
     */
    public void setVal(String val) {
        this.val = val;
    }

    /**
     * 获取值2
     *
     * @return val2 - 值2
     */
    public String getVal2() {
        return val2;
    }

    /**
     * 设置值2
     *
     * @param val2 值2
     */
    public void setVal2(String val2) {
        this.val2 = val2;
    }

    /**
     * 获取状态 1正常 2禁用
     *
     * @return status - 状态 1正常 2禁用
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 1正常 2禁用
     *
     * @param status 状态 1正常 2禁用
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}