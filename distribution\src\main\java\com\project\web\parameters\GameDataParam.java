package com.project.web.parameters;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 */
@Data
@ApiModel("玩法参数")
public class GameDataParam {

    private String taskuuid;

    private String uuid;

    private String applypoint;

    private String popmsg;

    private String giftlist;

    private String fannamereadylist;

    private String donationdetail;

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
