<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.ProjectOrderMapper">
  <resultMap id="BaseResultMap" type="com.project.model.ProjectOrder">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="pid" jdbcType="INTEGER" property="pid" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_no" jdbcType="VARCHAR" property="orderNo" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="commission" jdbcType="DECIMAL" property="commission" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="create_user" jdbcType="VARCHAR" property="createUser" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>