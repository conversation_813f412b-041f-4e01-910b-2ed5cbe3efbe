<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.AreaMapper">
  <resultMap id="BaseResultMap" type="com.project.model.Area">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="area_code" jdbcType="VARCHAR" property="areaCode" />
    <result column="area_name" jdbcType="VARCHAR" property="areaName" />
    <result column="area_parent_code" jdbcType="VARCHAR" property="areaParentCode" />
    <result column="area_type" jdbcType="VARCHAR" property="areaType" />
  </resultMap>
</mapper>