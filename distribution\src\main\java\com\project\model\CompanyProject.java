package com.project.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
@Data
@ApiModel(value="com.project.model.CompanyProject")
@Table(name = "b_company_project")
public class CompanyProject implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 类型 1普通项目 
     */
    @ApiModelProperty(value="type类型 1普通项目 ")
    private Integer type;

    @ApiModelProperty(value="图片")
    private String pic;

    /**
     * 公司id
     */
    @Column(name = "company_id")
    @ApiModelProperty(value="companyId公司id")
    private Integer companyId;


    /**
     * 负责人
     */
    @ApiModelProperty(value="principal负责人")
    private String principal;

    /**
     * 负责人电话
     */
    @Column(name = "principal_phone")
    @ApiModelProperty(value="principalPhone负责人电话")
    private String principalPhone;

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    /**
     * 备注
     */
    @ApiModelProperty(value="content备注")
    private String content;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    /**
     * 描述
     */
    @ApiModelProperty(value="description描述")
    private String description;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取类型 1普通项目 
     *
     * @return type - 类型 1普通项目 
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置类型 1普通项目 
     *
     * @param type 类型 1普通项目 
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取公司id
     *
     * @return company_id - 公司id
     */
    public Integer getCompanyId() {
        return companyId;
    }

    /**
     * 设置公司id
     *
     * @param companyId 公司id
     */
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    /**
     * 获取图片
     *
     * @return pic - 图片
     */
    public String getPic() {
        return pic;
    }

    /**
     * 设置图片
     *
     * @param pic 图片
     */
    public void setPic(String pic) {
        this.pic = pic;
    }

    /**
     * 获取负责人
     *
     * @return principal - 负责人
     */
    public String getPrincipal() {
        return principal;
    }

    /**
     * 设置负责人
     *
     * @param principal 负责人
     */
    public void setPrincipal(String principal) {
        this.principal = principal;
    }

    /**
     * 获取负责人电话
     *
     * @return principal_phone - 负责人电话
     */
    public String getPrincipalPhone() {
        return principalPhone;
    }

    /**
     * 设置负责人电话
     *
     * @param principalPhone 负责人电话
     */
    public void setPrincipalPhone(String principalPhone) {
        this.principalPhone = principalPhone;
    }

    /**
     * 获取名称
     *
     * @return name - 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取备注
     *
     * @return content - 备注
     */
    public String getContent() {
        return content;
    }

    /**
     * 设置备注
     *
     * @param content 备注
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取描述
     *
     * @return description - 描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 设置描述
     *
     * @param description 描述
     */
    public void setDescription(String description) {
        this.description = description;
    }
}