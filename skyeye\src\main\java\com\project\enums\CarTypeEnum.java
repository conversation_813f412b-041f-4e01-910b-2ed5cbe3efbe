package com.project.enums;


import lombok.Data;

/**
 */
public enum CarTypeEnum {

    CarType1(1,"平板"),
    CarType2(2,"高栏"),
    CarType3(3,"厢式"),
    CarType4(4,"集装箱"),
    CarType5(5,"自卸"),
    CarType6(6,"冷藏"),
    CarType7(7,"保温"),
    CarType8(8,"高低板"),
    CarType9(9,"面包车"),
    CarType10(10,"棉被车"),
    CarType11(11,"爬梯车"),
    CarType12(12,"飞翼车"),
    CarType13(13,"依维柯")


    ;
    private Integer key;
    private String code;

    CarTypeEnum(Integer key, String code) {
        this.key = key;
        this.code = code;
    }

    public  static AttrBizTypeEnum getEnum(int key){
        for(AttrBizTypeEnum e:AttrBizTypeEnum.values()){
            if(key == e.getKey().intValue()){
                return e;
            }
        }
        return null;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
