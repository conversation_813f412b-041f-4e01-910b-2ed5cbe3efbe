package com.project.enums;



/**
 */
public enum FreightTypeEnum {

    FreightType1(1,"蔬菜"),
    FreightType2(2,"水果"),
    FreightType3(3,"玉米"),
    FreightType4(4,"木材苗圃"),
    FreightType5(5,"粮食谷壳"),
    FreightType6(6,"活禽活畜"),
    FreightType7(7,"活水产品"),
    FreightType8(8,"经济作物"),
    FreightType9(9,"其他农业"),
    FreightType10(10,"金属钢材"),
    FreightType11(11,"煤炭矿产"),
    FreightType12(12,"建材"),
    FreightType13(13,"化工塑料"),
    FreightType14(14,"纸类"),
    FreightType15(15,"机械设备"),
    FreightType16(16,"配件零件"),
    FreightType17(17,"农用物资"),
    FreightType18(18,"空包装"),
    FreightType19(19,"废品废料"),

    FreightType20(20,"家具家居"),
    FreightType21(21,"食品饮料"),
    FreightType22(22,"服饰纺织"),
    FreightType23(23,"设施用具"),
    FreightType24(24,"快递搬家"),
    FreightType25(25,"车辆"),
    FreightType26(26,"办公文体"),
    FreightType27(27,"快消医药"),
    FreightType28(28,"数码家电"),
    FreightType29(29,"其他"),

    ;
    private Integer key;
    private String code;

    FreightTypeEnum(Integer key, String code) {
        this.key = key;
        this.code = code;
    }

    public  static AttrBizTypeEnum getEnum(int key){
        for(AttrBizTypeEnum e:AttrBizTypeEnum.values()){
            if(key == e.getKey().intValue()){
                return e;
            }
        }
        return null;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
