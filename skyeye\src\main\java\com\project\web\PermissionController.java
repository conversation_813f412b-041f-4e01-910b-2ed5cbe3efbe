package com.project.web;

import cn.hutool.core.util.StrUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.project.service.PermissionService;
import com.project.service.RolePermissionService;
import com.project.core.Result;
import com.project.core.ResultCode;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.model.Permission;
import com.project.model.Role;
import com.project.model.RolePermission;
import com.project.model.User;
import com.project.web.parameters.PermissionBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * Created by CodeGenerator on 2019/11/01.
 */
@Api(tags = "权限管理")
@RestController
@RequestMapping("/permission")
public class PermissionController {

	private static Logger log = LoggerFactory.getLogger(PermissionController.class);

	@Resource
	private PermissionService permissionService;

	@Resource
	private RolePermissionService rolePermissionService;

	@Resource
	private UserUtil userUtil;

	@PostMapping("/add")
	@ApiOperation(value = "permission新增",httpMethod = "POST")
	public Result add(@RequestBody Permission permission, HttpServletRequest request) {
		if(permission == null){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if (StrUtil.isBlank(permission.getType())) {
			return ResultGenerator.genFailResult(ResultCode.TYPE_IS_NULL);
		}
		if (StrUtil.isBlank(permission.getName())) {
			return ResultGenerator.genFailResult(ResultCode.NAME_IS_NULL);
		}
		try {
			if ("1".equals(permission.getType())){
				permission.setIsAction("1");
			} else {
				permission.setIsAction("0");
			}
			User user = userUtil.getTokenUser(request);
			permission.setCreateTime(new Date());
			permission.setStatus(0);
			if(permission.getSeq() == null){
				permission.setSeq(0);
			}
			if (user != null){
				permission.setCreateUserId(user.getName());
			} else {
				permission.setCreateUserId("sys");
			}
			permissionService.save(permission);
			rolePermissionService.save(RolePermission.builder().permissionId(permission.getId()+"").roleId("1").build());


		} catch (Exception e) {
			log.error("新增对象操作异常e:{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return ResultGenerator.genSuccessResult();

	}

	@PostMapping("/delete")
	@ApiOperation(value = "permission删除",httpMethod = "POST")
	public Result delete(@RequestParam Integer id) {
		if(id == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			permissionService.deleteById(id);
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return ResultGenerator.genSuccessResult();
	}

	@PostMapping("/update")
	@ApiOperation(value = "permission更新",httpMethod = "POST")
	public Result update(@RequestBody Permission permission, HttpServletRequest request) {
		if(permission == null){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if(permission.getId() == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			User user = userUtil.getTokenUser(request);
			permission.setUpdateTime(new Date());
			if (user != null){
				permission.setUpdateUserId(user.getName());
			} else {
				permission.setUpdateUserId("sys");
			}
			permissionService.update(permission);
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return ResultGenerator.genSuccessResult();
	}

	@PostMapping("/detail")
	@ApiOperation(value = "permission获取详情",httpMethod = "POST")
	public Result<Permission> detail(@RequestParam Integer id) {
		if(id == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		Permission permission = null;
		try {
			permission = permissionService.findById(id);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}

		return ResultGenerator.genSuccessResult(permission);
	}

	@PostMapping("/list")
	@ApiOperation(value = "permission获取列表",httpMethod = "POST")
	public Result<List<Permission>> list(@RequestBody Permission permission, @RequestParam(defaultValue = "0") Integer page, @RequestParam(defaultValue = "0") Integer size) {
		PageHelper.startPage(page, size);

		Condition condition = new Condition(permission.getClass());
		Criteria criteria = condition.createCriteria();
		//        criteria.andEqualTo("name", city.getName());
		PageInfo pageInfo = null;
		try {
			List<Permission> list = permissionService.findByCondition(condition);
			pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return ResultGenerator.genSuccessResult(pageInfo);
	}

	/**
	 * 获取权限树
	 * @return
	 */
	@ApiOperation(value = "获取权限树",httpMethod = "GET")
	@RequestMapping(value = "/getTree", method = { RequestMethod.GET, RequestMethod.POST })
	public Result<List<PermissionBo>> getPermissionTree() {
		try {
			Condition condition2 = new Condition(Permission.class);
			condition2.orderBy("seq");

			List<Permission> ps = permissionService.findByCondition(condition2);
			if (ps == null || ps.isEmpty()) {
				return ResultGenerator.genFailResult(ResultCode.RESULT_IS_NULL);
			}

			List<PermissionBo> bos = new LinkedList<PermissionBo>();
			for (Permission permission : ps) {
				PermissionBo t = new PermissionBo();
				BeanUtils.copyProperties( permission,t);
				bos.add(t);
			}
			List<PermissionBo> list = assemblyChildren(bos);
			return ResultGenerator.genSuccessResult(list);
		} catch (Exception e) {
			log.error("获取权限树异常：e{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}

	}

	/**
	 * 获取有关系的权限树
	 * @param roleId 角色id
	 * @return
	 */
	@ApiOperation(value = "获取有关系的权限树",httpMethod = "GET")
	@RequestMapping(value = "/getDataTree", method = { RequestMethod.GET, RequestMethod.POST })
	public Result<List<PermissionBo>> getPermissionTreeHasData( String roleId) {
		try {
			Condition condition = new Condition(RolePermission.class);
			Criteria criteria = condition.createCriteria();
			if (StrUtil.isNotBlank(roleId)) {
				criteria.andEqualTo("roleId", roleId);
			}
			List<RolePermission> rps = rolePermissionService.findByCondition(condition);
			Condition condition2 = new Condition(Permission.class);
			condition2.orderBy("seq");
			List<Permission> ps = permissionService.findByCondition(condition2);
			if (ps == null || ps.isEmpty()) {
				return ResultGenerator.genFailResult(ResultCode.RESULT_IS_NULL);
			}
			List<PermissionBo> bos = new ArrayList<PermissionBo>();
			for (Permission permission : ps) {
				PermissionBo t = new PermissionBo();
				BeanUtils.copyProperties(permission,t);
				t.setHasRelevance("0");
				for (RolePermission rp : rps) {
					if (StrUtil.equals(rp.getPermissionId(), permission.getId() + "")) {
						t.setHasRelevance("1");
					}
				}
				bos.add(t);
			}
			List<PermissionBo> list = assemblyChildren(bos);
			return ResultGenerator.genSuccessResult(list);
		} catch (Exception e) {
			log.error("获取有关系的权限树异常：e{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}

	}

	/**
	 * 获取有关系的页面和动作
	 * @param roleId 角色id
	 * @return
	 */
	@ApiOperation(value = "获取有关系的页面和动作",httpMethod = "GET")
	@RequestMapping(value = "/getByParentIds", method = { RequestMethod.GET, RequestMethod.POST })
	public Result getPermissionByParentIds( String roleId, String ids) {
		if (StrUtil.isBlank(ids)) {
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			List<PermissionBo> bos = new ArrayList<PermissionBo>();

			List<Permission> ps = permissionService.findAll();
			if (ps == null || ps.isEmpty()) {
				return ResultGenerator.genFailResult(ResultCode.RESULT_IS_NULL);
			}

			if (StrUtil.isNotBlank(roleId)) {
				Condition condition = new Condition(RolePermission.class);
				Criteria criteria = condition.createCriteria();
				criteria.andEqualTo("roleId", roleId);
				List<RolePermission> rps = rolePermissionService.findByCondition(condition);
				for (Permission permission : ps) {
					PermissionBo t = new PermissionBo();
					BeanUtils.copyProperties( permission,t);
					t.setHasRelevance("0");
					for (RolePermission rp : rps) {
						if (StrUtil.equals(rp.getPermissionId(), permission.getId() + "")) {
							t.setHasRelevance("1");
						}
					}
					bos.add(t);
				}
			} else {
				for (Permission permission : ps) {
					PermissionBo t = new PermissionBo();
					BeanUtils.copyProperties(permission,t);
					t.setHasRelevance("0");
					bos.add(t);
				}
			}

			Map<String, List<PermissionBo>> list = new HashMap<String, List<PermissionBo>>();
			String[] is = ids.split(",");
			for (String id : is) {
				ArrayList<PermissionBo> l = new ArrayList<PermissionBo>();
				for (PermissionBo bo : bos) {
					if (!StrUtil.equals(bo.getType(), "0")) {
						if (StrUtil.equals(bo.getParentId(), id)) {
							l.add(bo);
						}
					}
				}
				list.put(id, l);
			}
			return ResultGenerator.genSuccessResult(list);
		} catch (Exception e) {
			log.error("获取有关系的权限树异常：e{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}

	}

	/**
	 * 更新角色并
	 * 绑定角色权限关系
	 * @return
	 */
	@ApiOperation(value = "更新角色并绑定角色权限关系",httpMethod = "GET")
	@RequestMapping(value = "/bangRolePermission", method = { RequestMethod.GET, RequestMethod.POST })
	public Result bangRolePermission(@RequestBody Role role, HttpServletRequest request) {
		if (role == null) {
			log.error("参数不能为空");
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if (role.getId() == null) {
			log.error("参数不能为空");
			return ResultGenerator.genFailResult(ResultCode.ROLEID_IS_NULL);
		}
		if (StrUtil.isBlank(role.getPermissionIds())) {
			log.error("参数不能为空");
			return ResultGenerator.genFailResult(ResultCode.PERMISSIONID_IS_NULL);
		}
		try {
			User user = userUtil.getTokenUser(request);
			if (user == null){
				return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
			}
			String[] ids = role.getPermissionIds().split(",");
			List<RolePermission> temp = new ArrayList<RolePermission>();
			for (String id : ids) {
				RolePermission cd = new RolePermission();
				cd.setRoleId(role.getId()+"");
				cd.setPermissionId(id);
				temp.add(cd);
			}
			Role temp2 = new Role();
			temp2.setId(role.getId());
			temp2.setUpdateTime(new Date());
			temp2.setUpdateUserId(user.getName());
			temp2.setName(role.getName());
			temp2.setStatus(role.getStatus());
			if (rolePermissionService.bangRolePermission(temp, temp2) > 0) {
				return ResultGenerator.genSuccessResult();
			} else {
				return ResultGenerator.genFailResult(ResultCode.SQL_ERROR);
			}
		} catch (Exception e) {
			log.error("绑定关系异常:e{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
	}

	/**
	 * 组装资源组
	 *
	 * @param rs
	 * @return
	 */
	private static List<PermissionBo> assemblyChildren(List<PermissionBo> rs) {
		if (rs == null) {
			return null;
		}
		//权限的集合对象   对象里面有权限的子权限(孩子节点)
		List<PermissionBo> temp = new LinkedList<PermissionBo>();
		temp.addAll(rs);


		List<PermissionBo> ret = new LinkedList<PermissionBo>();
		// 组装一个map
		//把ID为k  每个权限对象 组装为map
		Map<String, PermissionBo> tempMap = new LinkedHashMap<String, PermissionBo>();
		for (PermissionBo r : temp) {
			tempMap.put(r.getId() + "", r);
		}

		//组装子父权限集合
		for (PermissionBo r : temp) {
			//判断是否有父节点
			if (StrUtil.isNotBlank(r.getParentId())) {
				//取出当前对象的父节点对象
				PermissionBo parent = tempMap.get(r.getParentId());
				//判断不为空
				if (parent != null) {
					//取出对象中子节点集合
					List<PermissionBo> children = parent.getChildren();

					//LIst等于空   就创建一个有序集合
					if (children == null) {
						children = new LinkedList<PermissionBo>();
					}
					//在集合添加此对象ID
					children.add(tempMap.get(r.getId() + ""));
					parent.setChildren(children);
				}
			}
		}
		//组装最大的父权限
		//判断权限集合每个对象是否有父节点
		for (PermissionBo r : temp) {
			if (StrUtil.isBlank(r.getParentId())) {
				ret.add(tempMap.get(r.getId() + ""));
			}
		}
		return ret;
	}
}
