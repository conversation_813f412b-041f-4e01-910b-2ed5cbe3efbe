<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.RolePermissionMapper">
  <resultMap id="BaseResultMap" type="com.project.model.RolePermission">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="role_id" jdbcType="VARCHAR" property="roleId" />
    <result column="permission_id" jdbcType="VARCHAR" property="permissionId" />
  </resultMap>
</mapper>