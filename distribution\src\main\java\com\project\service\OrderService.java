package com.project.service;
import com.project.model.Goods;
import com.project.model.Order;
import com.project.core.Service;
import com.project.model.User;
import com.project.web.parameters.OrderParam;

import java.math.BigDecimal;


/**
 * Created by CodeGenerator on 2025/04/25.
 */
public interface OrderService extends Service<Order> {

    void paySuccess(Integer id);

    Order createOrder(OrderParam param, User tokenUser, Goods goods, String orderNo, BigDecimal amount);

    Integer pay(Integer id);
}
