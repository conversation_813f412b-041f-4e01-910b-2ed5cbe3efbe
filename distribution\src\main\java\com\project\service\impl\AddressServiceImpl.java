package com.project.service.impl;

import com.project.dao.AddressMapper;
import com.project.model.Address;
import com.project.service.AddressService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/04/25.
 */
@Service
@Transactional
public class AddressServiceImpl extends AbstractService<Address> implements AddressService {
    @Resource
    private AddressMapper cAddressMapper;

}
