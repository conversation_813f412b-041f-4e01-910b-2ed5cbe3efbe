package com.project.service.impl;

import com.project.service.RoleService;
import com.project.dao.RoleMapper;
import com.project.model.Role;
import com.project.model.RolePermission;
import com.project.service.RolePermissionService;
import com.project.core.AbstractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * Created by CodeGenerator on 2024/04/08.
 */
@Service
@Transactional
public class RoleServiceImpl extends AbstractService<Role> implements RoleService {
    @Resource
    private RoleMapper bRoleMapper;

    @Resource
    private RolePermissionService rolePermissionService;

    @Override
    public boolean saveAndPermissionIds(Role role, String permissionIds) {
        if (role == null) {
            return false;
        }
        if(save(role)>0){
            if (StringUtils.isNotBlank(permissionIds)) {
                String[] ids = permissionIds.split(",");
                List<RolePermission> temp = new ArrayList<RolePermission>();
                for (String id : ids) {
                    RolePermission cd = new RolePermission();
                    cd.setRoleId(role.getId()+"");
                    cd.setPermissionId(id);
                    temp.add(cd);
                }
                rolePermissionService.save(temp);
            }
            return true;
        }
        return false;
    }
}
