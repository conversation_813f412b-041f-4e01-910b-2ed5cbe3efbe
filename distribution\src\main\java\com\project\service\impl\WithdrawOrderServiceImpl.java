package com.project.service.impl;

import com.project.dao.WithdrawOrderMapper;
import com.project.model.WithdrawOrder;
import com.project.service.WithdrawOrderService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/05/12.
 */
@Service
@Transactional
public class WithdrawOrderServiceImpl extends AbstractService<WithdrawOrder> implements WithdrawOrderService {
    @Resource
    private WithdrawOrderMapper cWithdrawOrderMapper;

}
