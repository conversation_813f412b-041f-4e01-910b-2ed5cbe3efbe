package com.project.web;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.project.core.*;
import com.project.model.*;
import com.project.service.*;
import com.project.web.parameters.LoginAccountParameters;
import com.project.web.parameters.PermissionBo;
import com.project.web.parameters.RegisterAccountParameters;
import com.project.web.parameters.StatisticsBo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;
import java.util.concurrent.TimeUnit;

/**
 * Created by CodeGenerator on 2019/11/01.
 */
@Api(tags = "统计管理")
@RestController
@RequestMapping("/statistics")
public class StatisticsController {

    private static Logger log = LoggerFactory.getLogger(StatisticsController.class);

    @Resource
    private UserService userService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private UserUtil userUtil;

    @PostMapping("/statistics")
    @ApiOperation(value = "统计 ",httpMethod = "POST")
    public Result<StatisticsBo> statistics() {
        try {
            return ResultGenerator.genSuccessResult();
        } catch (Exception e) {
            log.error("删除对象操作异常e:{}",e);
            return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
        }
    }


}
