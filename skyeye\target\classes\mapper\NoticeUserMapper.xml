<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.NoticeUserMapper">
  <resultMap id="BaseResultMap" type="com.project.model.NoticeUser">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="notice_id" jdbcType="INTEGER" property="noticeId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>