//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by Fernflower decompiler)
//

package com.project.exception;

import com.project.core.ResultCode;
import lombok.Data;

@Data
public class ServiceException extends RuntimeException {

    private ResultCode code;

    public ServiceException() {
    }

    public ServiceException(ResultCode code) {
        this.code = code;
    }

    public ServiceException(String message) {
        super(message);
    }

    public ServiceException(String message, Throwable cause) {
        super(message, cause);
    }
}
