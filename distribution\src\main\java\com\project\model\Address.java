package com.project.model;

import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
@Data
@ApiModel(value="com.project.model.Address")
@Table(name = "c_address")
public class Address extends BaseBeen implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 收货人名称
     */
    @ApiModelProperty(value="name收货人名称")
    private String name;

    /**
     * 用户表的用户ID
     */
    @Column(name = "user_id")
    @ApiModelProperty(value="userId用户表的用户ID")
    private Integer userId;

    /**
     * 行政区域表的省ID
     */
    @Column(name = "province_code")
    @ApiModelProperty(value="provinceCode行政区域表的省ID")
    private String provinceCode;

    /**
     * 行政区域表的市ID
     */
    @Column(name = "city_code")
    @ApiModelProperty(value="cityCode行政区域表的市ID")
    private String cityCode;

    /**
     * 行政区域表的区县ID
     */
    @Column(name = "area_code")
    @ApiModelProperty(value="areaCode行政区域表的区县ID")
    private String areaCode;

    @Transient
    @ApiModelProperty(value="名称")
    private String areaName;

    /**
     * 具体收货地址
     */
    @ApiModelProperty(value="address具体收货地址")
    private String address;

    /**
     * 手机号码
     */
    @ApiModelProperty(value="mobile手机号码")
    private String mobile;

    /**
     * 是否默认地址
     */
    @Column(name = "is_default")
    @ApiModelProperty(value="isDefault是否默认地址")
    private Integer isDefault;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    private static final long serialVersionUID = 1L;


}