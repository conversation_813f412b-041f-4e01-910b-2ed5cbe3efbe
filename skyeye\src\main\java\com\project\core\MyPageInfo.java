package com.project.core;

import com.github.pagehelper.PageInfo;
import lombok.*;

import java.io.Serializable;
import java.util.Collection;
import java.util.List;

/**
 * 对Page<E>结果进行包装
 * <p/>
 * 新增分页的多项属性，主要参考:http://bbs.csdn.net/topics/360010907
 *
 * <AUTHOR>
 */
@Builder
@AllArgsConstructor
@Getter
@Setter
public class MyPageInfo<T> extends PageInfo {

    //结果集
    private String data;

    public MyPageInfo() {
    }

    /**
     * 包装Page对象
     *
     * @param list
     */
    public MyPageInfo(List<T> list) {
        super(list, 8);
    }


}
