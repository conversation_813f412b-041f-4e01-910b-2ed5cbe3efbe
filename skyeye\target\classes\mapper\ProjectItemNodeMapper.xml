<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.ProjectItemNodeMapper">
  <resultMap id="BaseResultMap" type="com.project.model.ProjectItemNode">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_item_id" jdbcType="INTEGER" property="projectItemId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="progress" jdbcType="INTEGER" property="progress" />
    <result column="elapsed_time" jdbcType="INTEGER" property="elapsedTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>