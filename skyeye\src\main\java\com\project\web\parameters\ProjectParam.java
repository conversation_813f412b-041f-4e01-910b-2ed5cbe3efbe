package com.project.web.parameters;

import com.project.core.BaseBeen;
import com.project.model.ProjectItem;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

@Data
@ApiModel(value="新增项目")
public class ProjectParam extends BaseBeen implements Serializable {
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 公司id
     */
    @ApiModelProperty(value="companyId公司id")
    private Integer companyId;

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    /**
     * 类型 1家装 2店铺装修 
     */
    @ApiModelProperty(value="type类型 1家装 2店铺装修 ")
    private Integer type;

    /**
     * 状态 0未开始  1进行中 2待审核 3已完成
     */
    @ApiModelProperty(value="status状态 0未开始  1进行中 2待审核 3已完成")
    private Integer status;

    /**
     * 用户id
     */
    @ApiModelProperty(value="userId用户id")
    private Integer userId;

    @ApiModelProperty(value="审核用户id")
    private Integer auditUserId;

    @ApiModelProperty(value="业主电话")
    private String ownerPhone;


    /**
     * 创建人
     */
    @ApiModelProperty(value="createUserId创建人")
    private Integer createUserId;

    /**
     * 更新人
     */
    @ApiModelProperty(value="updateUserId更新人")
    private Integer updateUserId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    /**
     * 开始时间
     */
    @ApiModelProperty(value="beginTime开始时间")
    private Date beginTime;

    /**
     * 预计完成时间
     */
    @ApiModelProperty(value="finishTime预计完成时间")
    private Date finishTime;

    /**
     * 预计金额
     */
    @ApiModelProperty(value="amount预计金额")
    private BigDecimal amount;

    /**
     * 预计天数
     */
    @ApiModelProperty(value="days预计天数")
    private Integer days;

    /**
     * 地址
     */
    @ApiModelProperty(value="address地址")
    private String address;

    @ApiModelProperty(value="项目明细")
    private List<ProjectItemParam> items;

    private static final long serialVersionUID = 1L;


}