//package com.project.web.socket;
//
//import cn.hutool.core.util.StrUtil;
//import cn.hutool.json.JSONObject;
//import cn.hutool.json.JSONUtil;
//import com.corundumstudio.socketio.SocketIOClient;
//import com.corundumstudio.socketio.annotation.OnEvent;
//import com.project.core.RedisUtil;
////import com.youyou.common.core.redis.RedisCache;
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Component;
//import org.springframework.util.StringUtils;
//
//import java.util.HashMap;
//import java.util.Map;
//import java.util.Objects;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.concurrent.ConcurrentMap;
//import java.util.concurrent.TimeUnit;
//
///**
// * @Author: lxk
// * @Description:
// * @Date: 2023/6/03 21:28
// */
//@Component
//public class SocketUtil {
//
//    private final Logger log = LoggerFactory.getLogger(this.getClass());
//
//    //暂且把用户&客户端信息存在缓存
//    //  ConcurrentMap<caseId, ConcurrentMap<userId, SocketUserBo>>
//
//    @Autowired
//    private RedisUtil redisUtil;
//
//    public static ConcurrentMap<String, ConcurrentMap<String, SocketUserBo>> connectMap = new ConcurrentHashMap<>();
//
//
//    @OnEvent(value = "CHANNEL_SYSTEM")
//    public void systemDataListener(String str) {
//        JSONObject msgObject = JSONUtil.parseObj(str);
//        if (msgObject == null){
//            return;
//        }
//        String event = msgObject.getStr("event");
//        String time = msgObject.getStr("time");
//        String clientId = msgObject.getStr("clientId");
//        String group = msgObject.getStr("caseId");
//        log.info("[socket消息]收到用户 ： {} 推送到系统频道的一条消息 :{}",clientId,msgObject.toString() );
//        //TODO 对消息进行自己的处理
//
//    }
//
//    //发送消息给所有用户 除了忽略用户id
//    public void sendToAll(Map<String, Object> msg,String sendChannel,String group,String ignoreUser) {
//        if (connectMap.isEmpty()){
//            return;
//        }
//        if (connectMap.get(group).isEmpty()){
//            return;
//        }
//        log.info("发送socket消息:"+JSONUtil.toJsonStr(msg));
//        //给在这个频道的每个客户端发消息
//        for (Map.Entry<String, SocketUserBo> entry : connectMap.get(group).entrySet()) {
//            if (!StrUtil.equals(ignoreUser,entry.getKey())){
//                entry.getValue().getClient().sendEvent(sendChannel, msg);
//            }
//        }
//    }
//
//    public void sendToOne(String userFlag, String group,Map<String, Object> msg,String sendChannel) {
//        //拿出某个客户端信息
//        SocketIOClient socketClient = getSocketClient(userFlag,group);
//        if (Objects.nonNull(socketClient) ){
//            //单独给他发消息
//            socketClient.sendEvent(sendChannel,msg);
//        }
//    }
//
//
//    public Map<String,SocketUserBo> getRedisUser(String group) {
//        String key = "SocketIO:"+group;
//        Object o = redisUtil.get(key);
//        Map<String, SocketUserBo> redisMap = new HashMap<>();
//        if (o != null){
//            cn.hutool.json.JSONObject json = JSONUtil.parseObj(o.toString());
//            for (String temp : json.keySet()) {
//                cn.hutool.json.JSONObject subJson = json.getJSONObject(temp);
//                SocketUserBo userBo = JSONUtil.toBean(subJson, SocketUserBo.class);
//                redisMap.put(temp, userBo);
//            }
//        }
//        return redisMap;
//    }
//
//    public void saveInfo(String key,String group,String content) {
//        key = "SocketIOInfo:"+group+":"+key;
//        redisUtil.set(key, content,1, TimeUnit.DAYS);
//    }
//
//    public String getInfo(String key,String group) {
//        key = "SocketIOInfo:"+group+":"+key;
//        Object o = redisUtil.get(key);
//        if (o == null){
//            return null;
//        }
//        return o.toString();
//    }
//
//
//    public void saveRedisUser(String clentId,String group,SocketUserBo bo) {
//        String key ="SocketIO:"+group;
//        Object o = redisUtil.get(key);
//        Map<String, SocketUserBo> redisMap = new HashMap<>();
//        if (o != null){
//            cn.hutool.json.JSONObject json = JSONUtil.parseObj(o.toString());
//            for (String temp : json.keySet()) {
//                cn.hutool.json.JSONObject subJson = json.getJSONObject(temp);
//                SocketUserBo userBo = JSONUtil.toBean(subJson, SocketUserBo.class);
//                redisMap.put(temp, userBo);
//            }
//        }
//        redisMap.put(clentId,bo);
//        redisUtil.set(key, JSONUtil.toJsonStr(redisMap),1, TimeUnit.DAYS);
//    }
//
//    public void removeRedisUser(String clentId,String group) {
//        String key ="SocketIO:"+group;
//        Object o = redisUtil.get(key);
//        Map<String, SocketUserBo> redisMap = new HashMap<>();
//        if (o != null){
//            cn.hutool.json.JSONObject json = JSONUtil.parseObj(o.toString());
//            for (String temp : json.keySet()) {
//                cn.hutool.json.JSONObject subJson = json.getJSONObject(temp);
//                SocketUserBo userBo = JSONUtil.toBean(subJson, SocketUserBo.class);
//                redisMap.put(temp, userBo);
//            }
//        }
//        redisMap.remove(clentId);
//        redisUtil.set(key, JSONUtil.toJsonStr(redisMap),1, TimeUnit.DAYS);
//    }
//
//
//    /**
//     * 识别出客户端
//     * @param userFlag
//     * @return
//     */
//    public SocketIOClient getSocketClient(String userFlag,String group){
//        SocketIOClient client = null;
//        if (StringUtils.hasLength(userFlag) &&  !connectMap.get(group).isEmpty()){
//            for (String key : connectMap.get(group).keySet()) {
//                if (userFlag.equals(key)){
//                    client = connectMap.get(group).get(key).getClient();
//                }
//            }
//        }
//        return client;
//    }
//
//
//}