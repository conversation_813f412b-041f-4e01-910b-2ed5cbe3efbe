package com.project.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
//import com.project.model.Examine;
//import com.project.model.ExaminePlan;
//import com.project.model.Pachong;
//import com.project.service.ExaminePlanService;
//import com.project.service.ExamineService;
//import com.project.service.PachongService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@EnableScheduling
public class MyScheduledTasks {

//    @Resource
//    private ExaminePlanService examinePlanService;
//
//
//
//    @Resource
//    private ExamineService examineService;
//
//    @Resource
//    private PachongService pachongService;


    // 每隔5秒执行一次
//    @Scheduled(fixedRate = 5000)
//    public void task1() {
//        System.out.println("定时任务1执行，当前时间：" + System.currentTimeMillis());
//    }



    @Scheduled(cron = "0 0/5 * * * ?")
    public void task() {
//        Condition c = new Condition(Pachong.class);
//        c.createCriteria().andEqualTo("status",1)
//                .andLessThanOrEqualTo("signStartTime",DateUtil.now())
//                .andGreaterThanOrEqualTo("signEndTime",DateUtil.now())
//        ;
//        List<Pachong> list = pachongService.findByCondition(c);
//        if (CollUtil.isNotEmpty(list)){
//            for (Pachong pachong: list)  {
//                if (StrUtil.isNotBlank(pachong.getWeeks())){
//                    if (StrUtil.containsAny(pachong.getWeeks(),String.valueOf(DateUtil.dayOfWeek(DateUtil.date())-1))){
//                        //今天有没有签到
//                        if (pachong.getLastSginTime() == null || !DateUtil.isSameDay(pachong.getLastSginTime(),DateUtil.date())){
//                            Date sginTime = DateUtil.parseTime(DateUtil.format(pachong.getSginTime(),"HH:mm:ss"));
//                            //现在是否比配置的时间 晚了？
//                            if (DateUtil.compare(DateUtil.parseTime(DateUtil.format(DateUtil.date(),"HH:mm:ss")),sginTime) > 0){
//                                int delayMinutes = RandomUtil.randomInt(10);
//                                ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
//                                executorService.schedule(() -> {
//                                    pachongService.run(pachong);
//                                }, delayMinutes, TimeUnit.MINUTES);
//                            }
//                        }
//                    }
//                } else {
//                    //今天有没有签到
//                    if (pachong.getLastSginTime() == null || !DateUtil.isSameDay(pachong.getLastSginTime(),DateUtil.date())){
//                        Date sginTime = DateUtil.parseTime(DateUtil.format(pachong.getSginTime(),"HH:mm:ss"));
//                        //现在是否比配置的时间 晚了？
//                        if (DateUtil.compare(DateUtil.parseTime(DateUtil.format(DateUtil.date(),"HH:mm:ss")),sginTime) > 0){
//                            int delayMinutes = RandomUtil.randomInt(10);
//                            ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();
//                            executorService.schedule(() -> {
//                                pachongService.run(pachong);
//                            }, delayMinutes, TimeUnit.MINUTES);
//                        }
//                    }
//                }
//            }
//        }
    }

    public static void main(String[] args) {
        if (StrUtil.containsAny("1,2,3","1")){
            System.out.println(111);
        } else {
            System.out.println(222);
        }
        System.out.println(String.valueOf(DateUtil.dayOfWeek(DateUtil.date())));
    }

    // 每天凌晨1点执行
    @Scheduled(cron = "0 0 1 * * ?")
    public void task2() {
//        Condition c = new Condition(ExaminePlan.class);
//        c.createCriteria().andEqualTo("status",1);
//        List<ExaminePlan> list = examinePlanService.findByCondition(c);
//        if (CollUtil.isNotEmpty(list)){
//            for (ExaminePlan plan: list)  {
//                try{
//                    if (plan.getType() == 1){
//                        if (StrUtil.isNotBlank(plan.getTimes())){
//                            String[] timeStr = plan.getTimes().split(",");
//                            for (String time: timeStr ) {
//                                Integer t = Integer.parseInt(time)+1;
//                                if (DateUtil.dayOfWeek(DateUtil.date()) == t) {
//                                    examineService.save(Examine.builder().address(plan.getAddress())
//                                            .content("检查计划生成任务")
//                                            .pics(plan.getPics())
//                                            .examineTime(DateUtil.offset(DateUtil.tomorrow(), DateField.SECOND,-1))
//                                            .status(1)
//                                            .createTime(DateUtil.date())
//                                            .build());
//                                }
//                            }
//                        }
//                    } else if (plan.getType() == 2){
//                        if (StrUtil.isNotBlank(plan.getTimes())){
//                            String[] timeStr = plan.getTimes().split(",");
//                            for (String time: timeStr ) {
//                                Integer t = Integer.parseInt(time);
//                                if (DateUtil.dayOfMonth(DateUtil.date()) == t) {
//                                    examineService.save(Examine.builder().address(plan.getAddress())
//                                            .content("检查计划生成任务")
//                                            .pics(plan.getPics())
//                                            .examineTime(DateUtil.offset(DateUtil.tomorrow(), DateField.SECOND,-1))
//                                            .status(1)
//                                            .createTime(DateUtil.date())
//                                            .build());
//                                }
//                            }
//                        }
//                    }
//                }catch (Exception e){
//                    log.error("执行计划失败",e);
//                }
//
//            }
//        }


        System.out.println("定时任务执行，当前时间：" + System.currentTimeMillis());
    }
}