package com.project.service.impl;

import com.project.dao.ComplainMapper;
import com.project.model.Complain;
import com.project.service.ComplainService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2024/04/08.
 */
@Service
@Transactional
public class ComplainServiceImpl extends AbstractService<Complain> implements ComplainService {
    @Resource
    private ComplainMapper bComplainMapper;

}
