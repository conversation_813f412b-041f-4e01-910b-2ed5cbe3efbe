package com.project.web;
import cn.hutool.core.lang.UUID;
import com.project.core.*;
import com.project.model.File;
import com.project.service.FileService;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.core.io.UrlResource;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;

import org.springframework.web.multipart.MultipartFile;

import java.io.FileOutputStream;
import java.io.IOException;
import java.net.MalformedURLException;

/**
* Created by CodeGenerator on 2024/04/09.
*/
@Api(tags = "文件管理")
@RestController
@RequestMapping("/file")
public class FileController {

	private static Logger log = LoggerFactory.getLogger(FileController.class);

    @Autowired
    private FileService fileService;

	@Autowired
	private Parameters parameters;

	@Autowired
	private ResourceLoader resourceLoader;


	@NoToken
	@PostMapping("/upload")
	public Result<Integer> uploadImage(@RequestPart("file") MultipartFile file) {
		if (file.isEmpty()) {
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		try {
			String token = UUID.fastUUID().toString();
			// 检查上传目录是否存在，如果不存在则创建
			java.io.File directory = new java.io.File(parameters.getUploadDir());
			if (!directory.exists()) {
				directory.mkdirs();
			}
			java.io.File destFile = new java.io.File(parameters.getUploadDir() + java.io.File.separator + token);
			file.transferTo(destFile);
			File f = File.builder().fileName(file.getOriginalFilename()).token(token)
					.path(parameters.getUploadDir() + java.io.File.separator + token).build();
			fileService.saveUseGeneratedKeys(f);
			return ResultGenerator.genSuccessResult(f.getId());
		} catch (IOException e) {
			log.error("上传文件失败", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
	}



	@NoToken
	@GetMapping("/image/{id}")
	public ResponseEntity<Resource> getImage(@PathVariable Integer id) throws MalformedURLException {
		File f = fileService.findById(id);
		String path = f.getPath();

		// 将文件名添加到绝对路径中

		// 创建文件对象
		java.io.File file = new java.io.File(path);

		// 检查文件是否存在
		if (file.exists() && file.isFile()) {
			// 如果文件存在，则创建一个 UrlResource 对象表示该文件
			Resource resource = new UrlResource(file.toURI().toURL());
			// 返回 ResponseEntity 包含资源内容
			return ResponseEntity.ok().contentType(MediaType.IMAGE_JPEG).body(resource);
		} else {
			// 如果文件不存在，则返回 404
			return ResponseEntity.notFound().build();
		}
	}

}
