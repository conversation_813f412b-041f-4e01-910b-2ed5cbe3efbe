package com.project.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import cn.hutool.json.JSONUtil;
import com.project.core.*;
import com.project.model.*;
import com.project.service.*;
import com.project.web.parameters.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by CodeGenerator on 2019/11/01.
 */
@Api(tags = "[app]登录管理")
@RestController
@RequestMapping("/login")
public class LoginController {

    private static Logger log = LoggerFactory.getLogger(LoginController.class);

    @Resource
    private UserService userService;

    @Resource
    private RedisUtil redisUtil;

    @Resource
    private UserUtil userUtil;

    @Resource
    private PermissionService permissionService;

    @Resource
    private ProjectService projectService;

    @Resource
    private ProjectItemService projectItemService;

    @Resource
    private ProjectItemMemberService projectItemMemberService;

    @NoToken
    @PostMapping("/login")
    @ApiOperation(value = "后台登录",httpMethod = "POST")
    public Result<User> add(@RequestBody LoginAccountParameters loginParameters) {
        if(loginParameters == null){
            return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
        }
        if(StrUtil.isBlank(loginParameters.getAccount())){
            return ResultGenerator.genFailResult(ResultCode.ACCOUNT_IS_NULL);
        }
        Condition condition = new Condition(User.class);
        condition.createCriteria().andEqualTo("loginAccount",loginParameters.getAccount());
        List<User> users = userService.findByCondition(condition);
        if(CollUtil.isEmpty(users)){
            return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
        }
        if(users.size() != 1){
            return ResultGenerator.genFailResult(ResultCode.USER_IS_ERROR);
        }
        User user = users.get(0);
        if(StrUtil.equals("1",user.getStatus())){
            return ResultGenerator.genFailResult(ResultCode.LOGIN_USER_STATUS_IS_ERROR);
        }
        String pwd2 = SecureUtil.md5(loginParameters.getPwd() + user.getSalt()).toUpperCase();
        log.error("[密码]"+loginParameters.getAccount() + " :"+pwd2);
        //		String pwd2 = MD5Util.getMD5((loginParameters.getPwd() + user.getSalt()).toUpperCase());
        if (StrUtil.equals(user.getPassword().toUpperCase(), pwd2)) {
            String token = StrUtil.uuid();
            CacheKey key = CacheKey.generateKey(CacheType.UserLogin, token);
            user.setToken(token);
            user.setType(loginParameters.getType());
            //设置redis缓存1小时
            redisUtil.set(key.toString(), JSONUtil.toJsonStr(user), 7, TimeUnit.DAYS);
            user.setPassword("***");
            user.setSalt("***");
            return ResultGenerator.genSuccessResult(user);
        } else {
            return ResultGenerator.genFailResult(ResultCode.UNLOGIN_PWD_ERROR);
        }
    }


    @NoToken
    @PostMapping("/register")
    @ApiOperation(value = "注册",httpMethod = "POST")
    public Result register(@RequestBody RegisterAccountParameters params) {
        if(params == null){
            return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
        }
        if(StrUtil.isBlank(params.getAccount())){
            return ResultGenerator.genFailResult(ResultCode.ACCOUNT_IS_NULL);
        }
        Condition condition = new Condition(User.class);
        condition.createCriteria().andEqualTo("loginAccount",params.getAccount());
        List<User> users = userService.findByCondition(condition);
        if(CollUtil.isNotEmpty(users)){
            return ResultGenerator.genFailResult(ResultCode.USER_IS_EXIST);
        }
        String salt = RandomUtil.randomString(4);
        String pwd2 = SecureUtil.md5(params.getPwd() + salt).toUpperCase();
        log.error("[密码]"+params.getAccount() + " :"+pwd2);

        String code = getInviteCode(0);
        if (code == null){
            return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
        }
        User user = User.builder().createUserId("sys").createTime(DateUtil.date()).inviteCode(code)
                .name(params.getAccount())
                .status("0") .loginAccount(params.getAccount()).password(pwd2).salt(salt).build();


        if (StrUtil.isNotBlank(params.getPhone())){
            user.setPhoneNo(params.getPhone());
        }
        if (StrUtil.isNotBlank(params.getName())){
            user.setName(params.getName());
        }
        if (StrUtil.isNotBlank(params.getAddress())){
            user.setAddress(params.getAddress());
        }
        if (StrUtil.isNotBlank(params.getWorkNo())){
            user.setWorkNo(params.getWorkNo());
        }
        String roleIds = "";
        if (params.getType() == 1){
            roleIds = "3";
        } else if (params.getType() == 2){
            roleIds = "4";
        }else if (params.getType() == 3){
            roleIds = "5";
        } else if (params.getType() == 4){
            roleIds = "6";
        }else if (params.getType() == 5){
            roleIds = "5";
        } else if (params.getType() ==6){
            roleIds = "6";
        }
        user.setVipLevel(1);
        userService.saveAndBandRole(user,roleIds,params.getCode());
        return ResultGenerator.genSuccessResult(user);
    }

    private String getInviteCode(int index) {
        if (index > 10000){
            return null;
        }
        String s = RandomUtil.randomString(8);
        User inviteCode = userService.findBy("inviteCode", s);
        if (inviteCode == null){
            return s;
        }
        return getInviteCode(index + 1);
    }

    @ApiOperation(value = "获取首页信息",httpMethod = "GET")
    @RequestMapping(value = "/index", method = { RequestMethod.GET, RequestMethod.POST })
    public Result<IndexBO> index(HttpServletRequest request) {
        User tokenUser = userUtil.getTokenUser(request);
        if (tokenUser == null){
            return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
        }
        IndexBO index = new IndexBO();
        Condition c = new Condition(ProjectItemMember.class);
        c.createCriteria().andEqualTo("userId",tokenUser.getId());
        List<ProjectItemMember> members = projectItemMemberService.findByCondition(c);
        if (CollUtil.isEmpty(members)){
            index.setTotal(0);
            index.setFinish(0);
            index.setIng(0);
            index.setElses(0);
        } else {
            List<Integer> ids = members.stream().map(ProjectItemMember::getProjectId).collect(Collectors.toList());
            c = new Condition(Project.class);
            c.createCriteria().andIn("id",ids)
                    .andEqualTo("status",3);
            Integer finish = projectService.countByCondition(c);
            if (finish == null){
                finish = 0;
            }
            c.createCriteria().andIn("id",ids)
                    .andEqualTo("status",1);
            Integer ing = projectService.countByCondition(c);
            if (ing == null){
                ing = 0;
            }
            c.createCriteria().andIn("id",ids) ;
            Integer total = projectService.countByCondition(c);
            if (total == null){
                total = 0;
            }
            Integer elses = total - finish - ing;
            index.setTotal(total);
            index.setFinish(finish);
            index.setIng(ing);
            index.setElses(elses);
        }
        List<Project> abarbeitung = getProject(1,tokenUser.getId());
        if (CollUtil.isNotEmpty(abarbeitung)){
            index.setAbarbeitung(abarbeitung.stream().map(e -> BeanUtil.toBean(e, ProjectBO.class)).collect(Collectors.toList()));
        } else {
            index.setAbarbeitung(CollUtil.newArrayList());
        }
        List<Project> audit = getProject(2,tokenUser.getId());
        if (CollUtil.isNotEmpty(audit)){
            index.setAudit(audit.stream().map(e -> BeanUtil.toBean(e, ProjectBO.class)).collect(Collectors.toList()));
        } else {
            index.setAudit(CollUtil.newArrayList());
        }

        return ResultGenerator.genSuccessResult(index);

    }

    private List<Project> getProject(int type, Integer userId) {
        Condition c = new Condition(ProjectItemMember.class);
        c.createCriteria().andEqualTo("userId",userId);
        List<ProjectItemMember> itemMembers = projectItemMemberService.findByCondition(c);
        if (CollUtil.isEmpty(itemMembers)){
            return CollUtil.newArrayList();
        }
        c = new Condition(ProjectItem.class);
        Example.Criteria criteria = c.createCriteria().andIn("id",itemMembers.stream().map(ProjectItemMember::getProjectItemId).collect(Collectors.toList()));
        if (type == 1){
            criteria .andEqualTo("status",3);
        } else  if (type == 2){
            criteria .andEqualTo("status",2);
        }
        List<ProjectItem> items = projectItemService.findByCondition(c);
        if (CollUtil.isEmpty(items)){
            return CollUtil.newArrayList();
        }
        c = new Condition(Project.class);
        c.createCriteria().andIn("id",items.stream().map(ProjectItem::getProjectId).collect(Collectors.toList()));
        return projectService.findByCondition(c);
    }

    /**
     * 通过userId获取菜单，需要TOKEN的.
     *
     */
    @ApiOperation(value = "获取用户菜单",httpMethod = "GET")
    @RequestMapping(value = "/queryMenu", method = { RequestMethod.GET, RequestMethod.POST })
    public Result<List<PermissionBo>> queryMenu(HttpServletRequest request) {
        //校验权限
        try {
            User tokenUser = userUtil.getTokenUser(request);
            if (tokenUser == null){
                return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
            }
            List<Permission> ps = permissionService.selectEenu(tokenUser.getId() + "");

            List<PermissionBo> bos = new LinkedList<PermissionBo>();
            for (Permission permission : ps) {
                PermissionBo t = new PermissionBo();
                BeanUtils.copyProperties( permission,t);
                bos.add(t);
            }
            bos = assemblyChildren(bos);
            return ResultGenerator.genSuccessResult(bos);
        } catch (Exception e) {
            log.error("获取菜单异常e:{}", e);
            return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
        }

    }

    /**
     * 组装资源组
     *
     * @param rs
     * @return
     */
    private static List<PermissionBo> assemblyChildren(List<PermissionBo> rs) {
        if (rs == null) {
            return null;
        }
        List<PermissionBo> temp = new LinkedList<>();
        temp.addAll(rs);
        List<PermissionBo> ret = new LinkedList<>();
        // 组装一个map
        Map<String, PermissionBo> tempMap = new TreeMap<>();
        for (PermissionBo r : temp) {
            tempMap.put(r.getId() + "", r);
        }
        for (PermissionBo r : temp) {
            if (StrUtil.isNotBlank(r.getParentId())) {
                PermissionBo parent = tempMap.get(r.getParentId());
                if (parent != null) {
                    List<PermissionBo> children = parent.getChildren();
                    if (children == null) {
                        children = new LinkedList<>();
                    }
                    children.add(tempMap.get(r.getId() + ""));
                    parent.setChildren(children);
                }
            }
        }
        for (PermissionBo r : temp) {
            if (StrUtil.isBlank(r.getParentId())) {
                ret.add(tempMap.get(r.getId() + ""));
            }
        }
        return ret;
    }




}
