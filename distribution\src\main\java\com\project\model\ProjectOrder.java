package com.project.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(value="com.project.model.ProjectOrder")
@Table(name = "c_project_order")
public class ProjectOrder extends BaseBeen implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    @Column(name = "company_id")
    @ApiModelProperty(value="companyId公司id")
    private Integer companyId;

    /**
     * 项目id
     */
    @Column(name = "project_id")
    @ApiModelProperty(value="projectId项目id")
    private Integer projectId;

    /**
     * 团长id
     */
    @ApiModelProperty(value="pid团长id")
    private Integer pid;


    @Column(name = "order_no")
    @ApiModelProperty(value="订单号")
    private String orderNo;

    @Column(name = "order_id")
    @ApiModelProperty(value="订单id")
    private Integer orderId;


    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty(value="userId用户id")
    private Integer userId;

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    /**
     * 订单金额
     */
    @ApiModelProperty(value="amount订单金额")
    private BigDecimal amount;

    /**
     * 佣金
     */
    @ApiModelProperty(value="commission佣金")
    private BigDecimal commission;

    /**
     * 状态 1直推 2成团复购 3成团奖励
     */
    @ApiModelProperty(value="type状态 1直推 2成团复购 3成团奖励")
    private Integer type;

    @Transient
    @ApiModelProperty(value="省市区地址")
    private String areaName;

    @Transient
    @ApiModelProperty(value="图片")
    private String pic;

    /**
     * 状态 1待结算 2已结算
     */
    @ApiModelProperty(value="status状态 1待结算 2已结算")
    private Integer status;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value="createUser创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取项目id
     *
     * @return project_id - 项目id
     */
    public Integer getProjectId() {
        return projectId;
    }

    /**
     * 设置项目id
     *
     * @param projectId 项目id
     */
    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    /**
     * 获取团长id
     *
     * @return pid - 团长id
     */
    public Integer getPid() {
        return pid;
    }

    /**
     * 设置团长id
     *
     * @param pid 团长id
     */
    public void setPid(Integer pid) {
        this.pid = pid;
    }

    /**
     * 获取用户id
     *
     * @return user_id - 用户id
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * 设置用户id
     *
     * @param userId 用户id
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取名称
     *
     * @return name - 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取订单金额
     *
     * @return amount - 订单金额
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 设置订单金额
     *
     * @param amount 订单金额
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 获取佣金
     *
     * @return commission - 佣金
     */
    public BigDecimal getCommission() {
        return commission;
    }

    /**
     * 设置佣金
     *
     * @param commission 佣金
     */
    public void setCommission(BigDecimal commission) {
        this.commission = commission;
    }

    /**
     * 获取状态 1直推 2成团复购 3成团奖励
     *
     * @return type - 状态 1直推 2成团复购 3成团奖励
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置状态 1直推 2成团复购 3成团奖励
     *
     * @param type 状态 1直推 2成团复购 3成团奖励
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取状态 1待结算 2已结算
     *
     * @return status - 状态 1待结算 2已结算
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 1待结算 2已结算
     *
     * @param status 状态 1待结算 2已结算
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}