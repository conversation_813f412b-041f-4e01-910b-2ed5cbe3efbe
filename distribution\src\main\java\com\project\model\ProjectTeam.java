package com.project.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(value="com.project.model.ProjectTeam")
@Table(name = "c_project_team")
public class ProjectTeam implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 项目id
     */
    @Column(name = "project_id")
    @ApiModelProperty(value="projectId项目id")
    private Integer projectId;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty(value="userId用户id")
    private Integer userId;

    /**
     * 公司id
     */
    @Column(name = "company_id")
    @ApiModelProperty(value="companyId公司id")
    private Integer companyId;

    /**
     * 会员用户id
     */
    @Column(name = "member_user_id")
    @ApiModelProperty(value="memberUserId会员用户id")
    private Integer memberUserId;

    /**
     * 姓名
     */
    @ApiModelProperty(value="name姓名")
    private String name;

    /**
     * 头像
     */
    @ApiModelProperty(value="icon头像")
    private String icon;

    /**
     * 位置
     */
    @ApiModelProperty(value="location位置")
    private Integer location;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    private static final long serialVersionUID = 1L;


}