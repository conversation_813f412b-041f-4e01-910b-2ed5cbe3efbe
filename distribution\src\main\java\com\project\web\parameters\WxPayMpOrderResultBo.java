package com.project.web.parameters;

import com.thoughtworks.xstream.annotations.XStreamAlias;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * @Author: lxk
 * @Date: 2019/12/10 14:50
 * @Version 1.0
 */
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel("微信预支付返回类")
public class WxPayMpOrderResultBo implements Serializable {

    private static final long serialVersionUID = 3102531737084976255L;
    /**
     * 订单id
     */
    @ApiModelProperty(value="订单id")
    private Integer orderId;

    @ApiModelProperty(value="微信appid")
    private String appId;
    @ApiModelProperty(value="时间戳")
    private String timeStamp;
    @ApiModelProperty(value="一串啥字符串")
    private String nonceStr;
    /**
     * 由于package为java保留关键字，因此改为packageValue. 前端使用时记得要更改为package
     */
    @ApiModelProperty(value="微信的prepay_id")
    @XStreamAlias("package")
    private String packageValue;
    @ApiModelProperty(value="签名类型")
    private String signType;
    @ApiModelProperty(value="签名")
    private String paySign;

    @ApiModelProperty(value="跳转url地址")
    private String url;

    @ApiModelProperty(value="payCode")
    private String payCode;
}
