package com.project.web.open.para;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 */
@Data
@ApiModel("账号开放参数")
public class UserOpenParameters extends  OpenBaseParam{


    @ApiModelProperty(value="头像")
    private String icon;

    @ApiModelProperty(value="名称")
    private String name;


    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
