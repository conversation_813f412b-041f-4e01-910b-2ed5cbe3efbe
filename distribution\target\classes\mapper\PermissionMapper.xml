<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.PermissionMapper">
  <resultMap id="BaseResultMap" type="com.project.model.Permission">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="e_name" jdbcType="VARCHAR" property="eName" />
    <result column="logo" jdbcType="VARCHAR" property="logo" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="is_action" jdbcType="VARCHAR" property="isAction" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="url" jdbcType="VARCHAR" property="url" />
    <result column="composing_key" jdbcType="VARCHAR" property="composingKey" />
    <result column="seq" jdbcType="INTEGER" property="seq" />
    <result column="parent_id" jdbcType="VARCHAR" property="parentId" />
    <result column="create_user_id" jdbcType="VARCHAR" property="createUserId" />
    <result column="update_user_id" jdbcType="VARCHAR" property="updateUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>

  <select id="selectEenu" parameterType="string" resultMap="BaseResultMap">
    select distinct p.*
    from b_permission p,
         b_user_role ur,
         b_role r,
         b_role_permission rp
    where p.id = rp.permission_id
      and rp.role_id = ur.role_id
      and ur.role_id = r.id
      and ur.user_id = #{userId}
      and p.status = 0
      and r.status = 0
      and p.type in (1, 2)
    order by seq, id
  </select>

  <select id="selectPermissionByUserId" parameterType="string" resultMap="BaseResultMap">
    select distinct p.*
    from b_permission p,
         b_user_role ur,
         b_role r,
         b_role_permission rp
    where p.id = rp.permission_id
      and rp.role_id = ur.role_id
      and ur.role_id = r.id
      and ur.user_id = #{userId}
      and p.status = 0
      and r.status = 0
    order by seq, id
  </select>
</mapper>