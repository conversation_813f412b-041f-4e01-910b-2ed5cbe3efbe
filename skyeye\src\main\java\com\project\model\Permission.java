package com.project.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="com.qimingxing.journey.model.Permission")
@Table(name = "b_permission")
public class Permission implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    /**
     * 英文名
     */
    @Column(name = "e_name")
    @ApiModelProperty(value="eName英文名")
    private String eName;

    /**
     * logo图
     */
    @ApiModelProperty(value="logologo图")
    private String logo;

    /**
     * 类型 1菜单2叶子菜单 3页面 4动作
     */
    @ApiModelProperty(value="type类型 1菜单2叶子菜单 3页面 4动作")
    private String type;

    /**
     * 是否允许点击 0无子菜单 1有子菜单
     */
    @Column(name = "is_action")
    @ApiModelProperty(value="isAction是否允许点击 0无子菜单 1有子菜单")
    private String isAction;

    /**
     * 状态 0正常 1禁用
     */
    @ApiModelProperty(value="status状态 0正常 1禁用")
    private Integer status;

    /**
     * 跳转地址
     */
    @ApiModelProperty(value="url跳转地址")
    private String url;

    /**
     * 前端排版
     */
    @Column(name = "composing_key")
    @ApiModelProperty(value="composingKey前端排版")
    private String composingKey;

    /**
     * 排序
     */
    @ApiModelProperty(value="seq排序")
    private Integer seq;

    /**
     * 上级菜单
     */
    @Column(name = "parent_id")
    @ApiModelProperty(value="parentId上级菜单")
    private String parentId;

    /**
     * 创建人
     */
    @Column(name = "create_user_id")
    @ApiModelProperty(value="createUserId创建人")
    private String createUserId;

    /**
     * 更新人
     */
    @Column(name = "update_user_id")
    @ApiModelProperty(value="updateUserId更新人")
    private String updateUserId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取名称
     *
     * @return name - 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取英文名
     *
     * @return e_name - 英文名
     */
    public String geteName() {
        return eName;
    }

    /**
     * 设置英文名
     *
     * @param eName 英文名
     */
    public void seteName(String eName) {
        this.eName = eName;
    }

    /**
     * 获取logo图
     *
     * @return logo - logo图
     */
    public String getLogo() {
        return logo;
    }

    /**
     * 设置logo图
     *
     * @param logo logo图
     */
    public void setLogo(String logo) {
        this.logo = logo;
    }

    /**
     * 获取类型 1菜单2叶子菜单 3页面 4动作
     *
     * @return type - 类型 1菜单2叶子菜单 3页面 4动作
     */
    public String getType() {
        return type;
    }

    /**
     * 设置类型 1菜单2叶子菜单 3页面 4动作
     *
     * @param type 类型 1菜单2叶子菜单 3页面 4动作
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 获取是否允许点击 0无子菜单 1有子菜单
     *
     * @return is_action - 是否允许点击 0无子菜单 1有子菜单
     */
    public String getIsAction() {
        return isAction;
    }

    /**
     * 设置是否允许点击 0无子菜单 1有子菜单
     *
     * @param isAction 是否允许点击 0无子菜单 1有子菜单
     */
    public void setIsAction(String isAction) {
        this.isAction = isAction;
    }

    /**
     * 获取状态 0正常 1禁用
     *
     * @return status - 状态 0正常 1禁用
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 0正常 1禁用
     *
     * @param status 状态 0正常 1禁用
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取跳转地址
     *
     * @return url - 跳转地址
     */
    public String getUrl() {
        return url;
    }

    /**
     * 设置跳转地址
     *
     * @param url 跳转地址
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * 获取前端排版
     *
     * @return composing_key - 前端排版
     */
    public String getComposingKey() {
        return composingKey;
    }

    /**
     * 设置前端排版
     *
     * @param composingKey 前端排版
     */
    public void setComposingKey(String composingKey) {
        this.composingKey = composingKey;
    }

    /**
     * 获取排序
     *
     * @return seq - 排序
     */
    public Integer getSeq() {
        return seq;
    }

    /**
     * 设置排序
     *
     * @param seq 排序
     */
    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    /**
     * 获取上级菜单
     *
     * @return parent_id - 上级菜单
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * 设置上级菜单
     *
     * @param parentId 上级菜单
     */
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    /**
     * 获取创建人
     *
     * @return create_user_id - 创建人
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * 设置创建人
     *
     * @param createUserId 创建人
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 获取更新人
     *
     * @return update_user_id - 更新人
     */
    public String getUpdateUserId() {
        return updateUserId;
    }

    /**
     * 设置更新人
     *
     * @param updateUserId 更新人
     */
    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}