package com.project.web.parameters;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @Author: lxk
 * @Date: 2019/12/14 13:56
 * @Version 1.0
 */
@Data
@ApiModel("审核参数")
public class AuditParam implements Serializable {

    private static final long serialVersionUID = -5318595472853014732L;
    @ApiModelProperty("itemId")
    private Integer projectItemId;

    /**
     * 评分
     */
    @ApiModelProperty(value="score评分")
    private Integer score;

    /**
     * 审核内容
     */
    @ApiModelProperty(value="audit审核内容")
    private String audit;

    /**
     * 预计完成时间
     */
    @ApiModelProperty(value="审核时间")
    private Date auditTime;

    /**
     * 状态  1严重紧急 2严重 3紧急4不严重不紧急
     */
    @ApiModelProperty(value="auditType状态  1严重紧急 2严重 3紧急4不严重不紧急")
    private Integer auditType;

    /**
     * 类型 0未审核 1通过 2不通过
     */
    @ApiModelProperty(value="auditRet类型 0未审核 1通过 2不通过")
    private Integer auditRet;
}
