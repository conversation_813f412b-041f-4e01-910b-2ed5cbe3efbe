package com.project.util;

import cn.hutool.crypto.SecureUtil;
import org.springframework.beans.factory.annotation.Value;

import java.util.HashMap;
import java.util.Map;

//@Component
public class TransApi {

    @Value("${baidu.trans.api.host}")
    private String TRANS_API_HOST = "http://api.fanyi.baidu.com/api/trans/vip/translate";

    @Value("${baidu.app.id}")
    private String appid = "20191022000343414";

    @Value("${baidu.security.key}")
    private String securityKey = "tHaHt6dWYOG1VqzZMFBj";

    public static String type = "{\"auto\":\"自动检测\",\"zh\":\"中文\",\"en\":\"英语\",\"yue\":\"粤语\",\"wyw\":\"文言文\",\"jp\":\"日语\",\"kor\":\"韩语\",\"fra\":\"法语\",\"spa\":\"西班牙语\",\"th\":\"泰语\",\"ara\":\"阿拉伯语\",\"ru\":\"俄语\",\"pt\":\"葡萄牙语\",\"de\":\"德语\",\"it\":\"意大利语\",\"el\":\"希腊语\",\"nl\":\"荷兰语\",\"pl\":\"波兰语\",\"bul\":\"保加利亚语\",\"est\":\"爱沙尼亚语\",\"dan\":\"丹麦语\",\"fin\":\"芬兰语\",\"cs\":\"捷克语\",\"rom\":\"罗马尼亚语\",\"slo\":\"斯洛文尼亚语\",\"swe\":\"瑞典语\",\"hu\":\"匈牙利语\",\"cht\":\"繁体中文\",\"vie\":\"越南语\"}";

    public String getTransResult(String query, String from, String to) {
        Map<String, String> params = buildParams(query, from, to);
        return HttpGet.get(TRANS_API_HOST, params);
    }

    private Map<String, String> buildParams(String query, String from, String to) {
        Map<String, String> params = new HashMap<String, String>();
        params.put("q", query);
        params.put("from", from);
        params.put("to", to);
        params.put("appid", appid);
        // 随机数
        String salt = String.valueOf(System.currentTimeMillis());
        params.put("salt", salt);
        // 签名
        String src = appid + query + salt + securityKey; // 加密前的原文
        params.put("sign", SecureUtil.md5(src));
        return params;
    }

}
