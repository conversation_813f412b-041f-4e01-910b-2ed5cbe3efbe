<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.TeamMapper">
  <resultMap id="BaseResultMap" type="com.project.model.Team">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="company_id" jdbcType="VARCHAR" property="companyId" />
    <result column="member_name" jdbcType="VARCHAR" property="memberName" />
    <result column="member_id" jdbcType="INTEGER" property="memberId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>