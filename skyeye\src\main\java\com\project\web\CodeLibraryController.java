package com.project.web;
import cn.hutool.core.date.DateUtil;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.model.CodeLibrary;
import com.project.model.User;
import com.project.service.CodeLibraryService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.project.web.parameters.CodeLibraryListParam;
import com.project.web.parameters.CodeLibraryParam;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
* Created by CodeGenerator on 2025/01/26.
*/
@Api(tags = "codeLibrary管理")
@RestController
@RequestMapping("/code/library")
public class CodeLibraryController {

	private static Logger log = LoggerFactory.getLogger(CodeLibraryController.class);

    @Resource
    private CodeLibraryService codeLibraryService;

    @PostMapping("/add")
	@ApiOperation(value = "新增类型",httpMethod = "POST")
    public Result add(@RequestBody CodeLibraryParam param) {
    	if(param == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	try {
			CodeLibrary codeLibrary = new CodeLibrary();
			codeLibrary.setName(param.getName());
			if (param.getType() == 1){
				codeLibrary.setKeyCode("project_type");
				codeLibrary.setCode("project_type"+param.getCode());
			} else if (param.getType() == 2){
				codeLibrary.setKeyCode("item_type");
				codeLibrary.setCode("item_type"+param.getCode());
			}
			codeLibrary.setStatus(1);
			codeLibrary.setUserId(param.getUserId());
    		codeLibrary.setCreateTime(DateUtil.date());
			codeLibrary.setUpdateTime(DateUtil.date());
    		codeLibraryService.save(codeLibrary);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success();
    }



    @PostMapping("/update")
	@ApiOperation(value = "codeLibrary更新",httpMethod = "POST")
    public Result update(@RequestBody CodeLibrary codeLibrary) {
    	if(codeLibrary == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	if(codeLibrary.getId() == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	try {
    		codeLibraryService.update(codeLibrary);
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

	@Resource
	private UserUtil userUtil;


	@RequestMapping("/CodeLibrary")
	@ApiOperation(value = "CodeLibrary",httpMethod = "POST")
	public Result<List<CodeLibrary>> appList(@RequestBody CodeLibraryListParam param, HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		Condition condition = new Condition(CodeLibrary.class);
		Criteria criteria = condition.or();
		Criteria criteria2 = condition.or();
		if (param.getType() == 1){
			criteria.andLike("keyCode", "project_type%");
			criteria2.andLike("keyCode", "project_type%");
		} else if (param.getType() == 2){
			criteria.andLike("keyCode", "item_type%");
			criteria2.andLike("keyCode", "item_type%");
		}
		criteria.andIsNull("userId");
		criteria2.andEqualTo("userId",tokenUser.getId());

		PageInfo pageInfo = null;
		try {
			condition.setOrderByClause("create_time ");
			List<CodeLibrary> list = codeLibraryService.findByCondition(condition);
			pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success(pageInfo);
	}

    @RequestMapping("/list")
	@ApiOperation(value = "[后台]获取列表",httpMethod = "POST")
    public Result<List<CodeLibrary>> list(@RequestBody CodeLibrary codeLibrary, @RequestParam(defaultValue = "0") Integer page, @RequestParam(defaultValue = "10") Integer size) {

        PageHelper.startPage(page, size);

        Condition condition = new Condition(codeLibrary.getClass());
        Criteria criteria = condition.createCriteria();
//        criteria.andEqualTo("name", city.getName());
		PageInfo pageInfo = null;
		try {
    		 List<CodeLibrary> list = codeLibraryService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
