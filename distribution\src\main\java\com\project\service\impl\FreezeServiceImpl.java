package com.project.service.impl;

import com.project.dao.FreezeMapper;
import com.project.model.Freeze;
import com.project.service.FreezeService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/05/13.
 */
@Service
@Transactional
public class FreezeServiceImpl extends AbstractService<Freeze> implements FreezeService {
    @Resource
    private FreezeMapper cFreezeMapper;

}
