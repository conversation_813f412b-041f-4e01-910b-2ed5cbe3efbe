package com.project.core;

import java.io.Serializable;


public class <PERSON><PERSON><PERSON><PERSON> implements Serializable {

	private CacheType cacheType;

	private String subKey;

	private CacheKey() {
	}

	public static CacheKey generateKey(CacheType cacheType, String subKey) {
		if (cacheType == null) {
			throw new NullPointerException("CacheKey不允许存在空参数");
		}
		CacheKey key = new CacheKey();
		key.cacheType = cacheType;
		key.subKey = subKey;
		return key;
	}

	@Override
	public String toString() {
		return new StringBuilder().append(cacheType).append("_").append(subKey != null ? subKey : "").toString();
	}

	public CacheType getType() {
		return cacheType;
	}

}
