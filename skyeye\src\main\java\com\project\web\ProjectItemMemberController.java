package com.project.web;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.model.ProjectItemMember;
import com.project.service.ProjectItemMemberService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.util.List;

/**
* Created by CodeGenerator on 2025/01/22.
*/
@Api(tags = "projectItemMember管理")
@RestController
@RequestMapping("/project/item/member")
public class ProjectItemMemberController {

	private static Logger log = LoggerFactory.getLogger(ProjectItemMemberController.class);

    @Resource
    private ProjectItemMemberService projectItemMemberService;



    @RequestMapping("/list")
	@ApiOperation(value = "projectItemMember获取列表",httpMethod = "POST")
    public Result<List<ProjectItemMember>> list(@RequestBody ProjectItemMember projectItemMember, @RequestParam(defaultValue = "0") Integer page, @RequestParam(defaultValue = "10") Integer size) {

        PageHelper.startPage(page, size);
        
        Condition condition = new Condition(projectItemMember.getClass());
        Criteria criteria = condition.createCriteria();
//        criteria.andEqualTo("name", city.getName());
		PageInfo pageInfo = null;
		try {
    		 List<ProjectItemMember> list = projectItemMemberService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
