package com.project.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@ApiModel(value="com.project.model.Bankcard")
@Table(name = "c_bankcard")
public class Bankcard implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    @Column(name = "user_id")
    @ApiModelProperty(value="userId")
    private Integer userId;

    /**
     * 卡号
     */
    @ApiModelProperty(value="no卡号")
    private String no;

    /**
     * 银行
     */
    @ApiModelProperty(value="bank银行")
    private String bank;

    /**
     * 银行支行
     */
    @Column(name = "sub_bank")
    @ApiModelProperty(value="subBank银行支行")
    private String subBank;

    /**
     * 人名
     */
    @ApiModelProperty(value="name人名")
    private String name;

    /**
     * 银行电话
     */
    @ApiModelProperty(value="phone银行电话")
    private String phone;

    /**
     * 身份证号
     */
    @Column(name = "identity_card")
    @ApiModelProperty(value="identityCard身份证号")
    private String identityCard;

    @Column(name = "create_time")
    @ApiModelProperty(value="createTime")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * @return user_id
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * @param userId
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取卡号
     *
     * @return no - 卡号
     */
    public String getNo() {
        return no;
    }

    /**
     * 设置卡号
     *
     * @param no 卡号
     */
    public void setNo(String no) {
        this.no = no;
    }

    /**
     * 获取银行
     *
     * @return bank - 银行
     */
    public String getBank() {
        return bank;
    }

    /**
     * 设置银行
     *
     * @param bank 银行
     */
    public void setBank(String bank) {
        this.bank = bank;
    }

    /**
     * 获取银行支行
     *
     * @return sub_bank - 银行支行
     */
    public String getSubBank() {
        return subBank;
    }

    /**
     * 设置银行支行
     *
     * @param subBank 银行支行
     */
    public void setSubBank(String subBank) {
        this.subBank = subBank;
    }

    /**
     * 获取人名
     *
     * @return name - 人名
     */
    public String getName() {
        return name;
    }

    /**
     * 设置人名
     *
     * @param name 人名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取银行电话
     *
     * @return phone - 银行电话
     */
    public String getPhone() {
        return phone;
    }

    /**
     * 设置银行电话
     *
     * @param phone 银行电话
     */
    public void setPhone(String phone) {
        this.phone = phone;
    }

    /**
     * 获取身份证号
     *
     * @return identity_card - 身份证号
     */
    public String getIdentityCard() {
        return identityCard;
    }

    /**
     * 设置身份证号
     *
     * @param identityCard 身份证号
     */
    public void setIdentityCard(String identityCard) {
        this.identityCard = identityCard;
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}