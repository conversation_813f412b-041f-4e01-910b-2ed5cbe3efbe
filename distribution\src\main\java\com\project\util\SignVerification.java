package com.project.util;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.Map;

public class SignVerification {
    

    /**
     * 生成MD5签名
     * @param orgCode 机构码
     * @param timestamp 时间戳
     * @param params 其他需要参与签名的参数
     * @return 生成的MD5签名
     */
    public static String generateSign(String secretkey, String orgCode, long timestamp, Map<String, String> params) {
        try {
            // 1. 拼接签名字符串
            StringBuilder sb = new StringBuilder();
            sb.append("orgCode=").append(orgCode)
              .append("&timestamp=").append(timestamp)
              .append("&key=").append(secretkey);
            
            // 添加其他参数（按字母顺序排序以保证一致性）
            if (params != null && !params.isEmpty()) {
                params.entrySet().stream()
                    .sorted(Map.Entry.comparingByKey())
                    .forEach(entry -> sb.append("&").append(entry.getKey()).append("=").append(entry.getValue()));
            }
            
            // 2. 计算MD5
            MessageDigest md = MessageDigest.getInstance("MD5");
            byte[] digest = md.digest(sb.toString().getBytes());
            
            // 3. 转换为16进制字符串
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("MD5 algorithm not found", e);
        }
    }
    
    /**
     * 验证签名是否有效
     * @param orgCode 机构码
     * @param timestamp 时间戳
     * @param receivedSign 接收到的签名
     * @param params 其他参数
     * @param timeoutMillis 签名有效期（毫秒），0表示不验证时效性
     * @return 是否验证通过
     */
    public static boolean verifySign(String secretkey,String orgCode, long timestamp, String receivedSign,
                                   Map<String, String> params, long timeoutMillis) {
        // 1. 验证时间戳是否有效
        if (timeoutMillis > 0) {
            long currentTime = System.currentTimeMillis();
            if (Math.abs(currentTime - timestamp) > timeoutMillis) {
                return false; // 签名已过期
            }
        }
        
        // 2. 重新生成签名
        String generatedSign = generateSign(secretkey,orgCode, timestamp, params);
        
        // 3. 比较签名是否一致（安全比较，防止时序攻击）
        return MessageDigest.isEqual(generatedSign.getBytes(), receivedSign.getBytes());
    }
    
    // 测试示例
    public static void main(String[] args) {
        String orgCode = "TEST_ORG";
        long timestamp = System.currentTimeMillis();
        
        // 其他参数
        Map<String, String> params = new HashMap<>();
        params.put("userId", "12345");
        params.put("action", "login");
        
        // 生成签名
        String sign = generateSign("111",orgCode, timestamp, params);
        System.out.println("Generated Sign: " + sign);
        
        // 验证签名
        boolean isValid = verifySign("111",orgCode, timestamp, sign, params, 5 * 60 * 1000); // 5分钟有效期
        System.out.println("Signature is valid: " + isValid);
        
        // 测试错误签名
        boolean isInvalid = verifySign("111",orgCode, timestamp, "wrong_sign", params, 5 * 60 * 1000);
        System.out.println("Wrong signature should be invalid: " + !isInvalid);
    }
}