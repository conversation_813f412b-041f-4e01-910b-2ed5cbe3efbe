package com.project.service;
import com.project.model.ProjectAmount;
import com.project.core.Service;

import java.math.BigDecimal;


/**
 * Created by CodeGenerator on 2025/05/17.
 */
public interface ProjectAmountService extends Service<ProjectAmount> {


    /**
     * 货币交易
     * @param userId
     * @param projectId
     * @param bizType 业务类型  1、直推分佣 2成团奖励 3成团分佣 4购物抵扣 5参与活动新增上限 6提现 7冻结 8解冻 9签到 10 新订单增加每日产出 11 成团增加每日产出 12当前奖励金发放完毕重置每日产出
     * @param type  1积分 2活动金额 3活动金额上限 5每日产出 6提现金额
     * @param num
     * @return
     */
    Boolean deal(Integer userId, int projectId, int bizType, Byte type, BigDecimal num, Long bizId);

    /**
     * 新增成团次数
     * @param userId
     * @param projectId
     */
    void addActivityNum(Integer userId, Integer projectId);

    /**
     * 获取活动余额
     * @param userId
     * @param projectId
     * @return
     */
    ProjectAmount findPa(Integer userId, Integer projectId);

    /**
     * 获取活动每日产出
     * @param id
     * @return
     */
    BigDecimal getActivityOutput(Integer id);
}
