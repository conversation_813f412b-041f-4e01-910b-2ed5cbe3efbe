package com.project.web;
import cn.hutool.core.util.StrUtil;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.model.Message;
import com.project.model.User;
import com.project.service.MessageService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
* Created by CodeGenerator on 2024/04/08.
*/
@Api(tags = "消息管理")
@RestController
@RequestMapping("/message")
public class MessageController {

	private static Logger log = LoggerFactory.getLogger(MessageController.class);

    @Resource
    private MessageService messageService;

	@Resource
	private UserUtil userUtil;



	@RequestMapping("/list")
	@ApiOperation(value = "message获取列表",httpMethod = "POST")
    public Result<List<Message>> list(@RequestBody Message message) {

        PageHelper.startPage(message.getPage(), message.getSize());
        
        Condition condition = new Condition(message.getClass());
        Criteria criteria = condition.createCriteria();
		if (StrUtil.isNotBlank(message.getContent())){
			criteria.andLike("content", "%"+message.getContent()+"%");
		}
		condition.setOrderByClause("id desc");
		PageInfo pageInfo = null;
		try {
    		 List<Message> list = messageService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }

	@RequestMapping("/myMessage")
	@ApiOperation(value = "我的消息",httpMethod = "POST")
	public Result<List<Message>> myMessage(@RequestBody Message message, HttpServletRequest request) {

		User user = userUtil.getTokenUser(request);
		PageHelper.startPage(message.getPage(), message.getSize());

		Condition condition = new Condition(message.getClass());
		Criteria criteria = condition.createCriteria();
		criteria.andEqualTo("userId",user.getId());
		if (message.getStatus() != null){
			criteria.andEqualTo("status",message.getStatus());
		}
		condition.setOrderByClause("id desc");
		PageInfo pageInfo = null;
		try {
			List<Message> list = messageService.findByCondition(condition);
			Condition c = new Condition(Message.class);
			c.createCriteria().andEqualTo("userId",user.getId());
			pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success(pageInfo);
	}

	@RequestMapping("/read")
	@ApiOperation(value = "读消息",httpMethod = "GET")
	public Result read(@RequestParam Integer id, HttpServletRequest request) {
		Message message = messageService.findById(id);
		if (message == null){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if (message.getStatus() == 1){
			messageService.update(Message.builder().status(2).id(id).build());
		}
		return Result.success();
	}
}