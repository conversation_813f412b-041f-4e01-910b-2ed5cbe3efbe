package com.project.web.parameters;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.project.core.BaseBeen;
import com.project.model.ProjectItemRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="项目明细")
public class ProjectItemBO extends BaseBeen implements Serializable {
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 项目id
     */
    @ApiModelProperty(value="projectId项目id")
    private Integer projectId;

    @ApiModelProperty(value="项目名称")
    private String projectName;

    @Column(name = "principal_user_id")
    @ApiModelProperty(value="明细负责人")
    private Integer principalUserId;

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    /**
     * 类型 1泥工 2木工 3瓷砖 4水电
     */
    @ApiModelProperty(value="type类型 1泥工 2木工 3瓷砖 4水电")
    private Integer type;

    /**
     * 状态 0未开始  1进行中 2待审核 3整改中 4已完成
     */
    @ApiModelProperty(value="status状态 0未开始  1进行中 2待审核 3整改中 4已完成")
    private Integer status;

    /**
     * 创建人
     */
    @ApiModelProperty(value="createUserId创建人")
    private String createUserId;

    /**
     * 更新人
     */
    @ApiModelProperty(value="updateUserId更新人")
    private String updateUserId;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value="beginTime开始时间")
    private Date beginTime;

    /**
     * 预计完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @ApiModelProperty(value="finishTime预计完成时间")
    private Date finishTime;

    /**
     * 预计天数
     */
    @ApiModelProperty(value="days预计天数")
    private Integer days;

    @ApiModelProperty(value="签到天数")
    private Integer signDays;



    /**
     * 评分
     */
    @ApiModelProperty(value="score评分")
    private Integer score;

    /**
     * 审核内容
     */
    @ApiModelProperty(value="audit审核内容")
    private String audit;

    @ApiModelProperty(value="amount预计金额")
    private BigDecimal amount;

    /**
     * 预计完成时间
     */
    @ApiModelProperty(value="审核时间")
    private Date auditTime;

    /**
     * 状态  1严重紧急 2严重 3紧急4不严重不紧急
     */
    @ApiModelProperty(value="auditType状态  1严重紧急 2严重 3紧急4不严重不紧急")
    private Integer auditType;

    /**
     * 类型 0未审核 1通过 2不通过
     */
    @ApiModelProperty(value="auditRet类型 0未审核 1通过 2不通过")
    private Integer auditRet;
    @ApiModelProperty(value="完成度")
    private Double completion;

    @ApiModelProperty(value="工作记录")
    private List<ProjectItemRecordBO> records;

    @ApiModelProperty(value="成员")
    private List<ProjectItemMemberBO> members;

    private static final long serialVersionUID = 1L;


}