package com.project.web.parameters;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
@ApiModel(value="首页")
public class IndexBO implements Serializable {
    @ApiModelProperty(value="总任务数")
    private Integer total;

    /**
     * 用户id
     */
    @ApiModelProperty(value="完成任务数")
    private Integer finish;

    @ApiModelProperty(value="其他")
    private Integer elses;

    @ApiModelProperty(value="进行中")
    private Integer ing;

    @ApiModelProperty(value="整改")
    private List<ProjectBO> abarbeitung;

    @ApiModelProperty(value="审核")
    private List<ProjectBO> audit;


}