package com.project.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.project.dao.MessageMapper;
import com.project.model.Message;
import com.project.model.User;
import com.project.model.UserRole;
import com.project.service.MessageService;
import com.project.core.AbstractService;
import com.project.service.SseService;
import com.project.service.UserRoleService;
import com.project.service.UserService;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;


/**
 * Created by CodeGenerator on 2024/04/08.
 */
@Service
@Transactional
public class MessageServiceImpl extends AbstractService<Message> implements MessageService {
    @Resource
    private MessageMapper bMessageMapper;

    @Resource
    private SseService sseService;


    @Resource
    private UserRoleService userRoleService;


    @Override
    public void updateByCondition(Message message,Condition c) {
        bMessageMapper.updateByConditionSelective(message,c);
    }

    @Override
    public void sendMessage(int userId, String message) throws IOException {
        sseService.push(userId+"",message);
    }

    @Override
    public void sendMessageToGM(String message) throws IOException {
        Condition c = new Condition(UserRole.class);
        c.createCriteria().andEqualTo("roleId",6);
        List<UserRole> list = userRoleService.findByCondition(c);
        if (CollUtil.isNotEmpty(list)){
            for (UserRole userRole:list) {
                sseService.push(userRole.getUserId()+"",message);
            }
        }
    }
}
