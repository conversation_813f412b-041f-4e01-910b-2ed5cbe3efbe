package com.project.service.impl;

import com.project.dao.TeamMapper;
import com.project.model.Team;
import com.project.service.TeamService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/01/22.
 */
@Service
@Transactional
public class TeamServiceImpl extends AbstractService<Team> implements TeamService {
    @Resource
    private TeamMapper bTeamMapper;

}
