package com.project.model;

import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import javax.persistence.*;

@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
@ApiModel(value="用户")
@Table(name = "b_user")
public class User extends BaseBeen implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 姓名
     */
    @ApiModelProperty(value="name姓名")
    private String name;

    @ApiModelProperty(value="地址")
    private String address;

    @ApiModelProperty(value="头像")
    private String icon;
    @Transient
    @ApiModelProperty(value="type 登录类型 1业主 2施工方 3项目方")
    private Integer type;

    @Column(name = "vip_level")
    @ApiModelProperty(value="窗口数")
    private Integer vipLevel;



    @ApiModelProperty(value="pid")
    private Integer pid;

    @Column(name = "invite_code")
    @ApiModelProperty(value="邀请码")
    private String inviteCode;

    /**
     * 登录账号
     */
    @Column(name = "login_account")
    @ApiModelProperty(value="loginAccount登录账号")
    private String loginAccount;

    /**
     * 密码
     */
    @ApiModelProperty(value="password密码")
    private String password;

    /**
     * 密码盐
     */
    @ApiModelProperty(value="salt密码盐")
    private String salt;

    /**
     * 手机号码
     */
    @Column(name = "phone_no")
    @ApiModelProperty(value="phoneNo手机号码")
    private String phoneNo;

    @Column(name = "work_no")
    @ApiModelProperty(value="工号")
    private String workNo;


    /**
     * 邮箱
     */
    @ApiModelProperty(value="email邮箱")
    private String email;

    /**
     * 状态 0 正常 1注销 
     */
    @ApiModelProperty(value="status状态 0 正常 1注销 ")
    private String status;

    /**
     * 公司id
     */
    @Column(name = "company_id")
    @ApiModelProperty(value="companyId公司id")
    private String companyId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    @Column(name = "vip_time")
    @ApiModelProperty(value="会员到期时间")
    private Date vipTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    /**
     * 创建人
     */
    @Column(name = "create_user_id")
    @ApiModelProperty(value="createUserId创建人")
    private String createUserId;

    /**
     * 更新人
     */
    @Column(name = "update_user_id")
    @ApiModelProperty(value="updateUserId更新人")
    private String updateUserId;

    private static final long serialVersionUID = 1L;


    @Transient
    private String token;

    @Transient
    private String roleName;

    @Transient
    private String roleIds;

    public String getRoleIds() {
        return roleIds;
    }

    public void setRoleIds(String roleIds) {
        this.roleIds = roleIds;
    }

    @Transient
    private List<UserRole> userRoles;

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public List<UserRole> getUserRoles() {
        return userRoles;
    }

    public void setUserRoles(List<UserRole> userRoles) {
        this.userRoles = userRoles;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取姓名
     *
     * @return name - 姓名
     */
    public String getName() {
        return name;
    }

    /**
     * 设置姓名
     *
     * @param name 姓名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取登录账号
     *
     * @return login_account - 登录账号
     */
    public String getLoginAccount() {
        return loginAccount;
    }

    /**
     * 设置登录账号
     *
     * @param loginAccount 登录账号
     */
    public void setLoginAccount(String loginAccount) {
        this.loginAccount = loginAccount;
    }

    /**
     * 获取密码
     *
     * @return password - 密码
     */
    public String getPassword() {
        return password;
    }

    /**
     * 设置密码
     *
     * @param password 密码
     */
    public void setPassword(String password) {
        this.password = password;
    }

    /**
     * 获取密码盐
     *
     * @return salt - 密码盐
     */
    public String getSalt() {
        return salt;
    }

    /**
     * 设置密码盐
     *
     * @param salt 密码盐
     */
    public void setSalt(String salt) {
        this.salt = salt;
    }

    /**
     * 获取手机号码
     *
     * @return phone_no - 手机号码
     */
    public String getPhoneNo() {
        return phoneNo;
    }

    /**
     * 设置手机号码
     *
     * @param phoneNo 手机号码
     */
    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    /**
     * 获取邮箱
     *
     * @return email - 邮箱
     */
    public String getEmail() {
        return email;
    }

    /**
     * 设置邮箱
     *
     * @param email 邮箱
     */
    public void setEmail(String email) {
        this.email = email;
    }

    /**
     * 获取状态 0 正常 1注销 
     *
     * @return status - 状态 0 正常 1注销 
     */
    public String getStatus() {
        return status;
    }

    /**
     * 设置状态 0 正常 1注销 
     *
     * @param status 状态 0 正常 1注销 
     */
    public void setStatus(String status) {
        this.status = status;
    }

    /**
     * 获取公司id
     *
     * @return company_id - 公司id
     */
    public String getCompanyId() {
        return companyId;
    }

    /**
     * 设置公司id
     *
     * @param companyId 公司id
     */
    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取创建人
     *
     * @return create_user_id - 创建人
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * 设置创建人
     *
     * @param createUserId 创建人
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 获取更新人
     *
     * @return update_user_id - 更新人
     */
    public String getUpdateUserId() {
        return updateUserId;
    }

    /**
     * 设置更新人
     *
     * @param updateUserId 更新人
     */
    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }
}