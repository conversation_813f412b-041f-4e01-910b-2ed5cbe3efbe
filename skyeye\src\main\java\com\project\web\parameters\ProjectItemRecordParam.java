package com.project.web.parameters;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Data
@ApiModel(value="签到或请假")
public class ProjectItemRecordParam implements Serializable {
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 项目id
     */
    @ApiModelProperty(value="projectId项目id")
    private Integer projectId;

    /**
     * 项目明细id
     */
    @ApiModelProperty(value="projectItemId项目明细id")
    private Integer projectItemId;

    /**
     * 团队id
     */
    @ApiModelProperty(value="teamId团队id")
    private Integer teamId;

    /**
     * 用户id
     */
    @ApiModelProperty(value="userId用户id")
    private Integer userId;

    /**
     * 上班图片
     */
    @ApiModelProperty(value="onPics上班图片")
    private String onPics;

    /**
     * 下班图片
     */
    @ApiModelProperty(value="offPics下班图片")
    private String offPics;

    /**
     * 特殊情况
     */
    @ApiModelProperty(value="special特殊情况")
    private String special;

    /**
     * 签到天
     */
    @Column(name = "sign_day")
    @ApiModelProperty(value="signDay签到天")
    private String signDay;

    /**
     * 上班签到时间
     */
    @ApiModelProperty(value="onTime上班签到时间")
    private Date onTime;

    @ApiModelProperty(value="内容")
    private String content;

    /**
     * 下班签到时间
     */
    @ApiModelProperty(value="offTime下班签到时间")
    private Date offTime;

    /**
     * 类型 1上班 2请假
     */
    @ApiModelProperty(value="type类型 1上班 2请假 3 早上签到 4下午签到")
    private Integer type;

    /**
     * 更新时间
     */
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    private static final long serialVersionUID = 1L;


}