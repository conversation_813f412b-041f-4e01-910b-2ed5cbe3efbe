package com.project.model;

import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Data
@ApiModel(value="com.project.model.ProjectItem")
@Table(name = "b_project_item")
public class ProjectItem extends BaseBeen implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 项目id
     */
    @Column(name = "project_id")
    @ApiModelProperty(value="projectId项目id")
    private Integer projectId;

    @ApiModelProperty(value="amount预计金额")
    private BigDecimal amount;


    @Column(name = "principal_user_id")
    @ApiModelProperty(value="明细负责人")
    private Integer principalUserId;


    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    /**
     * 类型 1泥工 2木工 3瓷砖 4水电
     */
    @ApiModelProperty(value="type类型 1泥工 2木工 3瓷砖 4水电")
    private Integer type;

    /**
     * 状态 0未开始  1进行中 2待审核 3整改中 4已完成
     */
    @ApiModelProperty(value="status状态 0未开始  1进行中 2待审核 3整改中 4已完成")
    private Integer status;

    /**
     * 创建人
     */
    @Column(name = "create_user_id")
    @ApiModelProperty(value="createUserId创建人")
    private Integer createUserId;

    /**
     * 更新人
     */
    @Column(name = "update_user_id")
    @ApiModelProperty(value="updateUserId更新人")
    private Integer updateUserId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    /**
     * 开始时间
     */
    @Column(name = "begin_time")
    @ApiModelProperty(value="beginTime开始时间")
    private Date beginTime;

    /**
     * 预计完成时间
     */
    @Column(name = "finish_time")
    @ApiModelProperty(value="finishTime预计完成时间")
    private Date finishTime;

    /**
     * 预计天数
     */
    @ApiModelProperty(value="days预计天数")
    private Integer days;

    /**
     * 评分
     */
    @ApiModelProperty(value="score评分")
    private Integer score;

    /**
     * 审核内容
     */
    @ApiModelProperty(value="audit审核内容")
    private String audit;

    /**
     * 预计完成时间
     */
    @Column(name = "audit_time")
    @ApiModelProperty(value="auditTime预计完成时间")
    private Date auditTime;

    /**
     * 状态  1严重紧急 2严重 3紧急4不严重不紧急
     */
    @Column(name = "audit_type")
    @ApiModelProperty(value="auditType状态  1严重紧急 2严重 3紧急4不严重不紧急")
    private Integer auditType;

    /**
     * 类型 1通过 2不通过
     */
    @Column(name = "audit_ret")
    @ApiModelProperty(value="auditRet类型 1通过 2不通过")
    private Integer auditRet;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取项目id
     *
     * @return project_id - 项目id
     */
    public Integer getProjectId() {
        return projectId;
    }

    /**
     * 设置项目id
     *
     * @param projectId 项目id
     */
    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    /**
     * 获取名称
     *
     * @return name - 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取类型 1泥工 2木工 3瓷砖 4水电
     *
     * @return type - 类型 1泥工 2木工 3瓷砖 4水电
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置类型 1泥工 2木工 3瓷砖 4水电
     *
     * @param type 类型 1泥工 2木工 3瓷砖 4水电
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取状态 0未开始  1进行中 2待审核 3整改中 4已完成
     *
     * @return status - 状态 0未开始  1进行中 2待审核 3整改中 4已完成
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 0未开始  1进行中 2待审核 3整改中 4已完成
     *
     * @param status 状态 0未开始  1进行中 2待审核 3整改中 4已完成
     */
    public void setStatus(Integer status) {
        this.status = status;
    }


    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取开始时间
     *
     * @return begin_time - 开始时间
     */
    public Date getBeginTime() {
        return beginTime;
    }

    /**
     * 设置开始时间
     *
     * @param beginTime 开始时间
     */
    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    /**
     * 获取预计完成时间
     *
     * @return finish_time - 预计完成时间
     */
    public Date getFinishTime() {
        return finishTime;
    }

    /**
     * 设置预计完成时间
     *
     * @param finishTime 预计完成时间
     */
    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    /**
     * 获取预计天数
     *
     * @return days - 预计天数
     */
    public Integer getDays() {
        return days;
    }

    /**
     * 设置预计天数
     *
     * @param days 预计天数
     */
    public void setDays(Integer days) {
        this.days = days;
    }

    /**
     * 获取评分
     *
     * @return score - 评分
     */
    public Integer getScore() {
        return score;
    }

    /**
     * 设置评分
     *
     * @param score 评分
     */
    public void setScore(Integer score) {
        this.score = score;
    }

    /**
     * 获取审核内容
     *
     * @return audit - 审核内容
     */
    public String getAudit() {
        return audit;
    }

    /**
     * 设置审核内容
     *
     * @param audit 审核内容
     */
    public void setAudit(String audit) {
        this.audit = audit;
    }

    /**
     * 获取预计完成时间
     *
     * @return audit_time - 预计完成时间
     */
    public Date getAuditTime() {
        return auditTime;
    }

    /**
     * 设置预计完成时间
     *
     * @param auditTime 预计完成时间
     */
    public void setAuditTime(Date auditTime) {
        this.auditTime = auditTime;
    }

    /**
     * 获取状态  1严重紧急 2严重 3紧急4不严重不紧急
     *
     * @return audit_type - 状态  1严重紧急 2严重 3紧急4不严重不紧急
     */
    public Integer getAuditType() {
        return auditType;
    }

    /**
     * 设置状态  1严重紧急 2严重 3紧急4不严重不紧急
     *
     * @param auditType 状态  1严重紧急 2严重 3紧急4不严重不紧急
     */
    public void setAuditType(Integer auditType) {
        this.auditType = auditType;
    }

    /**
     * 获取类型 1通过 2不通过
     *
     * @return audit_ret - 类型 1通过 2不通过
     */
    public Integer getAuditRet() {
        return auditRet;
    }

    /**
     * 设置类型 0待审核 1通过 2不通过
     *
     * @param auditRet 类型 1通过 2不通过
     */
    public void setAuditRet(Integer auditRet) {
        this.auditRet = auditRet;
    }

    public Integer getPrincipalUserId() {
        return principalUserId;
    }

    public void setPrincipalUserId(Integer principalUserId) {
        this.principalUserId = principalUserId;
    }
}