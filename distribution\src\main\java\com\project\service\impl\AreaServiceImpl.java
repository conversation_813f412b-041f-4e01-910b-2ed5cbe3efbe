package com.project.service.impl;

import cn.hutool.core.util.StrUtil;
import com.project.dao.AreaMapper;
import com.project.model.Area;
import com.project.service.AreaService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2024/11/28.
 */
@Service
@Transactional
public class AreaServiceImpl extends AbstractService<Area> implements AreaService {
    @Resource
    private AreaMapper bAreaMapper;

    @Override
    public String getAreaName(String code) {
        String str = "";
        Area area = findBy("areaCode",code);
        if (area != null){
            str = area.getAreaName() + str;
            if (!StrUtil.equals("1",area.getAreaParentCode())){
                return getAreaName(area.getAreaParentCode()) + str;
            } else {
                return str;
            }
        } else {
            return str;
        }
    }

}
