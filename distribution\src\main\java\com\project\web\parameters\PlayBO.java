package com.project.web.parameters;

import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Data
@ApiModel(value="小玩法")
public class PlayBO extends BaseBeen implements Serializable {
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    /**
     * 链接
     */
    @ApiModelProperty(value="link链接")
    private String link;

    @ApiModelProperty(value="icon图标")
    private String payLink3;

    @ApiModelProperty(value="icon图标")
    private String payLink2;

    @ApiModelProperty(value="icon图标")
    private String payLink;

    @ApiModelProperty(value="案例图标")
    private String examplePic;

    /**
     * 图标
     */
    @ApiModelProperty(value="icon图标")
    private String icon;

    @ApiModelProperty(value="是否已解锁")
    private Integer isOpen;

    @ApiModelProperty(value="disabledTime失效时间")
    private Date disabledTime;

    /**
     * vip等级
     */
    @ApiModelProperty(value="vipLevelvip等级")
    private Integer vipLevel;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    private static final long serialVersionUID = 1L;


}