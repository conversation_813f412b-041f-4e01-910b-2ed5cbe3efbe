package com.project.web;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.model.Ore;
import com.project.service.OreService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.util.List;

/**
* Created by CodeGenerator on 2025/05/13.
*/
@Api(tags = "矿机管理")
@RestController
@RequestMapping("/ore")
public class OreController {

	private static Logger log = LoggerFactory.getLogger(OreController.class);

    @Resource
    private OreService oreService;



    @RequestMapping("/detail")
	@ApiOperation(value = "ore获取详情",httpMethod = "GET")
    public Result<Ore> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	Ore ore = null;
    	try {
    		ore = oreService.findById(id);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success(ore);
    }

    @RequestMapping("/list")
	@ApiOperation(value = "ore获取列表",httpMethod = "POST")
    public Result<List<Ore>> list(@RequestBody Ore ore) {

 //       PageHelper.startPage(page, size);
        
        Condition condition = new Condition(ore.getClass());
        Criteria criteria = condition.createCriteria();
//        criteria.andEqualTo("name", city.getName());
		PageInfo pageInfo = null;
		try {
    		 List<Ore> list = oreService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
