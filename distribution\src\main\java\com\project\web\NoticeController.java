package com.project.web;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.model.Notice;
import com.project.model.User;
import com.project.service.NoticeService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.Data;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
* Created by CodeGenerator on 2024/04/08.
*/
@Api(tags = "通知管理")
@RestController
@RequestMapping("/notice")
public class NoticeController {

	private static Logger log = LoggerFactory.getLogger(NoticeController.class);

    @Resource
    private NoticeService noticeService;

	@Resource
	private UserUtil userUtil;


	@PostMapping("/add")
	@ApiOperation(value = "notice新增",httpMethod = "POST")
    public Result add(@RequestBody Notice notice, HttpServletRequest request) {
    	if(notice == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
		if(StrUtil.isBlank(notice.getContent())){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
    	try {
			notice.setId(null);
			User user = userUtil.getTokenUser(request);
			notice.setStatus(1);
    		notice.setCreateTime(DateUtil.date());
    		notice.setCreateUser(user.getName());
    		noticeService.save(notice);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @RequestMapping("/delete")
	@ApiOperation(value = "notice删除",httpMethod = "GET")
    public Result delete(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	try {
    		noticeService.deleteById(id);
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @GetMapping("/updateStatus")
	@ApiOperation(value = "更新状态",httpMethod = "GET")
    public Result updateStatus(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	try {
			Notice notice = noticeService.findById(id);
			if (notice == null){
				return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
			}
			int status = 0;
			if (notice.getStatus() == 1){
				status = 2;
			} else {
				status = 1;
			}
    		noticeService.update(Notice.builder().status(status).build());
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

	@PostMapping("/update")
	@ApiOperation(value = "更新",httpMethod = "POST")
	public Result update(@RequestBody Notice update) {
		if(update == null){
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if(update.getId() == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			noticeService.update(update);
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success();
	}



    @RequestMapping("/list")
	@ApiOperation(value = "notice获取列表",httpMethod = "POST")
    public Result<List<Notice>> list(@RequestBody Notice notice) {
        PageHelper.startPage(notice.getPage(), notice.getSize());
        Condition condition = new Condition(notice.getClass());
        Criteria criteria = condition.createCriteria();
		if (notice.getStatus() != null){
			criteria.andEqualTo("status", notice.getStatus());
			if (notice.getStatus() == 2){
				criteria.andGreaterThan("loseTime", DateUtil.format(DateUtil.date(), "yyyy-MM-dd")+" 23:59:59");
			}
		}
		condition.setOrderByClause("create_time desc");
		PageInfo pageInfo = null;
		try {
    		 List<Notice> list = noticeService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
