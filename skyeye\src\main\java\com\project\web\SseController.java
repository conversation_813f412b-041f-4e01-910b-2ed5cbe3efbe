package com.project.web;

import cn.hutool.json.JSONUtil;
import com.project.core.NoToken;
import com.project.service.SseService;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@RestController
@RequestMapping(path = "sse")
public class SseController {

    @Resource
    private SseService sseService;



    @NoToken
    @GetMapping(path = "subscribe", produces = { MediaType.TEXT_EVENT_STREAM_VALUE})
    public SseEmitter subscribe(String id) {
        return sseService.subscribe(id);
    }

    @NoToken
    @GetMapping(path = "push")
    public String push(String id, String content) {
        return sseService.push(id,content);
    }

    @NoToken
    @GetMapping(path = "over")
    public String over(String id) {
        return sseService.over(id);
    }


//    @Resource
//    RabbitTemplate rabbitTemplate;
    @GetMapping(path = "testMq")
    public String testMq() {
        HashMap<Object, Object> hashMap = new HashMap<>();
        hashMap.put("name","zhangsan");
        hashMap.put("age","18");
//        rabbitTemplate.convertAndSend("fanoutSse","", JSONUtil.toJsonStr(hashMap));
        return "OK";
    }
}
