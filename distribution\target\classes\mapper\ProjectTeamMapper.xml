<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.ProjectTeamMapper">
  <resultMap id="BaseResultMap" type="com.project.model.ProjectTeam">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="member_user_id" jdbcType="INTEGER" property="memberUserId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="icon" jdbcType="VARCHAR" property="icon" />
    <result column="location" jdbcType="INTEGER" property="location" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>