<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.ComplainMapper">
  <resultMap id="BaseResultMap" type="com.project.model.Complain">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="pics" jdbcType="VARCHAR" property="pics" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="notarize_user_id" jdbcType="INTEGER" property="notarizeUserId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>