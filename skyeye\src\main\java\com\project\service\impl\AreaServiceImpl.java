package com.project.service.impl;

import com.project.dao.AreaMapper;
import com.project.model.Area;
import com.project.service.AreaService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2024/11/28.
 */
@Service
@Transactional
public class AreaServiceImpl extends AbstractService<Area> implements AreaService {
    @Resource
    private AreaMapper bAreaMapper;

}
