<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.ProjectItemRecordMapper">
  <resultMap id="BaseResultMap" type="com.project.model.ProjectItemRecord">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="project_id" jdbcType="INTEGER" property="projectId" />
    <result column="project_item_id" jdbcType="INTEGER" property="projectItemId" />
    <result column="team_id" jdbcType="INTEGER" property="teamId" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />
    <result column="on_pics" jdbcType="VARCHAR" property="onPics" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="off_pics" jdbcType="VARCHAR" property="offPics" />
    <result column="special" jdbcType="VARCHAR" property="special" />
    <result column="sign_day" jdbcType="VARCHAR" property="signDay" />
    <result column="on_time" jdbcType="TIMESTAMP" property="onTime" />
    <result column="off_time" jdbcType="TIMESTAMP" property="offTime" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>