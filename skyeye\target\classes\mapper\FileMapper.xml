<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.FileMapper">
  <resultMap id="BaseResultMap" type="com.project.model.File">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="file_name" jdbcType="VARCHAR" property="fileName" />
    <result column="path" jdbcType="VARCHAR" property="path" />
    <result column="token" jdbcType="VARCHAR" property="token" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
  </resultMap>
</mapper>