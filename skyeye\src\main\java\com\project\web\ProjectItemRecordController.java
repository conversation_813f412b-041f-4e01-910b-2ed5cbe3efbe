package com.project.web;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.model.ProjectItemRecord;
import com.project.service.ProjectItemRecordService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.util.List;

/**
* Created by CodeGenerator on 2025/01/26.
*/
@Api(tags = "projectItemRecord管理")
@RestController
@RequestMapping("/project/item/record")
public class ProjectItemRecordController {

	private static Logger log = LoggerFactory.getLogger(ProjectItemRecordController.class);

    @Resource
    private ProjectItemRecordService projectItemRecordService;



    @PostMapping("/update")
	@ApiOperation(value = "projectItemRecord更新",httpMethod = "POST")
    public Result update(@RequestBody ProjectItemRecord projectItemRecord) {
    	if(projectItemRecord == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	if(projectItemRecord.getId() == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	try {
    //		projectItemRecord.setUpdateTime(new Date());
    //		projectItemRecord.setUpdateUserId(userId);
    		projectItemRecordService.update(projectItemRecord);
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @RequestMapping("/detail")
	@ApiOperation(value = "projectItemRecord获取详情",httpMethod = "POST")
    public Result<ProjectItemRecord> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	ProjectItemRecord projectItemRecord = null;
    	try {
    		projectItemRecord = projectItemRecordService.findById(id);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success(projectItemRecord);
    }

    @RequestMapping("/list")
	@ApiOperation(value = "projectItemRecord获取列表",httpMethod = "POST")
    public Result<List<ProjectItemRecord>> list(@RequestBody ProjectItemRecord projectItemRecord, @RequestParam(defaultValue = "0") Integer page, @RequestParam(defaultValue = "10") Integer size) {

        PageHelper.startPage(page, size);
        
        Condition condition = new Condition(projectItemRecord.getClass());
        Criteria criteria = condition.createCriteria();
//        criteria.andEqualTo("name", city.getName());
		PageInfo pageInfo = null;
		try {
    		 List<ProjectItemRecord> list = projectItemRecordService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
