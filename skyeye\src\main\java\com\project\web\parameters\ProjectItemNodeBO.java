package com.project.web.parameters;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Data
@ApiModel(value="项目节点")
public class ProjectItemNodeBO implements Serializable {
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 项目id
     */
    @ApiModelProperty(value="projectId项目id")
    private Integer projectId;

    /**
     * 项目明细id
     */
    @ApiModelProperty(value="projectItemId项目明细id")
    private Integer projectItemId;

    /**
     * 节点名称
     */
    @ApiModelProperty(value="name节点名称")
    private String name;

    /**
     * 进度值
     */
    @ApiModelProperty(value="progress进度值")
    private Integer progress;

    /**
     * 耗时
     */
    @ApiModelProperty(value="elapsedTime耗时")
    private Integer elapsedTime;

    /**
     * 更新时间
     */
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    private static final long serialVersionUID = 1L;
}