package com.project.enums;


/**
 * 类型  1体 2功 3防 4暴
 */
public enum AttrBizTypeEnum {

    LIFE(1,"1","体"),
    ATK(2,"2","功"),
    DEFENSE(3,"3","防"),
    CRIT(4,"4","暴")
    ;
    private Integer key;
    private String desc;
    private String code;

    AttrBizTypeEnum(Integer key, String code, String desc) {
        this.key = key;
        this.code = code;
        this.desc = desc;
    }

    public  static AttrBizTypeEnum getEnum(int key){
        for(AttrBizTypeEnum e:AttrBizTypeEnum.values()){
            if(key == e.getKey().intValue()){
                return e;
            }
        }
        return null;
    }

    public Integer getKey() {
        return key;
    }

    public void setKey(Integer key) {
        this.key = key;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }
}
