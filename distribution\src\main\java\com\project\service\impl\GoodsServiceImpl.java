package com.project.service.impl;

import com.project.dao.GoodsMapper;
import com.project.model.Goods;
import com.project.service.GoodsService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/04/25.
 */
@Service
@Transactional
public class GoodsServiceImpl extends AbstractService<Goods> implements GoodsService {
    @Resource
    private GoodsMapper cGoodsMapper;

}
