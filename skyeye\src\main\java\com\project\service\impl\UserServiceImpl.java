package com.project.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.project.core.ResultCode;
import com.project.core.ResultGenerator;
import com.project.dao.UserMapper;
import com.project.model.Team;
import com.project.model.User;
import com.project.model.UserRole;
import com.project.service.TeamService;
import com.project.service.UserRoleService;
import com.project.service.UserService;
import com.project.core.AbstractService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;


/**
 * Created by CodeGenerator on 2024/04/08.
 */
@Service
@Transactional
public class UserServiceImpl extends AbstractService<User> implements UserService {
    @Resource
    private UserMapper bUserMapper;

    @Resource
    private UserRoleService userRoleService;

    @Resource
    private TeamService teamService;

    @Override
    public void saveAndBandRole(User user, String roleIds, String code) {
        Team team = null;
        if (StrUtil.isNotBlank(code)){
            User p = findBy("inviteCode", code);
            if (p != null){
                team = new Team();
                team.setStatus("0");
                team.setCompanyId(p.getCompanyId());
                team.setCreateTime(DateUtil.date());
                team.setUserId(p.getId());
                team.setMemberName(user.getName());

                user.setPid(p.getId());
            }
        }
        if (saveUseGeneratedKeys(user) > 0) {
            if (team != null){
                team.setMemberId(user.getId());
                teamService.save(team);
            }
            String[] ids = roleIds.split(",");
            List<UserRole> temp = new ArrayList<UserRole>();
            for (String rId : ids) {
                UserRole cd = new UserRole();
                cd.setRoleId(Integer.parseInt(rId));
                cd.setUserId(user.getId());
                temp.add(cd);
            }
            if (temp != null) {
                Condition condition = new Condition(UserRole.class);
                Example.Criteria criteria = condition.createCriteria();
                criteria.andEqualTo("userId", user.getId());
                userRoleService.deleteByCondition(condition);
                userRoleService.save(temp);
            }
        }
    }

    @Override
    public void updateAndBandRole(User user, String roleIds) {
        update(user);
        if (StringUtils.isNotBlank(roleIds)) {
            String[] ids = roleIds.split(",");
            List<UserRole> temp = new ArrayList<UserRole>();
            for (String rId : ids) {
                UserRole cd = new UserRole();
                cd.setRoleId(Integer.parseInt(rId));
                cd.setUserId(user.getId());
                temp.add(cd);
            }
            if (temp != null) {
                Condition condition = new Condition(UserRole.class);
                Example.Criteria criteria = condition.createCriteria();
                criteria.andEqualTo("userId", user.getId());
                userRoleService.deleteByCondition(condition);
                userRoleService.save(temp);
            }
        }
    }

    @Override
    public boolean check(User user) {
        if (user.getVipTime() == null){
            return false;
        }
        if (DateUtil.compare(DateUtil.date(),user.getVipTime())>=0){
            return false;
        }
        return true;
    }

}
