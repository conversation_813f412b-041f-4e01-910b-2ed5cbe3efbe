package com.project.core;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@Data
@RefreshScope
public class Parameters {
    /**
     *
     当前激活的配置文件
     */
    @Value("${spring.profiles.active}")
    private String        env;

    /**
     *
     是否启用签名
     */
    @Value("${signature}")
    private String        signature;

//    /**
//     * 是否启用登录验证
//     */
//    @Value("${dubbo.registry.address}")
//    private String        dubboRegistryAddress;

    /**
     * 是否启用登录验证
     */
    @Value("${login.check}")
    private String        loginCheck;

    @Value("${spring.application.name}")
    private String        application;

    @Value("${upload.dir}")
    private String        uploadDir;



}
