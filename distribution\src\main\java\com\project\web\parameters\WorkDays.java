package com.project.web.parameters;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: lxk
 * @Date: 2019/12/14 13:56
 * @Version 1.0
 */
@Data
@ApiModel("工时")
public class WorkDays implements Serializable {

    private static final long serialVersionUID = -5318595472853014732L;

    /**
     * 上级区域id
     */
    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("值")
    private Integer val;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("记录id")
    private Integer id;
}
