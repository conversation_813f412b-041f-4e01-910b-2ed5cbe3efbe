package com.project.web;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.project.model.*;
import com.project.service.*;
import com.project.core.Result;
import com.project.core.ResultCode;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.web.parameters.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.*;
import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by CodeGenerator on 2019/11/01.
 */
@Api(tags = "用户管理")
@RestController
@RequestMapping("/user")
public class UserController {

	private static Logger log = LoggerFactory.getLogger(UserController.class);

	@Resource
	private UserService userService;

	@Resource
	private FlowService flowService;

	@Resource
	private UserUtil userUtil;

	@Resource
	private UserRoleService userRoleService;

	@Resource
	private RoleService roleService;

	@Resource
	private ProjectAmountService projectAmountService;

	@Resource
	private CompanyService companyService;

	@PostMapping("/add")
	@ApiOperation(value = "user新增",httpMethod = "POST")
	public Result add(@RequestBody UserParameters parameters, HttpServletRequest request) {
		if (parameters == null) {
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if (StrUtil.isBlank(parameters.getAccount())) {
			return ResultGenerator.genFailResult(ResultCode.LOGIN_ACCOUNT_IS_NULL);
		}
		if (StrUtil.isBlank(parameters.getRoleIds())) {
			return ResultGenerator.genFailResult(ResultCode.ROLEIDS_IS_NULL);
		}
		if (StrUtil.isBlank(parameters.getPwd())) {
			return ResultGenerator.genFailResult(ResultCode.LOGIN_PASSWORD_IS_NULL);
		}
		try {
			User tokenUser = userUtil.getTokenUser(request);
			if (tokenUser == null){
				return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
			}
			String salt = RandomUtil.randomString(4);
			User user = new User();
			user.setLoginAccount(parameters.getAccount());
			user.setSalt(salt);
			user.setIcon("https://pasture-**********.cos.ap-shanghai.myqcloud.com/yc/head.jpg");
			user.setName(parameters.getName());
			user.setPhoneNo(parameters.getPhoneNo());
			user.setAddress(parameters.getAddress());
			user.setWorkNo(parameters.getWorkNo());
			user.setStatus("0");
			user.setVipLevel(1);
			user.setPassword(SecureUtil.md5(parameters.getPwd() + salt).toUpperCase());
			user.setCreateTime(new Date());
			user.setCreateUserId(tokenUser.getName());
			userService.saveAndBandRole(user, parameters.getRoleIds(), null);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return ResultGenerator.genSuccessResult();
	}


	@RequestMapping("/status")
	@ApiOperation(value = "状态改变:启用禁用",httpMethod = "GET")
	public Result status(@RequestParam Integer id) {
		if(id == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			User temp = userService.findById(id);
			if (temp == null) {
				return ResultGenerator.genFailResult(ResultCode.RESULT_IS_NULL);
			}
			User temp2 = new User();
			temp2.setId(id);
			if(StrUtil.equals(temp.getStatus(),"1")){
				temp2.setStatus("0");
			} else {
				temp2.setStatus("1");
			}
			userService.update(temp2);

		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return ResultGenerator.genSuccessResult();
	}

	@PostMapping("/update")
	@ApiOperation(value = "user更新",httpMethod = "POST")
	public Result update(@RequestBody User param, HttpServletRequest request) {
		if (param == null) {
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if (param.getId() == null) {
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			User tokenUser = userUtil.getTokenUser(request);
			if (tokenUser == null){
				return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
			}
			User temp = userService.findById(param.getId());
			if (temp == null) {
				return ResultGenerator.genFailResult(ResultCode.RESULT_IS_NULL);
			}
			User user = new User();
			user.setId(param.getId());
			user.setName(param.getName());
			user.setIcon(param.getIcon());
			user.setUpdateTime(new Date());
//			user.setLoginAccount(param.getName());
			user.setPassword(null);
			user.setUpdateUserId(tokenUser.getName());
			if (StrUtil.isNotBlank(user.getRoleIds())){
				userService.updateAndBandRole(user, user.getRoleIds());
			} else {
				userService.update(user);
			}
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return ResultGenerator.genSuccessResult();
	}


	@RequestMapping("/share")
	@ApiOperation(value = "分享注册链接（二维码）",httpMethod = "GET")
	public Result<User> share(HttpServletRequest request) {
		User tokenUser =userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		String url = "#/pages/Register/Register?code="+tokenUser.getInviteCode();
		return ResultGenerator.genSuccessResult(url);
	}

	@RequestMapping("/exchange")
	@ApiOperation(value = "切换角色 type：1业主 2施工方 3项目方",httpMethod = "GET")
	public Result<User> exchange(@RequestParam Integer type, HttpServletRequest request) {
		User tokenUser =userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		tokenUser.setType(type);
		userUtil.updateTokenUser(tokenUser,request);
		return ResultGenerator.genSuccessResult(tokenUser);
	}
	@RequestMapping("/detail")
	@ApiOperation(value = "user获取详情",httpMethod = "GET")
	public Result<UserBO> detail(@RequestParam Integer id, HttpServletRequest request) {

		if(id == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		User user = null;
		try {
			user = userService.findById(id);
			if (user == null){
				return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
			}
			user.setPassword("******");
			user.setTranspwd("******");
			user.setSalt("***");
			user.setType(tokenUser.getType());
			UserBO bo = BeanUtil.toBean(user,UserBO.class);
			Condition condition = new Condition(UserRole.class);
			Criteria criteria = condition.createCriteria();
			criteria.andEqualTo("userId", id + "");
			List<UserRole> list = userRoleService.findByCondition(condition);
			bo.setUserRoles(list);
			Condition c = new Condition(Flow.class);
			c.createCriteria().andEqualTo("userId",id).andEqualTo("bizType",9)
					.andGreaterThanOrEqualTo("createTime", DateUtil.today());
			List<Flow> flows = flowService.findByCondition(c);
			if (CollUtil.isNotEmpty(flows)){
				bo.setSignStatus(2);
			} else {
				bo.setSignStatus(1);
			}
			bo.setActivityOutput(projectAmountService.getActivityOutput(id));
			if (user.getPid()!= null){
				User parent = userService.findById(user.getPid());
				parent.setPassword("******");
				parent.setTranspwd("******");
				parent.setSalt("***");
				UserBO pret = BeanUtil.toBean(parent,UserBO.class);
				bo.setParent(pret);
			}
			if (user.getCompanyId()!= null){
				Company company = companyService.findById(user.getCompanyId());
				bo.setCompany(company);
			}

			return ResultGenerator.genSuccessResult(bo);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}


	}

	@RequestMapping("/searching")
	@ApiOperation(value = "检索用户",httpMethod = "GET")
	public Result<List<User>> searching(@RequestParam String account) {
		Condition condition = new Condition(User.class);
		Criteria criteria = condition.or();
		criteria.andLike("loginAccount", "%" + account + "%");
		try {
			List<User> list = userService.findByCondition(condition);
			for (User user2 : list) {
				user2.setPassword("******");
				user2.setSalt("***");
			}
			return ResultGenerator.genSuccessResult(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
	}
	@PostMapping("/list")
	@ApiOperation(value = "user获取列表",httpMethod = "POST")
	public Result<List<User>> list(@RequestBody User user) {
		PageHelper.startPage(user.getPage(), user.getSize());
		Condition condition = new Condition(User.class);
		Criteria criteria = condition.or();
		Criteria criteria1 = condition.or();
		if (StrUtil.isNotBlank(user.getName())) {
			criteria.andLike("loginAccount", "%" + user.getName() + "%");
			if (NumberUtil.isNumber(user.getName())){
				criteria1.andEqualTo("id",user.getName());
			}
		}
		if (StrUtil.isNotBlank(user.getStatus())) {
			criteria.andEqualTo("status", user.getStatus());
			criteria1.andEqualTo("status", user.getStatus());
		}
		if (StrUtil.isNotBlank(user.getStartTime())) {
			criteria.andGreaterThanOrEqualTo("createTime", user.getStartTime());
			criteria1.andGreaterThanOrEqualTo("createTime", user.getStartTime());
		}
		if (StrUtil.isNotBlank(user.getEndTime())) {
			criteria.andLessThanOrEqualTo("createTime", user.getEndTime());
			criteria1.andLessThanOrEqualTo("createTime", user.getEndTime());
		}

		PageInfo pageInfo = null;
		try {
			condition.setOrderByClause("id desc");
			List<User> list = userService.findByCondition(condition);
			for (User user2 : list) {
				user2.setPassword("******");
				user2.setSalt("***");
            }
			pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return ResultGenerator.genSuccessResult(pageInfo);
	}

	/**
	 * 重置密码
	 *
	 * @return
	 */
	@PostMapping("/restpwd")
	@ApiOperation(value = "重置密码",httpMethod = "POST")
	public Result<User> restpwd(@RequestBody RestpwdParameters parameters, HttpServletRequest request) {
		// 校验权限
		if (parameters == null) {
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if (parameters.getUserId() == null) {
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			User tokenUser =userUtil.getTokenUser(request);
			if (tokenUser == null){
				return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
			}
			User temp = userService.findById(parameters.getUserId());
			if (temp == null) {
				return ResultGenerator.genFailResult(ResultCode.RESULT_IS_NULL);
			}
			User user = new User();
			user.setId(parameters.getUserId());
			user.setUpdateTime(new Date());
			user.setUpdateUserId(tokenUser.getName());
			if (!StrUtil.equals(user.getAddress(),parameters.getAddress()) && StrUtil.isNotBlank(parameters.getAddress())){
				user.setAddress(parameters.getAddress());
			}
			if (StrUtil.isNotBlank(parameters.getPwd())) {
				user.setPassword(SecureUtil.md5((parameters.getPwd() + temp.getSalt())).toUpperCase());
			}
			userService.update(user);
			User ret = userService.findById(parameters.getUserId());
			Condition condition2 = new Condition(UserRole.class);
			Criteria criteria2 = condition2.createCriteria();
			criteria2.andEqualTo("userId", ret.getId() + "");
			List<UserRole> list2 = userRoleService.findByCondition(condition2);
			if (CollUtil.isNotEmpty(list2)){
				ret.setUserRoles(list2);
				Role role = roleService.findById(list2.get(0).getRoleId());
				if (role != null){
					ret.setRoleName(role.getName());
					ret.setType(role.getId());
				}
			}
			return ResultGenerator.genSuccessResult(ret);
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
	}

	@GetMapping("/statistics")
	@ApiOperation(value = "统计 type :0直推人数 1直推有效人数 2直推收益 3成团奖励 4成团分佣",httpMethod = "GET")
	public Result<List<ValBo>> statistics(@RequestParam Integer type, HttpServletRequest request) {
		List<ValBo> ret = new ArrayList<>();
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		if (type == 0){
			ret = userService.statistic(tokenUser.getId());
		} else if (type >= 1){
			ret = flowService.statistic(tokenUser.getId(),type);
		}
		return ResultGenerator.genSuccessResult(ret);
	}

	@GetMapping("/sign")
	@ApiOperation(value = "签到",httpMethod = "GET")
	public Result<BigDecimal> sign(HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		tokenUser = userService.findById(tokenUser.getId());
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		if (StrUtil.equals(tokenUser.getStatus() ,"1")){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NOT_ACTIVE);
		}
		Condition c = new Condition(ProjectAmount.class);
		c.createCriteria().andEqualTo("userId",tokenUser.getId());
		List<ProjectAmount> pas = projectAmountService.findByCondition(c);
		if (CollUtil.isEmpty(pas)){
			return ResultGenerator.genFailResult(ResultCode.ORDER_IS_NOT_HAVE);
		}
		boolean flag = false;
		for (ProjectAmount pa:pas){
			if (pa.getActivityOutput() .compareTo(BigDecimal.ZERO) >0){
				flag = true;
			}
		}
		if (!flag){
			return ResultGenerator.genFailResult(ResultCode.ORDER_IS_NOT_HAVE);
		}
		User u = userService.findById(tokenUser.getId());
		BigDecimal amount = BigDecimal.ZERO;
		c = new Condition(Flow.class);
		c.createCriteria().andEqualTo("userId",u.getId()).andEqualTo("bizType",9)
				.andGreaterThanOrEqualTo("createTime", DateUtil.today());
		List<Flow> flows = flowService.findByCondition(c);
		if (CollUtil.isNotEmpty(flows)){
			return ResultGenerator.genFailResult(ResultCode.SIGNED);
		}

		for (ProjectAmount pa:pas){
			BigDecimal residue =  pa.getActivityLimit().subtract(pa.getActivityAmount());
			BigDecimal add = pa.getActivityOutput();
			if (residue.compareTo(BigDecimal.ZERO)<=0 ){
				add = BigDecimal.ZERO;
			}
			if ( residue.compareTo(add) <0 ){
				add = residue;
				//重置每日产出
				projectAmountService.deal(u.getId(),pa.getProjectId(),12,(byte)5,pa.getActivityOutput().negate(),null);
			}
			amount = amount .add( add);
			//增加累积活动金额
			projectAmountService.deal(u.getId(),pa.getProjectId(),9,(byte)2,add,null);
		}
		userService.deal(tokenUser.getId(),9,(byte)1, amount,null);


		return ResultGenerator.genSuccessResult(amount);
	}


	/**
	 * 重置密码
	 *
	 * @return
	 */
	@PostMapping("/restTranspwd")
	@ApiOperation(value = "重置交易密码",httpMethod = "POST")
	public Result<User> restTranspwd(@RequestBody RestTranspwdParameters parameters, HttpServletRequest request) {
		// 校验权限
		if (parameters == null) {
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		if (parameters.getUserId() == null) {
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			User tokenUser =userUtil.getTokenUser(request);
			if (tokenUser == null){
				return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
			}
			User temp = userService.findById(parameters.getUserId());
			if (temp == null) {
				return ResultGenerator.genFailResult(ResultCode.RESULT_IS_NULL);
			}
			String pwd2 = SecureUtil.md5(parameters.getPwd() + temp.getSalt()).toUpperCase();
			if (StrUtil.equals(temp.getPassword().toUpperCase(), pwd2)) {
				User user = new User();
				user.setId(parameters.getUserId());
				user.setUpdateTime(new Date());
				user.setUpdateUserId(tokenUser.getName());
				user.setTranspwd(parameters.getTranspwd());
				userService.update(user);
				return ResultGenerator.genSuccessResult();
			} else {
				return ResultGenerator.genFailResult(ResultCode.UNLOGIN_PWD_ERROR);
			}
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
	}




	@PostMapping("/myUsers")
	@ApiOperation(value = "获取我的邀请的用户",httpMethod = "POST")
	public Result<List<UserBO>> myUsers(@RequestBody User user, HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		PageHelper.startPage(user.getPage(), user.getSize());
		Condition condition = new Condition(User.class);
		Criteria criteria = condition.createCriteria();
		Criteria criteria2 = condition.or();
		criteria.andEqualTo("pid", tokenUser.getId());
		criteria2.andEqualTo("pid", tokenUser.getId());
		if (StrUtil.isNotBlank(user.getName())) {
			criteria.andLike("name", "%" + user.getName() + "%");
			criteria2.andLike("loginAccount", "%" + user.getName() + "%");
		}
		if (StrUtil.isNotBlank(user.getStatus())) {
			criteria.andEqualTo("status", user.getStatus());
			criteria2.andEqualTo("status", user.getStatus());
		}
		if (StrUtil.isNotBlank(user.getStartTime())) {
			criteria.andGreaterThanOrEqualTo("createTime", user.getStartTime());
			criteria2.andGreaterThanOrEqualTo("createTime", user.getStartTime());
		}
		if (StrUtil.isNotBlank(user.getEndTime())) {
			criteria.andLessThanOrEqualTo("createTime", user.getEndTime());
			criteria2.andLessThanOrEqualTo("createTime", user.getEndTime());
		}

		PageInfo pageInfo = null;
		try {
			List<User> list = userService.findByCondition(condition);
			pageInfo = new PageInfo(list);
			pageInfo.setList(list.stream().map( user2 ->{
				UserBO bo = BeanUtil.toBean(user2,UserBO.class);
				bo.setPassword("******");
				bo.setSalt("***");
				Condition condition2 = new Condition(UserRole.class);
				Criteria criteria3 = condition2.createCriteria();
				criteria3.andEqualTo("userId", user2.getId() + "");
				List<UserRole> list2 = userRoleService.findByCondition(condition2);
				if (CollUtil.isNotEmpty(list2)){
					bo.setUserRoles(list2);
					Role role = roleService.findById(list2.get(0).getRoleId());
					if (role != null){
						bo.setRoleName(role.getName());
						bo.setType(role.getId());
					}
				}
				return bo;
			}).collect(Collectors.toList()));
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return ResultGenerator.genSuccessResult(pageInfo);
	}
}
