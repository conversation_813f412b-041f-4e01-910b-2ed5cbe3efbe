package com.project.web.parameters;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 账号登录参数
 */
@ApiModel("账号登录参数")
public class LoginAccountParameters {

    @ApiModelProperty(name="account",value = "登录账号",required=true)
    private String account;

    @ApiModelProperty(name="pwd",value = "登录密码",required=true)
    private String pwd;

    @ApiModelProperty(name="type",value = "登录类型 1业主 2施工方 3项目方",required=true)
    private Integer type;

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public String getAccount() {
        return account;
    }

    public void setAccount(String account) {
        this.account = account;
    }

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
