package com.project.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
//import com.project.model.Examine;
//import com.project.model.ExaminePlan;
//import com.project.model.Pachong;
//import com.project.service.ExaminePlanService;
//import com.project.service.ExamineService;
//import com.project.service.PachongService;
import com.project.model.Order;
import com.project.model.WithdrawOrder;
import com.project.service.OrderService;
import com.project.service.WithdrawOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@EnableScheduling
public class MyScheduledTasks {

    @Resource
    private OrderService orderService;

    @Resource
    private WithdrawOrderService withdrawOrderService;
//
//
//
//    @Resource
//    private ExamineService examineService;
//
//    @Resource
//    private PachongService pachongService;


    // 每隔5秒执行一次
//    @Scheduled(fixedRate = 5000)
//    public void task1() {
//        System.out.println("定时任务1执行，当前时间：" + System.currentTimeMillis());
//    }



    @Scheduled(cron = "0 0/5 * * * ?")
    public void task() {
        Condition c = new Condition(Order.class);
        c.createCriteria().andEqualTo("status",1)
                .andLessThanOrEqualTo("createTime",DateUtil.yesterday());
        List<Order> orders = orderService.findByCondition(c);
        if (CollUtil.isNotEmpty(orders)){
            for (Order o:orders){
                orderService.update(Order.builder().id(o.getId()).status(4).build());
            }
        }
    }

//    @Scheduled(cron = "0 0/5 * * * ?")
//    public void task2() {
//        Condition c = new Condition(WithdrawOrder.class);
//        c.createCriteria().andEqualTo("status",1)
//                .andLessThanOrEqualTo("createTime",DateUtil.yesterday());
//        List<WithdrawOrder> orders = withdrawOrderService.findByCondition(c);
//        if (CollUtil.isNotEmpty(orders)){
//            for (WithdrawOrder o:orders){
//                withdrawOrderService.update(WithdrawOrder.builder().id(o.getId()).status(4).build());
//            }
//        }
//    }

    public static void main(String[] args) {
        if (StrUtil.containsAny("1,2,3","1")){
            System.out.println(111);
        } else {
            System.out.println(222);
        }
        System.out.println(String.valueOf(DateUtil.dayOfWeek(DateUtil.date())));
    }


}