package com.project.web.parameters;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;

/**
 * <pre>
 * 微信扫码支付统一下单后发起支付拼接所需参数实现类
 * Created by <PERSON><PERSON> on 2017-9-1.
 * </pre>
 *
 * <AUTHOR> href="https://github.com/binarywang">Bin<PERSON></a>
 */
@Data
@ToString
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WxPayNativeOrderResultBo implements Serializable {
  private static final long serialVersionUID = 887792717425241444L;

  @ApiModelProperty(value="订单id")
  private Integer orderId;
  @ApiModelProperty(value="订单号")
  private String orderSn;

  private String codeUrl;
}
