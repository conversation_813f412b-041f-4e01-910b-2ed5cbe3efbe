package com.project.web.parameters;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Data
@ApiModel(value="提现入参")
public class WithdrawOrderParam implements Serializable {

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;


    /**
     * 支付金额
     */
    @ApiModelProperty(value="amount支付金额")
    private BigDecimal amount;

    /**
     * 类型 1提现
     */
    @ApiModelProperty(value="type类型 1提现")
    private Integer type;




    private static final long serialVersionUID = 1L;

}