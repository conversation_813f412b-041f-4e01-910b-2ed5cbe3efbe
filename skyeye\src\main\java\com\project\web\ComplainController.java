package com.project.web;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.model.Complain;
import com.project.model.User;
import com.project.model.UserRole;
import com.project.service.ComplainService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.project.service.UserRoleService;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
* Created by CodeGenerator on 2024/04/08.
*/
@Api(tags = "投诉建议管理")
@RestController
@RequestMapping("/complain")
public class ComplainController {

	private static Logger log = LoggerFactory.getLogger(ComplainController.class);

    @Resource
    private ComplainService complainService;

//	@Resource
//	private WorkService workService;

	@Resource
	private UserUtil userUtil;

	@Resource
	private UserRoleService userRoleService;


	@PostMapping("/add")
	@ApiOperation(value = "居民投诉建议",httpMethod = "POST")
    public Result add(@RequestBody Complain complain, HttpServletRequest request) {
    	if(complain == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	try {
			if (StrUtil.isBlank(complain.getContent())){
				return ResultGenerator.genFailResult(ResultCode.CONTENT_IS_NULL);
			}
			if (StrUtil.isBlank(complain.getAddress())){
				return ResultGenerator.genFailResult(ResultCode.ADDRESS_IS_NULL);
			}
			if (complain.getType() == null){
				return ResultGenerator.genFailResult(ResultCode.TYPE_IS_NULL);
			}
			User tokenUser = userUtil.getTokenUser(request);
			if (tokenUser == null){
				return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
			}
			complain.setId(null);
			complain.setUserId(tokenUser.getId());
    		complain.setCreateTime(new Date());
			complain.setStatus(1);
    		complainService.save(complain);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success();
    }

	@RequestMapping("/delete")
	@ApiOperation(value = "删除投诉",httpMethod = "GET")
	public Result delete(@RequestParam Integer id) {
		if(id == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
//			Complain complain = complainService.findById(id);
//			if (complain == null){
//				return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
//			}
//			if (complain.getStatus() != 1){
//				return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
//			}
			complainService.deleteById(id);
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success();
	}

    @RequestMapping("/close")
	@ApiOperation(value = "取消投诉",httpMethod = "GET")
    public Result close(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	try {
			Complain complain = complainService.findById(id);
			if (complain == null){
				return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
			}
			if (complain.getStatus() == 4){
				return Result.success();
			}
			complainService.update(Complain.builder().id(id).status(4).build());
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @GetMapping("/dispose")
	@ApiOperation(value = "确认",httpMethod = "GET")
    public Result dispose(@RequestParam Integer id, HttpServletRequest request) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
    	try {
			Complain c = complainService.findById(id);
			if (c == null){
				 return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
            }
            if (c.getStatus() == 3){
                return Result.success();
			}
			if (c.getStatus() != 1){
				return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
			}
			complainService.update(Complain.builder().id(id).updateTime(DateUtil.date())
					.status(3).notarizeUserId(tokenUser.getId()).build());
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

	@GetMapping("/toWork")
	@ApiOperation(value = "转卫生工作",httpMethod = "GET")
	public Result toWork(@RequestParam Integer id, HttpServletRequest request) {
		if(id == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		try {
			Complain c = complainService.findById(id);
			if (c == null){
				return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
			}
			if (c.getStatus() == 2){
				return Result.success();
			}
			if (c.getStatus() != 1){
				return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
			}
			complainService.update(Complain.builder().id(id).updateTime(DateUtil.date())
					.status(2).notarizeUserId(tokenUser.getId()).build());
//			workService.save(Work.builder().status(1).workPics(c.getPics())
//					.address(c.getAddress()).content(c.getContent())
//					.createTime(DateUtil.date()).updateTime(DateUtil.date())
//					.userId(c.getUserId()).build());
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success();
	}

    @RequestMapping("/detail")
	@ApiOperation(value = "complain获取详情",httpMethod = "GET")
    public Result<Complain> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	Complain complain = null;
    	try {
    		complain = complainService.findById(id);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success(complain);
    }

    @RequestMapping("/list")
	@ApiOperation(value = "complain获取列表",httpMethod = "POST")
    public Result<List<Complain>> list(@RequestBody Complain complain, HttpServletRequest request) {


		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		Condition condition2 = new Condition(UserRole.class);
		Criteria criteria2 = condition2.createCriteria();
		criteria2.andEqualTo("userId", tokenUser.getId() + "");
		List<UserRole> list2 = userRoleService.findByCondition(condition2);
		Integer roleId = 0;
		if (CollUtil.isNotEmpty(list2)){
			roleId = list2.get(0).getRoleId();
		}

        PageHelper.startPage(complain.getPage(), complain.getSize());
        
        Condition condition = new Condition(complain.getClass());
        Criteria criteria = condition.createCriteria();
		if (roleId == 4){
			criteria.andEqualTo("userId",  tokenUser.getId());
		}

		if (complain.getStatus() != null){
			criteria.andEqualTo("status", complain.getStatus());
		}
		if(StrUtil.isNotBlank(complain.getContent())){
			criteria.andLike("content", "%"+complain.getContent()+"%");
		}
		PageInfo pageInfo = null;
		try {
			condition.setOrderByClause("create_time desc");
    		 List<Complain> list = complainService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
