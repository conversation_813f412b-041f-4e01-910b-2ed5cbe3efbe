<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

   <groupId>com.linke</groupId>
    <artifactId>distribution</artifactId>
    <version>1.0</version>
    <packaging>jar</packaging>

    <properties>
        <java.version>1.8</java.version>
    </properties>

    <!-- Inherit defaults from Spring Boot -->
	  <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
		<version>2.3.2.RELEASE</version>
    </parent>

    <dependencies>
        <!--Spring Boot依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--常用库依赖-->
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
        </dependency>
        <dependency>
            <groupId>org.jsoup</groupId>
            <artifactId>jsoup</artifactId>
            <version>1.14.3</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.6</version>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>23.0</version>
        </dependency>
        <!--MySQL JDBC驱动-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <!--MyBatis 及 插件依赖-->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>1.3.1</version>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>3.4.5</version>
        </dependency>
        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper</artifactId>
            <version>3.4.2</version>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>4.2.1</version>
        </dependency>
        <!--阿里 FastJson依赖-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.67</version>
        </dependency>
        <!--阿里 Druid Spring Boot Starter依赖-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.10</version>
        </dependency>
        <!--代码生成器依赖-->
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.23</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mybatis.generator</groupId>
            <artifactId>mybatis-generator-core</artifactId>
            <version>1.3.5</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.github.misterchangray.mybatis.generator.plugins</groupId>
            <artifactId>myBatisGeneratorPlugins</artifactId>
            <version>1.2</version>
            <scope>test</scope>
        </dependency>
        <!-- swagger2 -->
<!--        <dependency>-->
<!--            <groupId>io.springfox</groupId>-->
<!--            <artifactId>springfox-swagger2</artifactId>-->
<!--            <version>2.8.0</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>io.springfox</groupId>-->
<!--            <artifactId>springfox-swagger-ui</artifactId>-->
<!--            <version>2.8.0</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--&lt;!&ndash;            <groupId>us.codecraft</groupId>&ndash;&gt;-->
<!--&lt;!&ndash;            <artifactId>webmagic-core</artifactId>&ndash;&gt;-->
<!--&lt;!&ndash;            <version>0.7.3</version>&ndash;&gt;-->
<!--            <groupId>us.codecraft</groupId>-->
<!--            <artifactId>webmagic-core</artifactId>-->
<!--            <version>1.0.2-SNAPSHOT</version>-->
<!--&lt;!&ndash;            <exclusions>&ndash;&gt;-->
<!--&lt;!&ndash;                <exclusion>&ndash;&gt;-->
<!--&lt;!&ndash;                    <groupId>org.slf4j</groupId>&ndash;&gt;-->
<!--&lt;!&ndash;                    <artifactId>slf4j-log4j12</artifactId>&ndash;&gt;-->
<!--&lt;!&ndash;                </exclusion>&ndash;&gt;-->
<!--&lt;!&ndash;            </exclusions>&ndash;&gt;-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>us.codecraft</groupId>-->
<!--            <artifactId>webmagic-extension</artifactId>-->
<!--            <version>0.7.3</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.4.4</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.30</version> <!-- 使用最新版本 -->
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.twitter</groupId>
            <artifactId>twitter-api-java-sdk</artifactId>
            <version>1.1.4</version>
        </dependency>
        <dependency>
            <groupId>oauth.signpost</groupId>
            <artifactId>signpost-core</artifactId>
            <version>1.2.1.2</version>
        </dependency>

        <dependency>
            <groupId>oauth.signpost</groupId>
            <artifactId>signpost-commonshttp4</artifactId>
            <version>1.2.1.2</version>
        </dependency>


        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger2</artifactId>
            <version>2.9.2</version>
        </dependency>
        <dependency>
            <groupId>io.springfox</groupId>
            <artifactId>springfox-swagger-ui</artifactId>
            <version>2.9.2</version>
        </dependency>

        <dependency>
            <groupId>com.corundumstudio.socketio</groupId>
            <artifactId>netty-socketio</artifactId>
            <version>2.0.2</version>
        </dependency>

        <dependency>
            <groupId>com.github.houbb</groupId>
            <artifactId>opencc4j</artifactId>
            <version>1.1.0</version>
        </dependency>
        <dependency>
            <groupId>com.google.collections</groupId>
            <artifactId>google-collections</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.github.xiaoymin</groupId>
            <artifactId>swagger-bootstrap-ui</artifactId>
            <version>1.8.5</version>
        </dependency>
        <!-- swagger2 皮肤 -->
        <dependency>
            <groupId>com.github.caspar-chen</groupId>
            <artifactId>swagger-ui-layer</artifactId>
            <version>1.1.2</version>
        </dependency>

        <dependency>
            <groupId>com.spring4all</groupId>
            <artifactId>swagger-spring-boot-starter</artifactId>
            <version>1.8.0.RELEASE</version>
        </dependency>

        <!--redis依赖-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-redis</artifactId>
            <version>1.3.8.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.0.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.17</version>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>org.java-websocket</groupId>-->
<!--            <artifactId>Java-WebSocket</artifactId>-->
<!--            <version>1.3.0</version>-->
<!--        </dependency>-->


        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-context</artifactId>
            <version>2.0.1.RELEASE</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.github.binarywang</groupId>-->
<!--            <artifactId>weixin-java-pay</artifactId>-->
<!--            <version>4.4.0</version>-->
<!--        </dependency>-->

        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt</artifactId>
            <version>0.9.1</version>
        </dependency>

<!--        <dependency>-->
<!--            <groupId>com.alipay.sdk</groupId>-->
<!--            <artifactId>alipay-sdk-java</artifactId>-->
<!--            <version>4.10.182.ALL</version>-->
<!--            <exclusions>-->
<!--                <exclusion>-->
<!--                    <artifactId>fastjson</artifactId>-->
<!--                    <groupId>com.alibaba</groupId>-->
<!--                </exclusion>-->
<!--            </exclusions>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.github.javen205</groupId>-->
<!--            <artifactId>IJPay-AliPay</artifactId>-->
<!--            <version>2.7.1</version>-->
<!--        </dependency>-->

<!--        <dependency>-->
<!--            <groupId>com.microsoft.playwright</groupId>-->
<!--            <artifactId>playwright</artifactId>-->
<!--            <version>1.17.1</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.google.protobuf</groupId>-->
<!--            <artifactId>protobuf-java</artifactId>-->
<!--            <version>3.5.1</version>-->
<!--        </dependency>-->
        <!--    <dependency>
                <groupId>com.google.protobuf</groupId>
                <artifactId>protobuf-java</artifactId>
                <version>3.5.1</version>
            </dependency>-->
        <!-- https://mvnrepository.com/artifact/com.google.protobuf/protobuf-java-util -->
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java-util</artifactId>
            <version>3.5.1</version>
        </dependency>

        <dependency>
            <groupId>com.github.binarywang</groupId>
            <artifactId>weixin-java-pay</artifactId>
            <version>4.4.0</version>
        </dependency>

        <dependency>
            <groupId>com.alipay.sdk</groupId>
            <artifactId>alipay-sdk-java</artifactId>
            <version>4.10.182.ALL</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.github.javen205</groupId>
            <artifactId>IJPay-AliPay</artifactId>
            <version>2.7.1</version>
        </dependency>

    </dependencies>

    <build>
        <finalName>distribution</finalName>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>aliyun-repos</id>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>aliyun-plugin</id>
            <url>http://maven.aliyun.com/nexus/content/groups/public/</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </pluginRepository>
    </pluginRepositories>

</project>
