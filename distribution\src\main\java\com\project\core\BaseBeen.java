package com.project.core;

import javax.persistence.Transient;

import io.swagger.annotations.ApiModelProperty;
import lombok.ToString;

@ToString
public class BaseBeen {

    @ApiModelProperty(name="startTime",value = "列表查询时开始时间")
    @Transient 
    private String startTime;

    @ApiModelProperty(name="endTime",value = "列表查询时结束时间")
    @Transient
    private String endTime;


    @ApiModelProperty(name="page",value = "列表查询时当前页")
    @Transient
    private Integer page;

    @ApiModelProperty(name="size",value = "列表查询时分页大小")
    @Transient
    private Integer size;

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }

    public String getStartTime() {
        return startTime;
    }

    public void setStartTime(String startTime) {
        this.startTime = startTime;
    }

    public String getEndTime() {
        return endTime;
    }

    public void setEndTime(String endTime) {
        this.endTime = endTime;
    }

}
