package com.project.web.parameters;

import io.swagger.annotations.ApiModelProperty;

import java.util.Date;
import java.util.List;

public class PermissionBo {

    private Integer id;

    /**
     * 名称
     */
    private String name;

    /**
     * 名称
     */
    private String label;


    /**
     * logo
     */
    private String logo;

    /**
     * 类型 0 一级菜单 1 二级菜单  2 三级菜单 3 页面 4动作
     */
    private String type;

    /**
     * 前端排版
     */
    private String composingKey;

    /**
     * 跳转地址
     */
    private String url;

    /**
     * 排序
     */
    private Integer seq;

    /**
     * 上级菜单
     */
    private String parentId;

    private String createUserId;

    private String updateUserId;

    private Date createTime;

    private Date updateTime;

    //有关联 0无 1有
    private String hasRelevance;

    /**
     * 是否允许点击
     */
    @ApiModelProperty(name="isAction",value = "是否允许点击")
    private String isAction;

    public String getIsAction() {
        return isAction;
    }

    public void setIsAction(String isAction) {
        this.isAction = isAction;
    }

    public String getLabel() {
        return name;
    }

    //孩子节点
    private List<PermissionBo> children;

    public String getComposingKey() {
        return composingKey;
    }

    public void setComposingKey(String composingKey) {
        this.composingKey = composingKey;
    }

    public String getLogo() {
		return logo;
	}

	public void setLogo(String logo) {
		this.logo = logo;
	}

	public String getHasRelevance() {
		return hasRelevance;
	}

	public void setHasRelevance(String hasRelevance) {
		this.hasRelevance = hasRelevance;
	}

	public List<PermissionBo> getChildren() {
		return children;
	}

	public void setChildren(List<PermissionBo> children) {
		this.children = children;
	}

	/**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取名称
     *
     * @return name - 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取类型 0 一级菜单 1 二级菜单  2 三级菜单 3 页面 4动作
     *
     * @return type - 类型 0 一级菜单 1 二级菜单  2 三级菜单 3 页面 4动作
     */
    public String getType() {
        return type;
    }

    /**
     * 设置类型 0 一级菜单 1 二级菜单  2 三级菜单 3 页面 4动作
     *
     * @param type 类型 0 一级菜单 1 二级菜单  2 三级菜单 3 页面 4动作
     */
    public void setType(String type) {
        this.type = type;
    }

    /**
     * 获取跳转地址
     *
     * @return url - 跳转地址
     */
    public String getUrl() {
        return url;
    }

    /**
     * 设置跳转地址
     *
     * @param url 跳转地址
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * 获取排序
     *
     * @return seq - 排序
     */
    public Integer getSeq() {
        return seq;
    }

    /**
     * 设置排序
     *
     * @param seq 排序
     */
    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    /**
     * 获取上级菜单
     *
     * @return parent_id - 上级菜单
     */
    public String getParentId() {
        return parentId;
    }

    /**
     * 设置上级菜单
     *
     * @param parentId 上级菜单
     */
    public void setParentId(String parentId) {
        this.parentId = parentId;
    }

    /**
     * @return create_user_id
     */
    public String getCreateUserId() {
        return createUserId;
    }

    /**
     * @param createUserId
     */
    public void setCreateUserId(String createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * @return update_user_id
     */
    public String getUpdateUserId() {
        return updateUserId;
    }

    /**
     * @param updateUserId
     */
    public void setUpdateUserId(String updateUserId) {
        this.updateUserId = updateUserId;
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * @return update_time
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * @param updateTime
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}
