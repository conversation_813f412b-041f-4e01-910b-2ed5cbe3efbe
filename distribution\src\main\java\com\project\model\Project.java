package com.project.model;

import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Data
@ApiModel(value="com.project.model.Project")
@Table(name = "c_project")
public class Project extends BaseBeen implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 公司id
     */
    @Column(name = "company_id")
    @ApiModelProperty(value="companyId公司id")
    private Integer companyId;

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    @ApiModelProperty(value="图片")
    private String pic;

    /**
     * 类型 code_library : project_type
     */
    @ApiModelProperty(value="type类型 code_library : project_type")
    private Integer type;

    /**
     * 状态 0未启用  1进行中 
     */
    @ApiModelProperty(value="status状态 0未启用  1进行中 ")
    private Integer status;

    /**
     * 创建人
     */
    @Column(name = "create_user_id")
    @ApiModelProperty(value="createUserId创建人")
    private Integer createUserId;

    /**
     * 更新人
     */
    @Column(name = "update_user_id")
    @ApiModelProperty(value="updateUserId更新人")
    private Integer updateUserId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    /**
     * 开始时间
     */
    @Column(name = "begin_time")
    @ApiModelProperty(value="beginTime开始时间")
    private Date beginTime;

    /**
     * 完成时间
     */
    @Column(name = "finish_time")
    @ApiModelProperty(value="finishTime完成时间")
    private Date finishTime;

    /**
     * 返佣
     */
    @ApiModelProperty(value="amount返佣")
    private BigDecimal amount;

    @ApiModelProperty(value="amount返佣2")
    private BigDecimal amount2;

    @ApiModelProperty(value="amount返佣3")
    private BigDecimal amount3;

    /**
     * 描述
     */
    @ApiModelProperty(value="description描述")
    private String description;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取公司id
     *
     * @return company_id - 公司id
     */
    public Integer getCompanyId() {
        return companyId;
    }

    /**
     * 设置公司id
     *
     * @param companyId 公司id
     */
    public void setCompanyId(Integer companyId) {
        this.companyId = companyId;
    }

    /**
     * 获取名称
     *
     * @return name - 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取类型 code_library : project_type
     *
     * @return type - 类型 code_library : project_type
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置类型 code_library : project_type
     *
     * @param type 类型 code_library : project_type
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取状态 0未启用  1进行中 
     *
     * @return status - 状态 0未启用  1进行中 
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 0未启用  1进行中 
     *
     * @param status 状态 0未启用  1进行中 
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取创建人
     *
     * @return create_user_id - 创建人
     */
    public Integer getCreateUserId() {
        return createUserId;
    }

    /**
     * 设置创建人
     *
     * @param createUserId 创建人
     */
    public void setCreateUserId(Integer createUserId) {
        this.createUserId = createUserId;
    }

    /**
     * 获取更新人
     *
     * @return update_user_id - 更新人
     */
    public Integer getUpdateUserId() {
        return updateUserId;
    }

    /**
     * 设置更新人
     *
     * @param updateUserId 更新人
     */
    public void setUpdateUserId(Integer updateUserId) {
        this.updateUserId = updateUserId;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取开始时间
     *
     * @return begin_time - 开始时间
     */
    public Date getBeginTime() {
        return beginTime;
    }

    /**
     * 设置开始时间
     *
     * @param beginTime 开始时间
     */
    public void setBeginTime(Date beginTime) {
        this.beginTime = beginTime;
    }

    /**
     * 获取完成时间
     *
     * @return finish_time - 完成时间
     */
    public Date getFinishTime() {
        return finishTime;
    }

    /**
     * 设置完成时间
     *
     * @param finishTime 完成时间
     */
    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }

    /**
     * 获取返佣
     *
     * @return amount - 返佣
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 设置返佣
     *
     * @param amount 返佣
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 获取描述
     *
     * @return description - 描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 设置描述
     *
     * @param description 描述
     */
    public void setDescription(String description) {
        this.description = description;
    }
}