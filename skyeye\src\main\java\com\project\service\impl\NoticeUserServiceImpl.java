package com.project.service.impl;

import com.project.dao.NoticeUserMapper;
import com.project.model.NoticeUser;
import com.project.service.NoticeUserService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/07/14.
 */
@Service
@Transactional
public class NoticeUserServiceImpl extends AbstractService<NoticeUser> implements NoticeUserService {
    @Resource
    private NoticeUserMapper bNoticeUserMapper;

}
