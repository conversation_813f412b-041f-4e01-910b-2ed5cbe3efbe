package com.project.model;

import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@Data
@ApiModel(value="com.project.model.Goods")
@Table(name = "c_goods")
public class Goods extends BaseBeen implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 公司id
     */
    @Column(name = "company_id")
    @ApiModelProperty(value="companyId公司id")
    private Integer companyId;

    /**
     * 商品名称
     */
    @ApiModelProperty(value="name商品名称")
    private String name;

    /**
     * 项目ids
     */
    @Column(name = "project_ids")
    @ApiModelProperty(value="projectIds项目ids")
    private String projectIds;

    /**
     * 大图
     */
    @Column(name = "big_pic")
    @ApiModelProperty(value="bigPic大图")
    private String bigPic;

    /**
     * 跳转 链接
     */
    @ApiModelProperty(value="link跳转 链接")
    private String link;

    /**
     * 会员有效时间
     */
    @Column(name = "effective_value")
    @ApiModelProperty(value="effectiveValue会员有效时间")
    private Integer effectiveValue;

    @ApiModelProperty(value="类型 1企业商品 2积分商品")
    private Integer type;

    @ApiModelProperty(value="积分兑换所需积分")
    private BigDecimal score;

    @Column(name = "score_amount")
    @ApiModelProperty(value="积分兑换金额")
    private BigDecimal scoreAmount;

    /**
     * 备注
     */
    @ApiModelProperty(value="memo备注")
    private String memo;

    /**
     * 状态 0未启用 1正常 2停用
     */
    @ApiModelProperty(value="status状态 0未启用 1正常 2停用")
    private Byte status;

    /**
     * 定价
     */
    @ApiModelProperty(value="amount定价")
    private BigDecimal amount;

    /**
     * 折扣价
     */
    @Column(name = "discount_amount")
    @ApiModelProperty(value="discountAmount折扣价")
    private BigDecimal discountAmount;

    @Column(name = "create_time")
    @ApiModelProperty(value="createTime")
    private Date createTime;

    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime")
    private Date updateTime;

    /**
     * 图片
     */
    @ApiModelProperty(value="pic图片")
    private String pic;

    /**
     * 类型 1月2年 3永久
     */
    @Column(name = "member_type")
    @ApiModelProperty(value="memberType类型 1月2年 3永久")
    private String memberType;

    /**
     * 排序
     */
    @Column(name = "sort_num")
    @ApiModelProperty(value="sortNum排序")
    private Integer sortNum;

    private static final long serialVersionUID = 1L;


}