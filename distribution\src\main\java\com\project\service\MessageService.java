package com.project.service;
import com.project.model.Message;
import com.project.core.Service;
import org.springframework.http.MediaType;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;
import tk.mybatis.mapper.entity.Condition;

import java.io.IOException;
import java.util.concurrent.ConcurrentHashMap;


/**
 * Created by CodeGenerator on 2024/04/08.
 */
public interface MessageService extends Service<Message> {

    public static ConcurrentHashMap<Integer, SseEmitter> sseEmitterMap = new ConcurrentHashMap<>();

    void updateByCondition(Message message,Condition c);

    public void sendMessage(int userId, String message) throws IOException ;

    public void sendMessageToGM( String message) throws IOException ;
}
