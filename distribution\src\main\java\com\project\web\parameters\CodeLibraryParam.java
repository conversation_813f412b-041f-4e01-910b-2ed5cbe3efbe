package com.project.web.parameters;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @Author: lxk
 * @Date: 2019/12/14 13:56
 * @Version 1.0
 */
@Data
@ApiModel("类型码参数")
public class CodeLibraryParam implements Serializable {

    private static final long serialVersionUID = -5318595472853014732L;

    /**
     * 名字
     */
    @ApiModelProperty("名字")
    private String name;

    @ApiModelProperty("英文名字")
    private String code;

    @ApiModelProperty("类型 1项目类型 2明细类型")
    private Integer type;

    @ApiModelProperty(value="用户id")
    private Integer userId;
}
