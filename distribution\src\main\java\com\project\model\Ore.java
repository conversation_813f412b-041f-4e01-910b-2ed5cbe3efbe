package com.project.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@ApiModel(value="com.project.model.Ore")
@Table(name = "c_ore")
public class Ore implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty(value="userId用户id")
    private Integer userId;

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    /**
     * 状态 1vip矿机 2svip矿机
     */
    @ApiModelProperty(value="type状态 1vip矿机 2svip矿机")
    private Integer type;

    /**
     * 状态 1正常 2禁用
     */
    @ApiModelProperty(value="status状态 1正常 2禁用")
    private Integer status;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value="createUser创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    /**
     * 日产出
     */
    @ApiModelProperty(value="output日产出")
    private BigDecimal output;

    /**
     * 累积产出
     */
    @Column(name = "accumulate_output")
    @ApiModelProperty(value="accumulateOutput累积产出")
    private BigDecimal accumulateOutput;

    /**
     * 预计总产出
     */
    @ApiModelProperty(value="total预计总产出")
    private BigDecimal total;

    /**
     * 完成时间
     */
    @Column(name = "finish_time")
    @ApiModelProperty(value="finishTime完成时间")
    private Date finishTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取用户id
     *
     * @return user_id - 用户id
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * 设置用户id
     *
     * @param userId 用户id
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取名称
     *
     * @return name - 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取状态 1vip矿机 2svip矿机
     *
     * @return type - 状态 1vip矿机 2svip矿机
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置状态 1vip矿机 2svip矿机
     *
     * @param type 状态 1vip矿机 2svip矿机
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取状态 1正常 2禁用
     *
     * @return status - 状态 1正常 2禁用
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 1正常 2禁用
     *
     * @param status 状态 1正常 2禁用
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取日产出
     *
     * @return output - 日产出
     */
    public BigDecimal getOutput() {
        return output;
    }

    /**
     * 设置日产出
     *
     * @param output 日产出
     */
    public void setOutput(BigDecimal output) {
        this.output = output;
    }

    /**
     * 获取累积产出
     *
     * @return accumulate_output - 累积产出
     */
    public BigDecimal getAccumulateOutput() {
        return accumulateOutput;
    }

    /**
     * 设置累积产出
     *
     * @param accumulateOutput 累积产出
     */
    public void setAccumulateOutput(BigDecimal accumulateOutput) {
        this.accumulateOutput = accumulateOutput;
    }

    /**
     * 获取预计总产出
     *
     * @return total - 预计总产出
     */
    public BigDecimal getTotal() {
        return total;
    }

    /**
     * 设置预计总产出
     *
     * @param total 预计总产出
     */
    public void setTotal(BigDecimal total) {
        this.total = total;
    }

    /**
     * 获取完成时间
     *
     * @return finish_time - 完成时间
     */
    public Date getFinishTime() {
        return finishTime;
    }

    /**
     * 设置完成时间
     *
     * @param finishTime 完成时间
     */
    public void setFinishTime(Date finishTime) {
        this.finishTime = finishTime;
    }
}