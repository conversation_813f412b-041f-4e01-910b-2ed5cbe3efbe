spring.datasource.url=***********************************************************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=Lin123456

#spring.redis.database=0
#spring.redis.host=127.0.0.1
#spring.redis.port=6379
#spring.redis.password=


spring.redis.database=0
spring.redis.host=**********
spring.redis.port=6379
spring.redis.password=


wx.pay.mchId=1718871521
wx.pay.mchKey=195d29d91e0d197b0fc921297111df98
#wx.pay.keyPath=classpath:/cert/1718871521.p12
wx.pay.keyPath=/home/<USER>/distribution/1718871521.p12
wxpay.api-v3-key=UDuLFDcmy5Eb6o0nTNZdu6ek4DDh4K8C
wx.pay.tradeType=NATIVE

wx.pay.appId=wx231fe8abc058aa97
wxpay.domain=https://api.mch.weixin.qq.com
wx.pay.notifyUrl=https://api2.youchaokj.com/distribution

