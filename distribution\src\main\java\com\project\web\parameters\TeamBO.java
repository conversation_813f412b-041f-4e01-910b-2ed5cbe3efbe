package com.project.web.parameters;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
@ApiModel(value="团队工时")
public class TeamBO implements Serializable {
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 用户id
     */
    @ApiModelProperty(value="userId用户id")
    private Integer userId;

    @ApiModelProperty(value="头像")
    private String icon;

    /**
     * 公司id
     */
    @ApiModelProperty(value="companyId公司id")
    private String companyId;

    /**
     * 姓名
     */
    @ApiModelProperty(value="memberName姓名")
    private String memberName;

    /**
     * 成员用户id
     */
    @ApiModelProperty(value="memberId成员用户id")
    private Integer memberId;

    /**
     * 状态 0 正常 1注销 
     */
    @ApiModelProperty(value="status状态 0 正常 1注销 ")
    private String status;

    @ApiModelProperty(value="工时")
    private Integer workDays;


    private static final long serialVersionUID = 1L;


}