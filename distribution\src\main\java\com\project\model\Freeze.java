package com.project.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@ApiModel(value="com.project.model.Freeze")
@Table(name = "c_freeze")
public class Freeze implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 订单号
     */
    @ApiModelProperty(value="no订单号")
    private Integer no;

    @Column(name = "user_id")
    @ApiModelProperty(value="userId")
    private Integer userId;

    /**
     * 代币类型 1-积分
     */
    @ApiModelProperty(value="type代币类型 1-积分")
    private Integer type;

    /**
     * 余额
     */
    @ApiModelProperty(value="amount余额")
    private BigDecimal amount;

    /**
     * 状态 1冻结中 已解冻
     */
    @ApiModelProperty(value="status状态 1冻结中 已解冻")
    private Integer status;

    /**
     * 业务类型 1、提现
     */
    @Column(name = "biz_type")
    @ApiModelProperty(value="bizType业务类型 1、提现")
    private Integer bizType;

    /**
     * 掉落业务id
     */
    @Column(name = "biz_id")
    @ApiModelProperty(value="bizId掉落业务id")
    private Long bizId;

    @Column(name = "create_time")
    @ApiModelProperty(value="createTime")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取订单号
     *
     * @return no - 订单号
     */
    public Integer getNo() {
        return no;
    }

    /**
     * 设置订单号
     *
     * @param no 订单号
     */
    public void setNo(Integer no) {
        this.no = no;
    }

    /**
     * @return user_id
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * @param userId
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取代币类型 1-积分
     *
     * @return type - 代币类型 1-积分
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置代币类型 1-积分
     *
     * @param type 代币类型 1-积分
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取余额
     *
     * @return amount - 余额
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 设置余额
     *
     * @param amount 余额
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 获取状态 1冻结中 已解冻
     *
     * @return status - 状态 1冻结中 已解冻
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 1冻结中 已解冻
     *
     * @param status 状态 1冻结中 已解冻
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取业务类型 1、提现
     *
     * @return biz_type - 业务类型 1、提现
     */
    public Integer getBizType() {
        return bizType;
    }

    /**
     * 设置业务类型 1、提现
     *
     * @param bizType 业务类型 1、提现
     */
    public void setBizType(Integer bizType) {
        this.bizType = bizType;
    }

    /**
     * 获取掉落业务id
     *
     * @return biz_id - 掉落业务id
     */
    public Long getBizId() {
        return bizId;
    }

    /**
     * 设置掉落业务id
     *
     * @param bizId 掉落业务id
     */
    public void setBizId(Long bizId) {
        this.bizId = bizId;
    }

    /**
     * @return create_time
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * @param createTime
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}