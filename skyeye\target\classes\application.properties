spring.profiles.active=dev
# \u6240\u6709\u73AF\u5883\u901A\u7528\u7684\u914D\u7F6E\uFF0C\u653E\u5728\u8FD9\u91CC
spring.application.name=skyeye
server.servlet.context-path=/skyeye
server.port=8101
# 404 \u4EA4\u7ED9\u5F02\u5E38\u5904\u7406\u5668\u5904\u7406
spring.mvc.throw-exception-if-no-handler-found=true
spring.resources.add-mappings=false

spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8

spring.servlet.multipart.max-file-size=200MB
spring.servlet.multipart.max-request-size=200MB

spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
signature=false
login.check=false
logging.config=classpath:logback-spring.xml
logging.level.com.qimingxing.journey=info
logging.level.com.qimingxing.journey.dao=debug

wx.pay.notifyUrl=
upload.dir=/home/<USER>/skyeye/upload/
skyeye.server.url=http://***********:8017/