package com.project.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.project.dao.ProjectAmountMapper;
import com.project.model.Flow;
import com.project.model.Project;
import com.project.model.ProjectAmount;
import com.project.model.User;
import com.project.service.FlowService;
import com.project.service.ProjectAmountService;
import com.project.core.AbstractService;
import com.project.service.ProjectService;
import com.project.service.UserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * Created by CodeGenerator on 2025/05/17.
 */
@Service
@Transactional
public class ProjectAmountServiceImpl extends AbstractService<ProjectAmount> implements ProjectAmountService {
    @Resource
    private ProjectAmountMapper cProjectAmountMapper;

    @Resource
    private FlowService flowService;

    @Resource
    private UserService userService;

    @Override
    public Boolean deal(Integer userId, int projectId, int bizType, Byte type, BigDecimal num, Long bizId) {
        User u = userService.findById(userId);
        ProjectAmount pa = findPa(userId,projectId);
        BigDecimal a = null;
        if (type.compareTo((byte)2) == 0){
            a = pa.getActivityAmount();
        } else  if (type.compareTo((byte)3) == 0){
            a = pa.getActivityLimit();
        }else  if (type.compareTo((byte)5) == 0){
            a = pa.getActivityOutput();
        }
        flowService.save(Flow.builder().bizType(bizType).bizId(bizId).amount(a.add(num))
                        .pid(u.getPid())
                .createTime(DateUtil.date()).num(num).userId(userId).type(type).build());
        Map<String,Object> map = new HashMap<>();
        map.put("userId",userId);
        map.put("projectId",projectId);
        map.put("bizType",bizType);
        map.put("type",type);
        map.put("num",num);
        int i = cProjectAmountMapper.deal(map);
        if (i > 0 ){
            return true;
        } else {
            return false;
        }
    }

    @Override
    public void addActivityNum(Integer userId, Integer projectId) {
        Condition c = new Condition(ProjectAmount.class);
        c.createCriteria().andEqualTo("userId",userId)
                .andEqualTo("projectId",projectId);
        List<ProjectAmount> pas = findByCondition(c);
        ProjectAmount pa = pas.get(0);
        update(ProjectAmount.builder().id(pa.getId()).activityNum(pa.getActivityNum()+1).build());
    }

    @Override
    public ProjectAmount findPa(Integer userId, Integer projectId) {
        Condition c = new Condition(ProjectAmount.class);
        c.createCriteria().andEqualTo("userId",userId)
                .andEqualTo("projectId",projectId);
        List<ProjectAmount> pas = findByCondition(c);
        if (CollUtil.isNotEmpty(pas)){
            return pas.get(0);
        }
        User user = userService.findById(userId);
        ProjectAmount pa = ProjectAmount.builder()
                .projectId(projectId).activityAmount(BigDecimal.ZERO)
                .activityLimit(BigDecimal.ZERO)
                .activityNum(0)
                .activityOutput(BigDecimal.ZERO)
                .userId(userId)
                .createTime(DateUtil.date())
                .companyId(user.getCompanyId())
                .build();
        saveUseGeneratedKeys(pa);
        return pa;
    }

    @Override
    public BigDecimal getActivityOutput(Integer userId) {
        Condition c = new Condition(ProjectAmount.class);
        c.createCriteria().andEqualTo("userId",userId) ;
        List<ProjectAmount> pas = findByCondition(c);
        if (CollUtil.isNotEmpty(pas)){
            BigDecimal out = BigDecimal.ZERO;
            for (ProjectAmount pa : pas){
                out = out.add(pa.getActivityOutput());
            }
            return out;
        }  else {
            return BigDecimal.ZERO;
        }
    }
}
