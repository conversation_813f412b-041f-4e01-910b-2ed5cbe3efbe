package com.project.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.project.dao.ProjectItemMapper;
import com.project.model.ProjectItem;
import com.project.model.ProjectItemMember;
import com.project.service.ProjectItemMemberService;
import com.project.service.ProjectItemService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.entity.Condition;

import javax.annotation.Resource;
import java.util.List;


/**
 * Created by CodeGenerator on 2025/01/22.
 */
@Service
@Transactional
public class ProjectItemServiceImpl extends AbstractService<ProjectItem> implements ProjectItemService {
    @Resource
    private ProjectItemMapper bProjectItemMapper;

    @Resource
    private ProjectItemMemberService projectItemMemberService;

    @Override
    public void saveProjectItem(List<ProjectItem> items) {
        for (ProjectItem projectItem:items ) {
            if (projectItem.getDays()<=0){
                projectItem.setDays(1);
            }
            if (projectItem.getId() == null){
                projectItem.setStatus(0);
                projectItem.setAuditRet(0);
                projectItem.setCreateTime(DateUtil.date());
                saveUseGeneratedKeys(projectItem);
                if (projectItem.getPrincipalUserId() != null){
                    Condition c = new Condition(ProjectItemMember.class);
                    c.createCriteria().andEqualTo("projectItemId",projectItem.getId())
                            .andEqualTo("userId",projectItem.getPrincipalUserId());
                    List<ProjectItemMember> members = projectItemMemberService.findByCondition(c);
                    if (CollUtil.isEmpty(members)){
                        ProjectItemMember member = new ProjectItemMember();
                        member.setProjectId(projectItem.getProjectId());
                        member.setProjectItemId(projectItem.getId());
                        member.setCreateTime(DateUtil.date());
                        member.setUserId(projectItem.getPrincipalUserId());
                        projectItemMemberService.save(member);
                    }
                }
            } else {
                ProjectItem item = findById(projectItem.getId());
                if (item == null){
                    continue;
                }
                projectItem.setUpdateTime(DateUtil.date());
                update(projectItem);
                if (projectItem.getPrincipalUserId() != null && item.getPrincipalUserId().compareTo(projectItem.getPrincipalUserId()) !=0){
                    Condition c = new Condition(ProjectItemMember.class);
                    c.createCriteria().andEqualTo("userId",projectItem.getPrincipalUserId())
                            .andEqualTo("projectItemId",projectItem.getId());
                    List<ProjectItemMember> members = projectItemMemberService.findByCondition(c);
                    if (CollUtil.isEmpty(members)){
                        ProjectItemMember member = new ProjectItemMember();
                        member.setProjectId(item.getProjectId());
                        member.setUserId(projectItem.getPrincipalUserId());
                        member.setProjectItemId(projectItem.getId());
                        member.setCreateTime(DateUtil.date());
                        projectItemMemberService.save(member);
                    }
                }
            }
        }
    }
}
