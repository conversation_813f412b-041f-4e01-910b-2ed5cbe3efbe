package com.project.web.parameters;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 */
@Data
@ApiModel("玩法参数")
public class PlayDataParam {

    @ApiModelProperty(name="params",value = "入参json模式",required=true)
    private String params;

    @ApiModelProperty(name="id",value = "玩法id",required=true)
    private Integer id;

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
