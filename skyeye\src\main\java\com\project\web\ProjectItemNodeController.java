package com.project.web;
import cn.hutool.core.date.DateUtil;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.model.ProjectItemNode;
import com.project.service.ProjectItemNodeService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import java.util.List;

/**
* Created by CodeGenerator on 2025/01/25.
*/
@Api(tags = "projectItemNode管理")
@RestController
@RequestMapping("/project/item/node")
public class ProjectItemNodeController {

	private static Logger log = LoggerFactory.getLogger(ProjectItemNodeController.class);

    @Resource
    private ProjectItemNodeService projectItemNodeService;

    @PostMapping("/add")
	@ApiOperation(value = "projectItemNode新增",httpMethod = "POST")
    public Result add(@RequestBody ProjectItemNode projectItemNode) {
    	if(projectItemNode == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	try {
    		projectItemNode.setCreateTime(DateUtil.date());
    		projectItemNodeService.save(projectItemNode);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success();
    }


    @PostMapping("/update")
	@ApiOperation(value = "projectItemNode更新",httpMethod = "POST")
    public Result update(@RequestBody ProjectItemNode projectItemNode) {
    	if(projectItemNode == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	if(projectItemNode.getId() == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	try {
    //		projectItemNode.setUpdateTime(new Date());
    //		projectItemNode.setUpdateUserId(userId);
    		projectItemNodeService.update(projectItemNode);
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @RequestMapping("/detail")
	@ApiOperation(value = "projectItemNode获取详情",httpMethod = "POST")
    public Result<ProjectItemNode> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	ProjectItemNode projectItemNode = null;
    	try {
    		projectItemNode = projectItemNodeService.findById(id);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success(projectItemNode);
    }

    @RequestMapping("/list")
	@ApiOperation(value = "projectItemNode获取列表",httpMethod = "POST")
    public Result<List<ProjectItemNode>> list(@RequestBody ProjectItemNode projectItemNode, @RequestParam(defaultValue = "0") Integer page, @RequestParam(defaultValue = "10") Integer size) {

        PageHelper.startPage(page, size);
        
        Condition condition = new Condition(projectItemNode.getClass());
        Criteria criteria = condition.createCriteria();
//        criteria.andEqualTo("name", city.getName());
		PageInfo pageInfo = null;
		try {
    		 List<ProjectItemNode> list = projectItemNodeService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
