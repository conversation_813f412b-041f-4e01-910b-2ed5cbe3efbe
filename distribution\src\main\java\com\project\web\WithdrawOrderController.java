package com.project.web;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.model.Bankcard;
import com.project.model.Freeze;
import com.project.model.User;
import com.project.model.WithdrawOrder;
import com.project.service.*;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.project.web.parameters.DisposeWithdrawOrderParam;
import com.project.web.parameters.WithdrawOrderParam;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
* Created by CodeGenerator on 2025/05/12.
*/
@Api(tags = "提现订单管理")
@RestController
@RequestMapping("/withdraw/order")
public class WithdrawOrderController {

	private static Logger log = LoggerFactory.getLogger(WithdrawOrderController.class);

    @Resource
    private WithdrawOrderService withdrawOrderService;
	@Resource
	private BankcardService bankcardService;


	@Resource
	private UserUtil userUtil;

	@Resource
	private UserService userService;

	@Resource
	private FreezeService freezeService;

    @PostMapping("/withdraw")
	@ApiOperation(value = "提现",httpMethod = "POST")
    public Result withdraw(@RequestBody WithdrawOrderParam param, HttpServletRequest request) {
		//校验权限
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
    	if(param == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	try {
			//判断余额够不够
			User user = userService.findById(tokenUser.getId());
			if (user == null){
				return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
			}
			if (param.getAmount().compareTo(BigDecimal.valueOf(110)) <=0){
				return ResultGenerator.genFailResult(ResultCode.PARAMETER_IS_ILLEGALITY);
			}
			if (user.getAmount().subtract(user.getFreezeAmount()).subtract(param.getAmount()).compareTo(BigDecimal.ZERO) < 0 ){
				return ResultGenerator.genFailResult(ResultCode.AMOUNT_IS_NOT_INSUFFICIENT);
			}
			WithdrawOrder withdrawOrder = new WithdrawOrder();
			withdrawOrder.setAmount(param.getAmount());
			withdrawOrder.setStatus(1);
			withdrawOrder.setName(tokenUser.getName()+"提现");
			withdrawOrder.setType(param.getType());
			withdrawOrder.setCreateTime(DateUtil.date());
			withdrawOrder.setUserId(tokenUser.getId());
    		withdrawOrderService.saveUseGeneratedKeys(withdrawOrder);
			//增加冻结金额
			userService.deal(tokenUser.getId(),7,(byte)4, param.getAmount(),Long.valueOf(withdrawOrder.getId()));
			Freeze freeze = new Freeze();
			freeze.setAmount(param.getAmount());
			freeze.setStatus(1);
			freeze.setType(1);
			freeze.setBizId(Long.valueOf(withdrawOrder.getId()));
			freeze.setCreateTime(DateUtil.date());
			freeze.setBizType(1);
			freeze.setUserId(tokenUser.getId());
			freeze.setNo(withdrawOrder.getId());
			freezeService.save(freeze);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success();
    }


    @PostMapping("/dispose")
	@ApiOperation(value = "打款处理",httpMethod = "POST")
    public Result dispose(@RequestBody DisposeWithdrawOrderParam param, HttpServletRequest request) {
		//校验权限
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		User u = userService.findById(tokenUser.getId());
		if (StrUtil.equals(u.getStatus() , "1")){
			return ResultGenerator.genFailResult(ResultCode.LOGIN_USER_STATUS_IS_ERROR);
		}
    	if(param == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
    	if(param.getWithdrawOrderId() == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	try {
			WithdrawOrder order = withdrawOrderService.findById(param.getWithdrawOrderId());
			if (order == null){
				return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
			}
			if (order.getStatus() == 2){
				return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
			}
			order.setStatus(2);
			order.setOutFlow(param.getOutFlow());
			order.setDisposeUser(tokenUser.getName());
			order.setDisposeUserId(tokenUser.getId());
			//解冻
			Freeze freeze = freezeService.findBy("no",param.getWithdrawOrderId());
			if (freeze == null){
				return ResultGenerator.genFailResult(ResultCode.FREEZE_IS_NULL);
			}
			if (freeze.getStatus() != 1){
				return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
			}
			freeze.setStatus(2);
			freezeService.update(freeze);
			//解冻金额
			userService.deal(order.getUserId(), 8, (byte) 4, order.getAmount().negate(), Long.valueOf(order.getId()));
			//余额扣除提现金额
			Boolean deal = userService.deal(order.getUserId(), 6, (byte) 1, order.getAmount().negate(), Long.valueOf(order.getId()));
			if (deal){
				//增加已提现金额
				userService.deal(order.getUserId(), 6,(byte)6, order.getAmount(),Long.valueOf(order.getId()));
				withdrawOrderService.update(order);
			}
		} catch (Exception e) {
			log.error("更新对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

    @RequestMapping("/detail")
	@ApiOperation(value = "withdrawOrder获取详情",httpMethod = "GET")
    public Result<WithdrawOrder> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	WithdrawOrder withdrawOrder = null;
    	try {
    		withdrawOrder = withdrawOrderService.findById(id);
			if (withdrawOrder !=null){
				Bankcard bankcard = bankcardService.findBy("userId", withdrawOrder.getUserId());
				withdrawOrder.setBankcard(bankcard);
			}
			return Result.success(withdrawOrder);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
    }

    @RequestMapping("/list")
	@ApiOperation(value = "withdrawOrder获取列表",httpMethod = "POST")
    public Result<List<WithdrawOrder>> list(@RequestBody WithdrawOrder withdrawOrder) {
        PageHelper.startPage(withdrawOrder.getPage(), withdrawOrder.getSize());
        Condition condition = new Condition(withdrawOrder.getClass());
        Criteria criteria = condition.createCriteria();
		if (withdrawOrder.getStatus() != null){
			criteria.andEqualTo("status", withdrawOrder.getStatus());
		}
		if (withdrawOrder.getUserId() != null){
			criteria.andEqualTo("userId", withdrawOrder.getUserId());
		}
		PageInfo pageInfo = null;
		try {
			condition.setOrderByClause("create_time desc");
    		 List<WithdrawOrder> list = withdrawOrderService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
			 pageInfo.setList(list.stream().map(e -> {
				 if (e.getUserId() != null){
					 Bankcard bankcard = bankcardService.findBy("userId", e.getUserId());
					 e.setBankcard(bankcard);
				 }
				 return e;
			 }).collect(Collectors.toList()));
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }
}
