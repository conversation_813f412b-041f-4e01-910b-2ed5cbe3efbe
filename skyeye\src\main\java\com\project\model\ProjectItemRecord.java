package com.project.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@ApiModel(value="com.project.model.ProjectItemRecord")
@Table(name = "b_project_item_record")
public class ProjectItemRecord implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 项目id
     */
    @Column(name = "project_id")
    @ApiModelProperty(value="projectId项目id")
    private Integer projectId;

    /**
     * 项目明细id
     */
    @Column(name = "project_item_id")
    @ApiModelProperty(value="projectItemId项目明细id")
    private Integer projectItemId;

    /**
     * 团队id
     */
    @Column(name = "team_id")
    @ApiModelProperty(value="teamId团队id")
    private Integer teamId;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty(value="userId用户id")
    private Integer userId;

    /**
     * 上班图片
     */
    @Column(name = "on_pics")
    @ApiModelProperty(value="onPics上班图片")
    private String onPics;

    @ApiModelProperty(value="内容")
    private String content;

    /**
     * 下班图片
     */
    @Column(name = "off_pics")
    @ApiModelProperty(value="offPics下班图片")
    private String offPics;

    /**
     * 特殊情况
     */
    @ApiModelProperty(value="special特殊情况")
    private String special;

    /**
     * 签到天
     */
    @Column(name = "sign_day")
    @ApiModelProperty(value="signDay签到天")
    private String signDay;

    /**
     * 上班签到时间
     */
    @Column(name = "on_time")
    @ApiModelProperty(value="onTime上班签到时间")
    private Date onTime;

    /**
     * 下班签到时间
     */
    @Column(name = "off_time")
    @ApiModelProperty(value="offTime下班签到时间")
    private Date offTime;

    /**
     * 类型 1上班 2请假
     */
    @ApiModelProperty(value="type类型 1上班 2请假 3上班打卡 4下班打卡")
    private Integer type;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取项目id
     *
     * @return project_id - 项目id
     */
    public Integer getProjectId() {
        return projectId;
    }

    /**
     * 设置项目id
     *
     * @param projectId 项目id
     */
    public void setProjectId(Integer projectId) {
        this.projectId = projectId;
    }

    /**
     * 获取项目明细id
     *
     * @return project_item_id - 项目明细id
     */
    public Integer getProjectItemId() {
        return projectItemId;
    }

    /**
     * 设置项目明细id
     *
     * @param projectItemId 项目明细id
     */
    public void setProjectItemId(Integer projectItemId) {
        this.projectItemId = projectItemId;
    }

    /**
     * 获取团队id
     *
     * @return team_id - 团队id
     */
    public Integer getTeamId() {
        return teamId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    /**
     * 设置团队id
     *
     * @param teamId 团队id
     */
    public void setTeamId(Integer teamId) {
        this.teamId = teamId;
    }

    /**
     * 获取用户id
     *
     * @return user_id - 用户id
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * 设置用户id
     *
     * @param userId 用户id
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取上班图片
     *
     * @return on_pics - 上班图片
     */
    public String getOnPics() {
        return onPics;
    }

    /**
     * 设置上班图片
     *
     * @param onPics 上班图片
     */
    public void setOnPics(String onPics) {
        this.onPics = onPics;
    }

    /**
     * 获取下班图片
     *
     * @return off_pics - 下班图片
     */
    public String getOffPics() {
        return offPics;
    }

    /**
     * 设置下班图片
     *
     * @param offPics 下班图片
     */
    public void setOffPics(String offPics) {
        this.offPics = offPics;
    }

    /**
     * 获取特殊情况
     *
     * @return special - 特殊情况
     */
    public String getSpecial() {
        return special;
    }

    /**
     * 设置特殊情况
     *
     * @param special 特殊情况
     */
    public void setSpecial(String special) {
        this.special = special;
    }

    /**
     * 获取签到天
     *
     * @return sign_day - 签到天
     */
    public String getSignDay() {
        return signDay;
    }

    /**
     * 设置签到天
     *
     * @param signDay 签到天
     */
    public void setSignDay(String signDay) {
        this.signDay = signDay;
    }

    /**
     * 获取上班签到时间
     *
     * @return on_time - 上班签到时间
     */
    public Date getOnTime() {
        return onTime;
    }

    /**
     * 设置上班签到时间
     *
     * @param onTime 上班签到时间
     */
    public void setOnTime(Date onTime) {
        this.onTime = onTime;
    }

    /**
     * 获取下班签到时间
     *
     * @return off_time - 下班签到时间
     */
    public Date getOffTime() {
        return offTime;
    }

    /**
     * 设置下班签到时间
     *
     * @param offTime 下班签到时间
     */
    public void setOffTime(Date offTime) {
        this.offTime = offTime;
    }

    /**
     * 获取类型 1上班 2请假
     *
     * @return type - 类型 1上班 2请假
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置类型 1上班 2请假
     *
     * @param type 类型 1上班 2请假
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}