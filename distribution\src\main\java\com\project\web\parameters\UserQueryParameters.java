package com.project.web.parameters;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 账号登录参数
 */
@Data
@ApiModel("账号查询参数")
public class UserQueryParameters {

    @ApiModelProperty(name="account",value = "登录账号或id",required=true)
    private String account;

    @ApiModelProperty(name="roleId",value = "角色id",required=true)
    private String roleId;

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
