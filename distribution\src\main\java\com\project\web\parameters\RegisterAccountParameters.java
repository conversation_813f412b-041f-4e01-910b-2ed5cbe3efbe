package com.project.web.parameters;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 账号登录参数
 */
@Data
@ApiModel("账号注册参数")
public class RegisterAccountParameters {

    @ApiModelProperty(name="account",value = "登录账号",required=true)
    private String account;

    @ApiModelProperty(name="pwd",value = "登录密码",required=true)
    private String pwd;

    @ApiModelProperty(name="transpwd",value = "交易密码",required=true)
    private String transpwd;

    @ApiModelProperty(name="pwd",value = "邀请码",required=true)
    private String code;

    @ApiModelProperty(name="type",value = "类型 1用户 2渠道",required=true)
    private Integer type;

    @ApiModelProperty(name="companyId",value = "公司id",required=true,example = "1")
    private Integer companyId;

    @ApiModelProperty(name="address",value = "地址",required=false)
    private String address;

    @ApiModelProperty(name="phone",value = "电话号码",required=false)
    private String phone;

    @ApiModelProperty(name="name",value = "名字",required=false)
    private String name;

    @ApiModelProperty(value="工号",required=false)
    private String workNo;


    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
