//package com.project.web.socket;
//
//import cn.hutool.core.collection.CollUtil;
//import cn.hutool.core.util.StrUtil;
//import com.corundumstudio.socketio.SocketIOClient;
//import com.corundumstudio.socketio.SocketIOServer;
//import com.corundumstudio.socketio.annotation.OnConnect;
//import com.corundumstudio.socketio.annotation.OnDisconnect;
//import org.springframework.beans.factory.annotation.Autowired;
//import javax.annotation.PostConstruct;
//import javax.annotation.PreDestroy;
//import javax.annotation.Resource;
//
//import org.slf4j.Logger;
//import org.slf4j.LoggerFactory;
//import org.springframework.stereotype.Component;
//
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.concurrent.ConcurrentMap;
//
//
///**
// * @Author: lxk
// * @Description:
// * @Date: 2023/6/03 21:21
// */
//@Component
//public class MySocketHandler {
//    private final Logger log = LoggerFactory.getLogger(this.getClass());
//    @Autowired
//    private SocketIOServer socketIoServer;
//    @Resource
//    private SocketUtil socketUtil;
//
////    @Autowired
////    private ICircleUserInfoService circleUserInfoService;
//
//
//    @PostConstruct
//    private void start(){
//        try {
//            socketIoServer.start();
//            log.info("socketServer is start");
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//    @PreDestroy
//    private void destroy(){
//        try {
//            socketIoServer.stop();
//            log.info("socketServer is stop");
//        }catch (Exception e){
//            e.printStackTrace();
//        }
//    }
//    @OnConnect
//    public void connect(SocketIOClient client) {
//        try {
//            String userId = client.getHandshakeData().getSingleUrlParam("userId");
//            String clientId = client.getHandshakeData().getSingleUrlParam("clientId");
//            String circleId = client.getHandshakeData().getSingleUrlParam("circleId");
//            // type 1 一对一  circleId:  min(userId)_max(userId) 2 群聊  circleId：圈子id
//            String type = client.getHandshakeData().getSingleUrlParam("type");
//            log.info("[建立sokect连接]客户端Transport:"+client.getTransport()+"RemoteAddress:"+client.getRemoteAddress()
//                    +"clientId: "+ clientId+ "  userId: "+ userId+" type: "+ type+ "开始连接");
//            if (StrUtil.equals("1",type)){
//
//            } else  if (StrUtil.equals("2",type)){
//
//            } else {
//                log.error("[建立sokect连接]未知的类型type："+type);
//                return;
//            }
//
////            CircleUserInfoData user = circleUserInfoService.findCircleUserInfoByUserId(Long.valueOf(circleId), Integer.valueOf(userId));
////            if (user == null){
////
////            }
//            //自定义了一个map存放连接，自定义了组、使用redis存储、同步所有用户信息
//            ConcurrentMap<String,SocketUserBo> map = SocketUtil.connectMap.get(circleId);
//            if (CollUtil.isEmpty(map)){
//                map = new ConcurrentHashMap();
//            }
//            map.put(clientId, SocketUserBo.builder().status(1).client(client).userId(userId).type(Integer.parseInt(type)).build());
//            //把连接 存入 本服务
//            SocketUtil.connectMap.put(circleId,map);
//        }catch (Exception e){
//            log.error("[建立sokect连接]客户端clientId: 接受信息错误",e);
//            log.info("[建立sokect连接]客户端clientId: 接受信息错误");
//        }
//    }
//
//    private void closeConnect(String closeClientId, String group) {
//        try {
//            if (!SocketUtil.connectMap.get(group).isEmpty()){
//                SocketUtil.connectMap.get(group).remove(closeClientId);
//            }
//            socketUtil.removeRedisUser(closeClientId,group);
//            if (socketUtil.getRedisUser(group).isEmpty()){
//                SocketUtil.connectMap.remove(group);
//                //TODO mq通知所有server 销毁房间
//            }
//        }catch (Exception e){
//            log.error("[关闭sokect连接]客户端clientId: 离开错误",e);
//            log.info("[关闭sokect连接]客户端clientId: 离开错误");
//        }
//    }
//
//
//    @OnDisconnect
//    public void onDisconnect(SocketIOClient client) {
//        try {
//            String clientId = client.getHandshakeData().getSingleUrlParam("clientId");
//            String group = client.getHandshakeData().getSingleUrlParam("caseId");
//            closeConnect(clientId,group);
//        }catch (Exception e){
//            log.error("[关闭sokect连接]客户端clientId: 离开错误",e);
//            log.info("[关闭sokect连接]客户端clientId: 离开错误");
//        }
//
//    }
//}