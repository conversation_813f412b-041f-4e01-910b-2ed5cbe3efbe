package com.project.model;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
@ApiModel(value="com.project.model.WithdrawOrder")
@Table(name = "c_withdraw_order")
public class WithdrawOrder extends BaseBeen implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty(value="userId用户id")
    private Integer userId;

    /**
     * 支付金额
     */
    @ApiModelProperty(value="amount支付金额")
    private BigDecimal amount;

    /**
     * 类型 1提现
     */
    @ApiModelProperty(value="type类型 1提现")
    private Integer type;

    /**
     * 状态 1待打款 2已打款
     */
    @ApiModelProperty(value="status状态 1待打款 2已打款 3已撤销 4已取消")
    private Integer status;

    /**
     * 处理人
     */
    @Column(name = "dispose_user")
    @ApiModelProperty(value="disposeUser处理人")
    private String disposeUser;

    @Column(name = "out_flow")
    @ApiModelProperty(value="外部流水号")
    private String outFlow;

    /**
     * 处理人id
     */
    @Column(name = "dispose_user_id")
    @ApiModelProperty(value="disposeUserId处理人id")
    private Integer disposeUserId;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value="createUser创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    @Transient
    @ApiModelProperty(value="银行卡信息")
    private Bankcard bankcard;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取名称
     *
     * @return name - 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取用户id
     *
     * @return user_id - 用户id
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * 设置用户id
     *
     * @param userId 用户id
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取支付金额
     *
     * @return amount - 支付金额
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 设置支付金额
     *
     * @param amount 支付金额
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 获取类型 1提现
     *
     * @return type - 类型 1提现
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置类型 1提现
     *
     * @param type 类型 1提现
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取状态 1待打款 2已打款
     *
     * @return status - 状态 1待打款 2已打款
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 1待打款 2已打款
     *
     * @param status 状态 1待打款 2已打款
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取处理人
     *
     * @return dispose_user - 处理人
     */
    public String getDisposeUser() {
        return disposeUser;
    }

    /**
     * 设置处理人
     *
     * @param disposeUser 处理人
     */
    public void setDisposeUser(String disposeUser) {
        this.disposeUser = disposeUser;
    }

    /**
     * 获取处理人id
     *
     * @return dispose_user_id - 处理人id
     */
    public Integer getDisposeUserId() {
        return disposeUserId;
    }

    /**
     * 设置处理人id
     *
     * @param disposeUserId 处理人id
     */
    public void setDisposeUserId(Integer disposeUserId) {
        this.disposeUserId = disposeUserId;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}