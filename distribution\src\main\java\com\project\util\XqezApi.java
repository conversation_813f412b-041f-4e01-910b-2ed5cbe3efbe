//package com.project.util;
//
//import cn.hutool.core.date.DateUtil;
//import cn.hutool.crypto.SecureUtil;
//import cn.hutool.http.HttpRequest;
//import cn.hutool.http.HttpUtil;
//import cn.hutool.json.JSONObject;
//import com.project.model.Pachong;
//import lombok.SneakyThrows;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.stereotype.Component;
//
//import java.net.URLEncoder;
//import java.util.HashMap;
//import java.util.Map;
//
//@Slf4j
////@Component
//public class XqezApi {
//
////    @Value("${baidu.trans.api.host}")
//    private static String TRANS_API_HOST = "https://www.syuantech.cn/prod-api/";
//
////    @Value("${baidu.app.id}")
////    private String appid = "20191022000343414";
////
////    @Value("${baidu.security.key}")
////    private String securityKey = "tHaHt6dWYOG1VqzZMFBj";
//
////    public static String type = "{\"auto\":\"自动检测\",\"zh\":\"中文\",\"en\":\"英语\",\"yue\":\"粤语\",\"wyw\":\"文言文\",\"jp\":\"日语\",\"kor\":\"韩语\",\"fra\":\"法语\",\"spa\":\"西班牙语\",\"th\":\"泰语\",\"ara\":\"阿拉伯语\",\"ru\":\"俄语\",\"pt\":\"葡萄牙语\",\"de\":\"德语\",\"it\":\"意大利语\",\"el\":\"希腊语\",\"nl\":\"荷兰语\",\"pl\":\"波兰语\",\"bul\":\"保加利亚语\",\"est\":\"爱沙尼亚语\",\"dan\":\"丹麦语\",\"fin\":\"芬兰语\",\"cs\":\"捷克语\",\"rom\":\"罗马尼亚语\",\"slo\":\"斯洛文尼亚语\",\"swe\":\"瑞典语\",\"hu\":\"匈牙利语\",\"cht\":\"繁体中文\",\"vie\":\"越南语\"}";
//
//    @SneakyThrows
//    public static String getSchool(String query) {
//        return HttpGet.get(TRANS_API_HOST+"/tenant/tenant/list?name="+URLEncoder.encode(query,"UTF-8"),null);
//    }
//
//    @SneakyThrows
//    public static String getLogin(Pachong pachong) {
//        //{
//        //	"enterpriseName": "重庆安全技术职业学院",
//        //	"schoolId": "1803690126952669185",
//        //	"schoolName": "重庆安全技术职业学院",
//        //	"studentName": "邹矮巧",
//        //	"studentNumber": "020139",
//        //	"openId": "o4gWI5WeLdmo1SVmxZypx8J4UvS8"
//        //}
//        JSONObject json = new JSONObject();
//        json.put("enterpriseName",pachong.getSchool());
//        json.put("schoolId",pachong.getSchoolId());
//        json.put("schoolName",pachong.getSchool());
//        json.put("studentName",pachong.getName());
//        json.put("studentNumber",pachong.getCode());
////        json.put("openId",pachong.getOpenId());
//        String body = json.toString();
//        log.error("getLogin body:" +body);
//        https://www.syuantech.cn/prod-api/auth/appLogin
//        return HttpUtil.post(TRANS_API_HOST+"/auth/appLogin",body);
//    }
//
//    @SneakyThrows
//    public static String sign(Pachong pachong) {
//        //{
//        //	"address": "重庆市重庆市江北区永和路 拓新时代B区",
//        //	"lat": 29.627263251578384,
//        //	"lng": 106.75520081973549,
//        //	"studentId": "1803728301961801741",
//        //	"clockStatus": 1,
//        //	"planId": "1806219528820027394",
//        //	"clockTime": "2024-09-02 12:07:20",
//        //	"teacherId": "202202263",
//        //	"rangeTime": "07:30:00,23:30:59"
//        //}
//        JSONObject json = new JSONObject();
//        json.put("address",pachong.getAddress());
//        json.put("lat",Double.valueOf(pachong.getLat()));
//        json.put("lng",Double.valueOf(pachong.getLng()));
//        json.put("clockStatus",1);
//        json.put("studentId",pachong.getStuId());
//        json.put("planId",pachong.getPlanId());
//        json.put("clockTime", DateUtil.now());
//        json.put("teacherId",pachong.getTeacherId());
//        json.put("rangeTime","07:30:00,23:30:59");
//        String body = json.toString();
//        log.error("sign body:" +body);
//        HttpRequest httpRequest = HttpRequest.post(TRANS_API_HOST + "/internship/sxClock");
//        httpRequest.header("Authorization",pachong.getToken());
//        httpRequest.body(body);
//        return httpRequest.execute().body();
//    }
//
//    @SneakyThrows
//    public static String getPlanListDetail(Pachong pachong) {
//        HttpRequest httpRequest = HttpRequest.get(TRANS_API_HOST + "/internship/plan/listDetail?studentId="+pachong.getStuId());
//        httpRequest.header("Authorization",pachong.getToken());
//        return httpRequest.execute().body();
//    }
//
//    @SneakyThrows
//    public static String getInfo(Pachong pachong) {
//        HttpRequest httpRequest = HttpRequest.get(TRANS_API_HOST + "/system/user/getInfo");
//        httpRequest.header("Authorization",pachong.getToken());
//        return httpRequest.execute().body();
//    }
//
//
//    @SneakyThrows
//    public static void main(String[] args) {
//        String s = getSchool("重庆");
//        System.out.println(s);
//        System.out.println(1);
//
//    }
//
////    private Map<String, String> buildParams(String query, String from, String to) {
////        Map<String, String> params = new HashMap<String, String>();
////        params.put("q", query);
////        params.put("from", from);
////        params.put("to", to);
////        params.put("appid", appid);
////        // 随机数
////        String salt = String.valueOf(System.currentTimeMillis());
////        params.put("salt", salt);
////        // 签名
////        String src = appid + query + salt + securityKey; // 加密前的原文
////        params.put("sign", SecureUtil.md5(src));
////        return params;
////    }
//
//}
