package com.project.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@Data
@AllArgsConstructor
@Builder
@NoArgsConstructor
@ApiModel(value="com.project.model.Team")
@Table(name = "b_team")
public class Team implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty(value="userId用户id")
    private Integer userId;

    @ApiModelProperty(value="头像")
    private String icon;

    /**
     * 公司id
     */
    @Column(name = "company_id")
    @ApiModelProperty(value="companyId公司id")
    private String companyId;

    /**
     * 姓名
     */
    @Column(name = "member_name")
    @ApiModelProperty(value="memberName姓名")
    private String memberName;

    /**
     * 成员用户id
     */
    @Column(name = "member_id")
    @ApiModelProperty(value="memberId成员用户id")
    private Integer memberId;

    /**
     * 状态 0 正常 1注销 
     */
    @ApiModelProperty(value="status状态 0 正常 1注销 ")
    private String status;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    private static final long serialVersionUID = 1L;


}