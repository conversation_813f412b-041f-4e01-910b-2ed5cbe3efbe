package com.project.web.parameters;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 
 */
@Data
@ApiModel("卡密参数")
public class CodeParam {

    @ApiModelProperty(name="code",value = "code",required=true)
    private String code;

    @ApiModelProperty(name="secret",value = "密钥",required=true)
    private String secret;

    @ApiModelProperty(name="id",value = "玩法id",required=true)
    private Integer id;

    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
