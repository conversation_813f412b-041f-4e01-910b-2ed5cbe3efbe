package com.project.web;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.project.core.Result;
import com.project.core.ResultGenerator;
import com.project.core.UserUtil;
import com.project.model.ProjectItemRecord;
import com.project.model.Team;
import com.project.model.User;
import com.project.service.ProjectItemRecordService;
import com.project.service.TeamService;

import com.project.core.ResultCode;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.project.service.UserService;
import com.project.web.parameters.TeamBO;
import com.project.web.parameters.WorkDays;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
* Created by CodeGenerator on 2025/01/22.
*/
@Api(tags = "team管理")
@RestController
@RequestMapping("/team")
public class TeamController {

	private static Logger log = LoggerFactory.getLogger(TeamController.class);

	@Resource
	private TeamService teamService;

	@Resource
	private UserService userService;
	@Resource
	private ProjectItemRecordService projectItemRecordService;

	@Resource
	private UserUtil userUtil;


	@PostMapping("/add")
	@ApiOperation(value = "新增团队成员", httpMethod = "POST")
	public Result add(@RequestBody TeamBO bo, HttpServletRequest request) {

		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		if (bo == null) {
			return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
		}
		try {
			Team team = BeanUtil.toBean(bo, Team.class);
			team.setUserId(tokenUser.getId());
			team.setCreateTime(DateUtil.date());
			team.setStatus("0");
			teamService.save(team);
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}

		return Result.success();
	}

	@RequestMapping("/delete")
	@ApiOperation(value = "team删除", httpMethod = "GET")
	public Result delete(@RequestParam Integer id) {
		if (id == null) {
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			teamService.deleteById(id);
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success();
	}


	@RequestMapping("/myList")
	@ApiOperation(value = "我的团队列表", httpMethod = "GET")
	public Result<List<Team>> myList(@RequestParam(defaultValue = "0") Integer page, @RequestParam(defaultValue = "10") Integer size, HttpServletRequest request) {

		PageHelper.startPage(page, size);

		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null) {
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}

		Condition condition = new Condition(Team.class);
		Criteria criteria = condition.createCriteria();
		criteria.andEqualTo("userId", tokenUser.getId());
		PageInfo pageInfo = null;
		try {
			condition.setOrderByClause("id desc");
			List<Team> list = teamService.findByCondition(condition);
			pageInfo = new PageInfo(list);
			pageInfo.setList(list.stream().map(e ->{
				User member = userService.findById(e.getMemberId());
				if (member != null){
					e.setIcon(member.getIcon());
					e.setMemberName(member.getName() == null? member.getLoginAccount() : member.getName());
				}
				return e;
			}).collect(Collectors.toList()));
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success(pageInfo);
	}

	@RequestMapping("/workStatistics")
	@ApiOperation(value = "我的团队的工时统计", httpMethod = "GET")
	public Result<List<TeamBO>> workStatistics(@RequestParam String startTime,@RequestParam String endTime,@RequestParam(defaultValue = "0") Integer page, @RequestParam(defaultValue = "10") Integer size, HttpServletRequest request) {
		PageHelper.startPage(page, size);

		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null) {
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}

		Condition condition = new Condition(Team.class);
		Criteria criteria = condition.createCriteria();
		criteria.andEqualTo("userId", tokenUser.getId());
		PageInfo pageInfo = null;
		try {
			List<Team> list = teamService.findByCondition(condition);
			pageInfo = new PageInfo(list);
			pageInfo.setList(list.stream().map(e -> {
				TeamBO bo = BeanUtil.toBean(e,TeamBO.class);
				Condition c = new Condition(ProjectItemRecord.class);
				c.createCriteria().andEqualTo("type",1)
						.andBetween("createTime",startTime,endTime)
						.andEqualTo("teamId",tokenUser.getId());
				int count = projectItemRecordService.countByCondition(c);
				bo.setWorkDays(count);
				return bo;
			}).collect(Collectors.toList()));
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}", e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success(pageInfo);
	}

	@RequestMapping("/monthWorkDays")
	@ApiOperation(value = "我的团队的工时统计-获取每月工时", httpMethod = "GET")
	public Result<List<WorkDays>> monthWorkDays(@RequestParam Integer year, HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null) {
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		Map<Integer, WorkDays> monthlyWorkHours = calculateMonthlyWorkHours(year,tokenUser.getId());
		return ResultGenerator.genSuccessResult(monthlyWorkHours.values());

	}

	@RequestMapping("/workDays")
	@ApiOperation(value = "我的团队的工时统计-获取当月工时", httpMethod = "GET")
	public Result<List<ProjectItemRecord>> workDays(@RequestParam Integer year,@RequestParam Integer month, HttpServletRequest request) {
		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null) {
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		if (year == null || year == 0 ){
			return ResultGenerator.genFailResult(ResultCode.YEAR_IS_NULL);
		}
		if ( month == null || month == 0){
			return ResultGenerator.genFailResult(ResultCode.MONTH_IS_NULL);
		}
		String[] monthDateRange = getMonthDateRange(year, month);
		Condition c = new Condition(ProjectItemRecord.class);
		c.createCriteria().andEqualTo("userId",tokenUser.getId()).andEqualTo("type",1)
				.andBetween("createTime",monthDateRange[0],monthDateRange[1]);
		List<ProjectItemRecord> records = projectItemRecordService.findByCondition(c);
		return ResultGenerator.genSuccessResult(records);

	}

	/**
	 * 计算指定年份每个月的工时
	 * @param year 年份
	 * @return 包含每个月工时的 Map，键为月份（1-12），值为工时
	 */
	public  Map<Integer, WorkDays> calculateMonthlyWorkHours(int year,int userId) {
		Map<Integer, WorkDays> monthlyWorkHours = new HashMap<>();
		// 遍历 1 到 12 月
		for (int month = 1; month <= 12; month++) {
			String[] monthDateRange = getMonthDateRange(year, month);
			Condition c = new Condition(ProjectItemRecord.class);
			c.createCriteria().andEqualTo("userId",userId).andEqualTo("type",4)
					.andBetween("createTime",monthDateRange[0],monthDateRange[1]);
			int workHours = projectItemRecordService.countByCondition(c);
			WorkDays workDays = new WorkDays();
			workDays.setName(month+"月");
			workDays.setVal(workHours);
			monthlyWorkHours.put(month,workDays);
		}
		return monthlyWorkHours;
	}

	/**
	 * 根据输入的年份和月份生成当月第一天 00:00:00 和下个月第一天 00:00:00 的字符串表示
	 *
	 * @param year  年份
	 * @param month 月份
	 * @return 包含当月第一天和下个月第一天日期时间字符串的数组
	 */
	public static String[] getMonthDateRange(int year, int month) {
		// 创建 YearMonth 对象表示指定的年月
		YearMonth yearMonth = YearMonth.of(year, month);

		// 获取当月第一天的 LocalDateTime 对象，并设置时间为 00:00:00
		LocalDateTime startOfMonth = yearMonth.atDay(1).atStartOfDay();

		// 获取下个月的 YearMonth 对象
		YearMonth nextYearMonth = yearMonth.plusMonths(1);

		// 获取下个月第一天的 LocalDateTime 对象，并设置时间为 00:00:00
		LocalDateTime startOfNextMonth = nextYearMonth.atDay(1).atStartOfDay();

		// 定义日期时间格式化器，格式为 "yyyy-MM-dd HH:mm:ss"
		DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

		// 格式化日期时间为字符串
		String startDateStr = startOfMonth.format(formatter);
		String endDateStr = startOfNextMonth.format(formatter);

		// 返回包含格式化后日期时间字符串的数组
		return new String[]{startDateStr, endDateStr};
	}
}