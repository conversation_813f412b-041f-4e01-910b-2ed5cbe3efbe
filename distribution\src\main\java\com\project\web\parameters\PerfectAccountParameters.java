package com.project.web.parameters;

import com.alibaba.fastjson.JSONObject;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 账号完善参数
 */
@Data
@ApiModel("账号完善参数")
public class PerfectAccountParameters {

    @ApiModelProperty(name="address",value = "地址",required=true)
    private String address;

    @ApiModelProperty(name="phone",value = "电话号码",required=true)
    private String phone;

    @ApiModelProperty(name="name",value = "名字",required=true)
    private String name;

    @ApiModelProperty(value="工号",required=false)
    private String workNo;


    @Override
    public String toString() {
        return JSONObject.toJSONString(this);
    }
}
