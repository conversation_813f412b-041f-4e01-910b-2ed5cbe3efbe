package com.project.web;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.github.binarywang.wxpay.bean.order.WxPayAppOrderResult;
import com.github.binarywang.wxpay.bean.order.WxPayMpOrderResult;
import com.github.binarywang.wxpay.bean.order.WxPayNativeOrderResult;
import com.github.binarywang.wxpay.bean.request.WxPayUnifiedOrderRequest;
import com.github.binarywang.wxpay.constant.WxPayConstants;
import com.github.binarywang.wxpay.service.WxPayService;
import com.project.core.*;
import com.project.exception.ServiceException;
import com.project.model.*;
import com.project.service.*;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.project.util.IpUtils;
import com.project.web.parameters.OrderParam;
import com.project.web.parameters.WxPayAppOrderResultBo;
import com.project.web.parameters.WxPayMpOrderResultBo;
import com.project.web.parameters.WxPayNativeOrderResultBo;
import org.springframework.web.bind.annotation.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import tk.mybatis.mapper.entity.Condition;
import tk.mybatis.mapper.entity.Example.Criteria;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
* Created by CodeGenerator on 2025/04/25.
*/
@Api(tags = "订单管理")
@RestController
@RequestMapping("/order")
public class OrderController {

	private static Logger log = LoggerFactory.getLogger(OrderController.class);

    @Resource
    private OrderService orderService;

	@Resource
	private AddressService addressService;

	@Resource
	private AreaService areaService;

	@Resource
	private GoodsService goodsService;

	@Resource
	private UserUtil userUtil;

	@Resource
	private WxPayService wxPayService;

	@Resource
	private ProjectService projectService;

	@Resource
	private ProjectAmountService projectAmountService;
	@Resource
	private UserService userService;

	@Resource
	private Parameters parameters;

    @PostMapping("/createOrder")
	@ApiOperation(value = "创建订单 返回订单状态 1待支付 2已支付",httpMethod = "POST")
    public Result createOrder(@RequestBody OrderParam param, HttpServletRequest request) {

		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
    	if(param == null){
    		return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
    	}
		Goods goods = goodsService.findById(param.getGoodsId());
		if (goods == null){
			return ResultGenerator.genFailResult(ResultCode.GOODS_IS_NULL);
		}
		User u = userService.findById(tokenUser.getId());
		if (u  == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		if (StrUtil.equals(u.getStatus() , "1")){
			return ResultGenerator.genFailResult(ResultCode.LOGIN_USER_STATUS_IS_ERROR);
		}
		if (param.getScoreType() == 2){
			if (goods.getScore() != null && u.getAmount().compareTo(goods.getScore()) <0){
				return ResultGenerator.genFailResult(ResultCode.AMOUNT_IS_NOT_INSUFFICIENT);
			}
		}
    	try {

			if (StrUtil.isNotBlank(goods.getProjectIds())){
				List<Project> projects = projectService.findByIds(goods.getProjectIds());
				if (CollUtil.isNotEmpty(projects)){
					for (Project p :projects){
						if (p.getType() == 2){
							ProjectAmount pa = projectAmountService.findPa(u.getId(),p.getId());
							if (pa.getActivityAmount().compareTo(pa.getActivityLimit())<0){
								Condition c2 = new Condition(Order.class);
								c2.createCriteria().andEqualTo("userId",tokenUser.getId())
										.andEqualTo("type",1)
										.andEqualTo("goodsId",param.getGoodsId())
										.andEqualTo("status",2);
								if (orderService.countByCondition(c2) > 0){
									return ResultGenerator.genFailResult(ResultCode.ORDER_IS_HAVE);
								}
							}
						}
					}
				}
			}
			Snowflake snowflake = IdUtil.createSnowflake(1, 1);

			String orderNo = String.valueOf(snowflake.nextId());
			BigDecimal amount = goods.getAmount();
			if (param.getScoreType() == 2){
				Boolean deal = userService.deal(tokenUser.getId(), 4, (byte) 1, goods.getScore().negate(), null);
				//抵扣成功
				if (deal){
					amount = goods.getAmount().subtract(goods.getScoreAmount());
				}
			}
			Order order= orderService.createOrder(param,tokenUser,goods,orderNo,amount);
			if (order.getStatus() == 1){
				if (param.getCoinType() == 1){
					String appId = param.getWxAppId();
					String tradeType = WxPayConstants.TradeType.JSAPI;
					if (null == param.getPayModel()) {
						// 默认JSAPI
						param.setPayModel(4);
					}
					if (1 == param.getPayModel()) {
						// APP
						tradeType = WxPayConstants.TradeType.APP;
					} else if (2 == param.getPayModel()) {
						tradeType = WxPayConstants.TradeType.NATIVE;
					}
					// 商户订单号
					WxPayUnifiedOrderRequest orderRequest = WxPayUnifiedOrderRequest.newBuilder()
							.body(goods.getName())
							.outTradeNo(orderNo)
							.totalFee(amount.multiply(BigDecimal.valueOf(100)).intValue())
							.spbillCreateIp(IpUtils.getRandomIp())
							.notifyUrl(parameters.getNotifyUrl() + "/order/wxBack")
							.tradeType(tradeType)
							.build();


					WxPayMpOrderResultBo wxPayMpOrderResultBo = new WxPayMpOrderResultBo();
					WxPayAppOrderResultBo wxPayAppOrderResultBo = new WxPayAppOrderResultBo();
					WxPayNativeOrderResultBo wxPayNativeOrderResultBo = new WxPayNativeOrderResultBo();
					if (1 == param.getPayModel()) {
						// APP
						WxPayAppOrderResult orderResult = wxPayService.createOrder(orderRequest);
						BeanUtil.copyProperties(orderResult, wxPayAppOrderResultBo);
					} else if (4 == param.getPayModel()) {
//						if (tokenUser.getOutAccount() != null) {
//							orderRequest.setOpenid(tokenUser.getOutAccount());
//						}
//						// 公众号  JSAPI
//						WxPayMpOrderResult orderResult = wxPayService.createOrder(orderRequest);
//						BeanUtil.copyProperties(orderResult, wxPayMpOrderResultBo);
					} else if (2 == param.getPayModel()) {
						// Native 扫码支付
						orderRequest.setProductId(orderNo);
						WxPayNativeOrderResult orderResult = wxPayService.createOrder(orderRequest);
						BeanUtil.copyProperties(orderResult, wxPayNativeOrderResultBo);
					}


					if (1 == param.getPayModel()) {
						wxPayAppOrderResultBo.setOrderId(order.getId());
						wxPayAppOrderResultBo.setOrderSn(orderNo);
						return ResultGenerator.genSuccessResult(wxPayAppOrderResultBo);
					}
					if (2 == param.getPayModel()) {
						wxPayNativeOrderResultBo.setOrderId(order.getId());
						wxPayNativeOrderResultBo.setOrderSn(orderNo);
						return ResultGenerator.genSuccessResult(wxPayNativeOrderResultBo);
					}
					wxPayMpOrderResultBo.setOrderId(order.getId());
					return ResultGenerator.genSuccessResult(wxPayMpOrderResultBo);
				}
			}
		} catch (Exception e) {
			log.error("新增对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success(1);
    }

    @RequestMapping("/deliver")
	@ApiOperation(value = "发货",httpMethod = "GET")
    public Result deliver(@RequestParam Integer id,@RequestParam String deliverNo) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}
    	try {
			Order order = orderService.findById(id);
			if (order == null){
				return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
			}
			if (order.getStatus() != 2){
				return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
			}
			if (order.getDeliverStatus() == 2){
				return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
			}
			orderService.update(Order.builder().id(id).deliverStatus(2).deliverNo(deliverNo).build());

		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success();
    }

	@RequestMapping("/refund")
	@ApiOperation(value = "退款",httpMethod = "GET")
	public Result refund(@RequestParam Integer id) {
		if(id == null){
			return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
		}
		try {
			Order order = orderService.findById(id);
			if (order == null){
				return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
			}
			if (order.getStatus() != 2){
				return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
			}
			orderService.update(Order.builder().id(id).status(3).build());
			Goods goods = goodsService.findById(order.getGoodsId());
			if (goods != null){
				List<Project> projects = projectService.findByIds(goods.getProjectIds());
				if (CollUtil.isNotEmpty(projects)){
					for (Project project:projects){
						if (project.getType() == 2){
							//减少上限
							projectAmountService.deal(order.getUserId(),project.getId(),5,(byte)3,
									order.getAmount().multiply(BigDecimal.valueOf(2)).negate(),Long.valueOf(order.getId()));

						}
					}
				}

			}
		} catch (Exception e) {
			log.error("删除对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success();
	}

	@RequestMapping("/pay")
	@ApiOperation(value = "积分支付",httpMethod = "GET")
	public Result<Integer> pay(@RequestParam Integer id) {
		return Result.success(orderService.pay(id));
	}

	@RequestMapping("/paySuccess")
	@ApiOperation(value = "线下支付成功",httpMethod = "GET")
	public Result paySuccess(@RequestParam Integer id) {
		try {
			Order order = orderService.findById(id);
			if (order == null){
				return ResultGenerator.genFailResult(ResultCode.OBJECT_IS_NULL);
			}
			if (order.getStatus() != 1){
				return ResultGenerator.genFailResult(ResultCode.STATUS_IS_ERROR);
			}
			orderService.paySuccess(id);
		} catch (Exception e){
			log.error("支付成功操作异常e:{}",e);
			if (e instanceof ServiceException){
				return ResultGenerator.genFailResult(((ServiceException) e).getCode());
			}
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success();
	}



    @RequestMapping("/detail")
	@ApiOperation(value = "order获取详情",httpMethod = "GET")
    public Result<Order> detail(@RequestParam Integer id) {
    	if(id == null){
    		return ResultGenerator.genFailResult(ResultCode.ID_IS_NULL);
    	}

    	Order order = null;
    	try {
    		order = orderService.findById(id);
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        
        return Result.success(order);
    }

    @RequestMapping("/list")
	@ApiOperation(value = "order获取列表",httpMethod = "POST")
    public Result<List<Order>> list(@RequestBody Order order, HttpServletRequest request) {

		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		PageHelper.startPage(order.getPage(), order.getSize());
		Condition condition = new Condition(order.getClass());
		Criteria criteria = condition.createCriteria();
        criteria.andEqualTo("userId", tokenUser.getId());
		if (order.getStatus() != null){
			criteria.andEqualTo("status", order.getStatus());
		}
		condition.setOrderByClause("create_time desc");
		PageInfo pageInfo = null;
		try {
    		 List<Order> list = orderService.findByCondition(condition);
    		 pageInfo = new PageInfo(list);
			 pageInfo.setList(list.stream().map( e -> {
				 if (e.getAddressId() != null){
					 Address address = addressService.findById(e.getAddressId());
					 if (address!=null){
						 e.setAreaName(areaService.getAreaName(address.getAreaCode()));
					 }
				 }
				 if (e.getGoodsId() != null){
					 Goods goods = goodsService.findById(e.getGoodsId());
					 if (goods != null){
						 e.setMemo(goods.getMemo());
					 }
				 }
				 return e;
			 }).collect(Collectors.toList()));
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
        return Result.success(pageInfo);
    }


	@RequestMapping("/list2")
	@ApiOperation(value = "order获取列表",httpMethod = "POST")
	public Result<List<Order>> list2(@RequestBody Order order, HttpServletRequest request) {

		User tokenUser = userUtil.getTokenUser(request);
		if (tokenUser == null){
			return ResultGenerator.genFailResult(ResultCode.USER_IS_NULL);
		}
		PageHelper.startPage(order.getPage(), order.getSize());
		Condition condition = new Condition(order.getClass());
		Criteria criteria = condition.createCriteria();
		if (order.getUserId() != null ){
			criteria.andEqualTo("userId", order.getUserId() );
		}
		if (order.getStatus() != null){
			criteria.andEqualTo("status", order.getStatus());
		}
		condition.setOrderByClause("create_time desc");
		PageInfo pageInfo = null;
		try {
			List<Order> list = orderService.findByCondition(condition);
			pageInfo = new PageInfo(list);
			pageInfo.setList(list.stream().map( e -> {
				if (e.getAddressId() != null){
					Address address = addressService.findById(e.getAddressId());
					if (address!=null){
						e.setAreaName(areaService.getAreaName(address.getAreaCode()));
					}
				}
				return e;
			}).collect(Collectors.toList()));
		} catch (Exception e) {
			log.error("查询对象操作异常e:{}",e);
			return ResultGenerator.genFailResult(ResultCode.INTERNAL_SERVER_ERROR);
		}
		return Result.success(pageInfo);
	}
}
