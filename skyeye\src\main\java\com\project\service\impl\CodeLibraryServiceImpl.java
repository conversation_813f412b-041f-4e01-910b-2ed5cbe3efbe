package com.project.service.impl;

import com.project.dao.CodeLibraryMapper;
import com.project.model.CodeLibrary;
import com.project.service.CodeLibraryService;
import com.project.core.AbstractService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;


/**
 * Created by CodeGenerator on 2025/01/26.
 */
@Service
@Transactional
public class CodeLibraryServiceImpl extends AbstractService<CodeLibrary> implements CodeLibraryService {
    @Resource
    private CodeLibraryMapper bCodeLibraryMapper;

}
