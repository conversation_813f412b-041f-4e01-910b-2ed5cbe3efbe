package com.project.model;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;

@ApiModel(value="com.project.model.NoticeUser")
@Table(name = "b_notice_user")
public class NoticeUser implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 图片
     */
    @ApiModelProperty(value="icon图片")
    private String icon;

    /**
     * title
     */
    @ApiModelProperty(value="nametitle")
    private String name;

    /**
     * 状态 1通知 2公告
     */
    @Column(name = "notice_id")
    @ApiModelProperty(value="noticeId状态 1通知 2公告")
    private Integer noticeId;

    /**
     * 状态 1正常 2禁用
     */
    @Column(name = "user_id")
    @ApiModelProperty(value="userId状态 1正常 2禁用")
    private Integer userId;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取图片
     *
     * @return icon - 图片
     */
    public String getIcon() {
        return icon;
    }

    /**
     * 设置图片
     *
     * @param icon 图片
     */
    public void setIcon(String icon) {
        this.icon = icon;
    }

    /**
     * 获取title
     *
     * @return name - title
     */
    public String getName() {
        return name;
    }

    /**
     * 设置title
     *
     * @param name title
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取状态 1通知 2公告
     *
     * @return notice_id - 状态 1通知 2公告
     */
    public Integer getNoticeId() {
        return noticeId;
    }

    /**
     * 设置状态 1通知 2公告
     *
     * @param noticeId 状态 1通知 2公告
     */
    public void setNoticeId(Integer noticeId) {
        this.noticeId = noticeId;
    }

    /**
     * 获取状态 1正常 2禁用
     *
     * @return user_id - 状态 1正常 2禁用
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * 设置状态 1正常 2禁用
     *
     * @param userId 状态 1正常 2禁用
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }
}