package com.project.model;

import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import javax.persistence.*;
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value="com.project.model.Complain")
@Table(name = "b_complain")
public class Complain extends BaseBeen implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 住户id
     */
    @Column(name = "user_id")
    @ApiModelProperty(value="userId住户id")
    private Integer userId;

    /**
     * 住户地址
     */
    @ApiModelProperty(value="address住户地址")
    private String address;


    @ApiModelProperty(value="图片  1,2")
    private String pics;


    /**
     * 状态 1 投诉 2建议
     */
    @ApiModelProperty(value="type状态 1 投诉 2建议")
    private Integer type;

    /**
     * 确认用户id
     */
    @Column(name = "notarize_user_id")
    @ApiModelProperty(value="notarizeUserId确认用户id")
    private Integer notarizeUserId;

    /**
     * 状态 1 待确认 2 已生成任务 3已确认
     */
    @ApiModelProperty(value="status状态 1 待确认 2 已生成任务 3已确认 4取消")
    private Integer status;

    /**
     * 投诉建议内容
     */
    @ApiModelProperty(value="content投诉建议内容")
    private String content;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取住户id
     *
     * @return user_id - 住户id
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * 设置住户id
     *
     * @param userId 住户id
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取住户地址
     *
     * @return address - 住户地址
     */
    public String getAddress() {
        return address;
    }

    /**
     * 设置住户地址
     *
     * @param address 住户地址
     */
    public void setAddress(String address) {
        this.address = address;
    }

    /**
     * 获取状态 1 投诉 2建议
     *
     * @return type - 状态 1 投诉 2建议
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置状态 1 投诉 2建议
     *
     * @param type 状态 1 投诉 2建议
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取确认用户id
     *
     * @return notarize_user_id - 确认用户id
     */
    public Integer getNotarizeUserId() {
        return notarizeUserId;
    }

    /**
     * 设置确认用户id
     *
     * @param notarizeUserId 确认用户id
     */
    public void setNotarizeUserId(Integer notarizeUserId) {
        this.notarizeUserId = notarizeUserId;
    }

    /**
     * 获取状态 1 待确认 2 已生成任务 3已确认  4取消
     *
     * @return status - 状态 1 待确认 2 已生成任务 3已确认 4取消
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 1 待确认 2 已生成任务 3已确认
     *
     * @param status 状态 1 待确认 2 已生成任务 3已确认
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取投诉建议内容
     *
     * @return content - 投诉建议内容
     */
    public String getContent() {
        return content;
    }

    /**
     * 设置投诉建议内容
     *
     * @param content 投诉建议内容
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }
}