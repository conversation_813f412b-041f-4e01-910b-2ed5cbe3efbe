<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.NoticeMapper">
  <resultMap id="BaseResultMap" type="com.project.model.Notice">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="img" jdbcType="VARCHAR" property="img" />
    <result column="title" jdbcType="VARCHAR" property="title" />

    <result column="lose_time" jdbcType="TIMESTAMP" property="loseTime" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>