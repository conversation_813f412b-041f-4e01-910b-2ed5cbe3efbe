<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.ProjectMapper">
  <resultMap id="BaseResultMap" type="com.project.model.Project">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="company_id" jdbcType="INTEGER" property="companyId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="user_id" jdbcType="INTEGER" property="userId" />

    <result column="owner_phone" jdbcType="VARCHAR" property="ownerPhone" />
    <result column="create_user_id" jdbcType="INTEGER" property="createUserId" />
    <result column="update_user_id" jdbcType="INTEGER" property="updateUserId" />
    <result column="audit_user_id" jdbcType="INTEGER" property="auditUserId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="begin_time" jdbcType="TIMESTAMP" property="beginTime" />
    <result column="finish_time" jdbcType="TIMESTAMP" property="finishTime" />
    <result column="amount" jdbcType="DECIMAL" property="amount" />
    <result column="days" jdbcType="INTEGER" property="days" />
    <result column="address" jdbcType="VARCHAR" property="address" />
  </resultMap>
</mapper>