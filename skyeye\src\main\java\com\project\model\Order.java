package com.project.model;

import com.project.core.BaseBeen;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;


@ApiModel(value="com.project.model.Order")
@Table(name = "b_order")
public class Order extends BaseBeen implements Serializable {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @ApiModelProperty(value="id")
    private Integer id;

    /**
     * 名称
     */
    @ApiModelProperty(value="name名称")
    private String name;

    /**
     * 支付金额
     */
    @ApiModelProperty(value="amount支付金额")
    private BigDecimal amount;

    /**
     * 状态 1月会员 2永久会员
     */
    @ApiModelProperty(value="type状态 1月会员 2永久会员")
    private Integer type;

    /**
     * 状态 1已付款 2已结算
     */
    @ApiModelProperty(value="status状态 1已付款 2已结算")
    private Integer status;

    /**
     * 创建人
     */
    @Column(name = "create_user")
    @ApiModelProperty(value="createUser创建人")
    private String createUser;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    @ApiModelProperty(value="createTime创建时间")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    @ApiModelProperty(value="updateTime更新时间")
    private Date updateTime;

    /**
     * 用户id
     */
    @Column(name = "user_id")
    @ApiModelProperty(value="userId用户id")
    private Integer userId;

    /**
     * 团长id
     */
    @ApiModelProperty(value="pid团长id")
    private Integer pid;

    /**
     * VIP等级
     */
    @ApiModelProperty(value="levelVIP等级")
    private Integer level;

    /**
     * 几个月
     */
    @ApiModelProperty(value="month几个月")
    private Integer month;

    private static final long serialVersionUID = 1L;

    /**
     * @return id
     */
    public Integer getId() {
        return id;
    }

    /**
     * @param id
     */
    public void setId(Integer id) {
        this.id = id;
    }

    /**
     * 获取名称
     *
     * @return name - 名称
     */
    public String getName() {
        return name;
    }

    /**
     * 设置名称
     *
     * @param name 名称
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 获取支付金额
     *
     * @return amount - 支付金额
     */
    public BigDecimal getAmount() {
        return amount;
    }

    /**
     * 设置支付金额
     *
     * @param amount 支付金额
     */
    public void setAmount(BigDecimal amount) {
        this.amount = amount;
    }

    /**
     * 获取状态 1月会员 2永久会员
     *
     * @return type - 状态 1月会员 2永久会员
     */
    public Integer getType() {
        return type;
    }

    /**
     * 设置状态 1月会员 2永久会员
     *
     * @param type 状态 1月会员 2永久会员
     */
    public void setType(Integer type) {
        this.type = type;
    }

    /**
     * 获取状态 1已付款 2已结算
     *
     * @return status - 状态 1已付款 2已结算
     */
    public Integer getStatus() {
        return status;
    }

    /**
     * 设置状态 1已付款 2已结算
     *
     * @param status 状态 1已付款 2已结算
     */
    public void setStatus(Integer status) {
        this.status = status;
    }

    /**
     * 获取创建人
     *
     * @return create_user - 创建人
     */
    public String getCreateUser() {
        return createUser;
    }

    /**
     * 设置创建人
     *
     * @param createUser 创建人
     */
    public void setCreateUser(String createUser) {
        this.createUser = createUser;
    }

    /**
     * 获取创建时间
     *
     * @return create_time - 创建时间
     */
    public Date getCreateTime() {
        return createTime;
    }

    /**
     * 设置创建时间
     *
     * @param createTime 创建时间
     */
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    /**
     * 获取更新时间
     *
     * @return update_time - 更新时间
     */
    public Date getUpdateTime() {
        return updateTime;
    }

    /**
     * 设置更新时间
     *
     * @param updateTime 更新时间
     */
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    /**
     * 获取用户id
     *
     * @return user_id - 用户id
     */
    public Integer getUserId() {
        return userId;
    }

    /**
     * 设置用户id
     *
     * @param userId 用户id
     */
    public void setUserId(Integer userId) {
        this.userId = userId;
    }

    /**
     * 获取团长id
     *
     * @return pid - 团长id
     */
    public Integer getPid() {
        return pid;
    }

    /**
     * 设置团长id
     *
     * @param pid 团长id
     */
    public void setPid(Integer pid) {
        this.pid = pid;
    }

    /**
     * 获取VIP等级
     *
     * @return level - VIP等级
     */
    public Integer getLevel() {
        return level;
    }

    /**
     * 设置VIP等级
     *
     * @param level VIP等级
     */
    public void setLevel(Integer level) {
        this.level = level;
    }

    /**
     * 获取几个月
     *
     * @return month - 几个月
     */
    public Integer getMonth() {
        return month;
    }

    /**
     * 设置几个月
     *
     * @param month 几个月
     */
    public void setMonth(Integer month) {
        this.month = month;
    }
}