package com.project.service.impl;

import cn.hutool.json.JSONUtil;
import com.project.service.SseService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
public class SseServiceImpl implements SseService {

    private static Map<String, SseEmitter> sseCache = new ConcurrentHashMap<>();

//    @Autowired
//    private AmqpTemplate amqpTemplate;

    @Override
    public SseEmitter subscribe(String id) {
        try{
            // 超时时间设置为1小时
            SseEmitter sseEmitter = new SseEmitter(3600_000L);
            sseCache.put(id, sseEmitter);
            sseEmitter.onTimeout(() -> sseCache.remove(id));
            sseEmitter.onCompletion(() -> System.out.println("完成！！！"));
            return sseEmitter;
        }catch (Exception e){
            log.error("sse注册失败");
        }
        return null;
    }

    @Override
    public String push(String id, String msg) {
        try {
            SseEmitter sseEmitter = sseCache.get(id);
            if (sseEmitter != null) {
                sseEmitter.send(msg);
            }
        }catch (Exception e){
            log.error("发送sse消息异常");
        }
        return "success";
    }

    @Override
    public String mq(String id, String msg) throws Exception{
//        SseParam sseParam = SseParam.builder().sseId(id).msg(msg).build();
//        amqpTemplate.convertAndSend("fanoutSse","", JSONUtil.toJsonStr(sseParam));
        return "success";
    }

    @Override
    public String over(String id) {
        SseEmitter sseEmitter = sseCache.get(id);
        if (sseEmitter != null) {
            sseEmitter.complete();
            sseCache.remove(id);
        }
        return "success";
    }
}

