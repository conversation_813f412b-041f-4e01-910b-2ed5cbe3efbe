<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.project.dao.CompanyMapper">
  <resultMap id="BaseResultMap" type="com.project.model.Company">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="type" jdbcType="INTEGER" property="type" />
    <result column="pic" jdbcType="VARCHAR" property="pic" />

    <result column="principal" jdbcType="VARCHAR" property="principal" />
    <result column="principal_phone" jdbcType="VARCHAR" property="principalPhone" />
    <result column="principal_wechat" jdbcType="VARCHAR" property="principalWechat" />
    <result column="principal_qrcode" jdbcType="VARCHAR" property="principalQrcode" />


    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="content" jdbcType="VARCHAR" property="content" />
    <result column="vip_time" jdbcType="TIMESTAMP" property="vipTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
</mapper>